#!/bin/bash

echo "=== 快速修复uploads静态文件服务 ==="

# 方法1: 使用sed在现有配置中插入uploads配置
echo "1. 正在修复Nginx配置..."
ssh root@************** '
# 备份当前配置
cp /www/server/panel/vhost/nginx/**************.conf /www/server/panel/vhost/nginx/**************.conf.backup.$(date +%Y%m%d_%H%M%S)

# 检查是否已存在uploads配置
if grep -q "location /uploads/" /www/server/panel/vhost/nginx/**************.conf; then
    echo "❌ uploads配置已存在，跳过添加"
else
    echo "➕ 添加uploads静态文件配置..."
    
    # 在"proxy_request_buffering off;"行后插入uploads配置
    sed -i "/proxy_request_buffering off;/a\\
    }\\
\\
    # 静态文件服务 - 处理上传的图片和文件\\
    location /uploads/ {\\
        alias /www/wwwroot/server/uploads/;\\
        expires 30d;\\
        add_header Cache-Control \"public, immutable\";\\
        \\
        # 安全设置 - 只允许特定文件类型\\
        location ~* \\.(jpg|jpeg|png|gif|bmp|webp|svg|ico|pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar)$ {\\
            expires 30d;\\
            add_header Cache-Control \"public, immutable\";\\
        }\\
        \\
        # 禁止执行脚本\\
        location ~* \\.(php|php5|php7|phtml|php3|asp|aspx|jsp|pl|py|cgi|sh)$ {\\
            deny all;\\
        }\\
    }" /www/server/panel/vhost/nginx/**************.conf
    
    echo "✅ uploads配置已添加"
fi
'

echo "2. 创建并设置上传目录权限..."
ssh root@************** '
cd /www/wwwroot/server
mkdir -p uploads/images uploads/documents
chmod 755 uploads uploads/images uploads/documents
chown -R www:www uploads
echo "📁 上传目录结构："
ls -la uploads/
'

echo "3. 测试并重载Nginx配置..."
ssh root@************** '
echo "🔧 测试Nginx配置..."
if nginx -t; then
    echo "✅ Nginx配置测试通过"
    nginx -s reload
    echo "🔄 Nginx配置已重新加载"
else
    echo "❌ Nginx配置测试失败，请检查配置文件"
    exit 1
fi
'

echo "4. 测试静态文件访问..."
ssh root@************** '
cd /www/wwwroot/server
echo "test image access" > uploads/test.txt
echo "🌐 测试访问：http://**************/uploads/test.txt"
curl -I http://127.0.0.1/uploads/test.txt 2>/dev/null | head -1
rm -f uploads/test.txt
'

echo ""
echo "=== 修复完成 ==="
echo "✅ 现在可以重新测试图片上传和显示功能"
echo "🔗 静态文件访问地址：http://**************/uploads/" 
#!/bin/bash

echo "=== 诊断uploads图片404问题 ==="

ssh root@************** '
echo "📋 1. 检查当前nginx配置是否包含uploads配置..."
echo "查找location /uploads/配置："
grep -A 10 "location /uploads/" /www/server/panel/vhost/nginx/**************.conf || echo "❌ 未找到uploads配置"

echo ""
echo "📁 2. 检查uploads目录结构..."
echo "服务器uploads目录："
ls -la /www/wwwroot/server/uploads/ 2>/dev/null || echo "❌ uploads目录不存在"
echo "images子目录："
ls -la /www/wwwroot/server/uploads/images/ 2>/dev/null || echo "❌ images目录不存在"

echo ""
echo "🔍 3. 检查具体的图片文件..."
echo "查找最新上传的图片："
find /www/wwwroot/server/uploads/ -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | head -5

echo ""
echo "🧪 4. 测试nginx配置语法..."
nginx -t

echo ""
echo "🌐 5. 测试uploads路径访问..."
# 创建测试文件
echo "test image content" > /www/wwwroot/server/uploads/test.txt
chmod 644 /www/wwwroot/server/uploads/test.txt

# 测试访问
echo "测试静态文件访问："
curl -I http://127.0.0.1/uploads/test.txt 2>/dev/null || echo "❌ uploads路径无法访问"

echo ""
echo "📊 6. 检查nginx进程和端口..."
ps aux | grep nginx | grep -v grep
netstat -tlnp | grep :80

echo ""
echo "📝 7. 检查nginx错误日志（最近10行）..."
tail -10 /www/wwwlogs/**************.error.log 2>/dev/null || echo "无错误日志"

# 清理测试文件
rm -f /www/wwwroot/server/uploads/test.txt
'

echo ""
echo "=== 诊断完成 ===" 
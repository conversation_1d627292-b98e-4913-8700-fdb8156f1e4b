#!/bin/bash

echo "=== 修复生产环境配置 ==="

# 上传生产环境配置文件到服务器
echo "上传生产环境配置文件..."
scp server/.env.production root@47.122.122.245:/www/wwwroot/server/.env

# 在服务器上重启PM2应用
echo "重启PM2应用..."
ssh root@47.122.122.245 "cd /www/wwwroot/server && pm2 restart all --update-env"

# 检查配置是否生效
echo "检查配置是否生效..."
ssh root@47.122.122.245 "cd /www/wwwroot/server && pm2 logs workyy --lines 15 | grep -E '(NODE_ENV|FEISHU_REDIRECT_URI|飞书配置加载)'"

echo "=== 修复完成 ===" 
# 飞书权限申请清单

## 应用信息
- **应用ID**: `cli_a66b3b2dcab8d013`
- **应用名称**: ChattyWork工作流系统

## 当前权限状态

### ✅ 已申请的权限
```
contact:user.base:readonly          # 获取用户基本信息
contact:user.email:readonly         # 获取用户邮箱信息  
contact:user.department:readonly    # 获取用户组织架构信息
contact:user.employee:readonly      # 获取用户受雇信息
contact:department.base:readonly    # 获取部门基础信息
contact:department.organize:readonly # 获取部门组织架构信息
```

### ❌ 缺失的权限（需要申请）
```
contact:user.phone:readonly         # 获取用户手机号
```

### ⚠️ 不需要的权限（可移除）
```
contact:user.employee_id:readonly   # 获取用户employee_id（代码中未使用）
```

## 立即需要申请的权限

### `contact:user.phone:readonly`
- **用途**: 获取用户手机号码
- **当前状态**: ❌ 缺失
- **影响**: 无法获取用户手机号，用户信息不完整
- **优先级**: 🔴 高
- **证据**: API返回`mobile_visible: true`但没有`mobile`字段

## 申请流程

1. 登录飞书开放平台: https://open.feishu.cn/
2. 找到应用: App ID `cli_a66b3b2dcab8d013`
3. 进入"权限管理"
4. 申请权限: `contact:user.phone:readonly`
5. 提交版本发布申请
6. 等待审核通过

## 验证方法

权限申请成功后，重新测试飞书登录，检查日志中是否出现：
```
飞书服务: ✅ 成功获取用户手机号: 138xxxxxxxx
```

而不是：
```
- 手机号: 未提供
``` 
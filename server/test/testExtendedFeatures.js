/**
 * 扩展功能测试脚本
 * 测试月度报告、节假日识别、系统维护通知和错误告警推送功能
 */

require('dotenv').config();

// 测试节假日识别功能
async function testHolidayService() {
  console.log('\n🎯 测试节假日识别功能...');
  
  try {
    const holidayService = require('../services/holidayService');
    
    // 测试一些特定日期
    const testDates = [
      '2024-01-01', // 元旦
      '2024-02-10', // 春节
      '2024-04-04', // 清明节
      '2024-05-01', // 劳动节
      '2024-10-01', // 国庆节
      '2024-02-04', // 调休工作日
      '2024-12-25', // 普通工作日
      '2024-12-28', // 周六
      '2024-12-29'  // 周日
    ];
    
    console.log('📅 测试各种日期的状态:');
    testDates.forEach(date => {
      const status = holidayService.getDateStatus(date);
      console.log(`${date} (${status.weekday}): ${status.description}`);
      
      if (status.isHoliday) {
        console.log(`  🎉 节假日: ${status.holidayName}`);
      }
      
      if (status.isWorkingWeekend) {
        console.log(`  💼 调休工作日`);
      }
    });
    
    // 测试工作日计算
    console.log('\n📊 工作日计算测试:');
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;
    
    const workingDays = holidayService.getWorkingDaysInMonth(currentYear, currentMonth);
    console.log(`${currentYear}年${currentMonth}月工作日数量: ${workingDays}天`);
    
    // 测试日期范围
    const rangeStart = '2024-10-01';
    const rangeEnd = '2024-10-07';
    const workingDaysInRange = holidayService.getWorkingDaysInRange(rangeStart, rangeEnd);
    console.log(`${rangeStart} 到 ${rangeEnd} 工作日数量: ${workingDaysInRange}天`);
    
    console.log('✅ 节假日识别功能测试完成');
    
  } catch (error) {
    console.error('❌ 节假日识别功能测试失败:', error);
  }
}

// 测试月度报告功能
async function testMonthlyReport() {
  console.log('\n🎯 测试月度报告功能...');
  
  try {
    const scheduledReportService = require('../services/scheduledReportService');
    
    console.log('📊 生成月度报告...');
    await scheduledReportService.triggerMonthlyReport();
    
    console.log('✅ 月度报告功能测试完成');
    
  } catch (error) {
    console.error('❌ 月度报告功能测试失败:', error);
  }
}

// 测试系统维护通知功能
async function testMaintenanceNotification() {
  console.log('\n🎯 测试系统维护通知功能...');
  
  try {
    const systemEventService = require('../services/systemEventService');
    
    // 测试计划维护通知
    console.log('🔧 测试计划维护通知...');
    await systemEventService.sendMaintenanceNotification({
      type: 'scheduled',
      title: '光年小卖部定期维护',
      startTime: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后
      endTime: new Date(Date.now() + 90 * 60 * 1000), // 90分钟后
      reason: '系统定期维护，优化性能和安全性',
      impact: [
        '商品兑换功能暂时不可用',
        '用户查看功能正常',
        '数据不会丢失'
      ],
      preparations: [
        '请提前完成重要操作',
        '维护期间请勿频繁刷新'
      ],
      contactInfo: '系统管理员'
    });
    
    // 测试紧急维护通知
    console.log('🚨 测试紧急维护通知...');
    await systemEventService.sendMaintenanceNotification({
      type: 'emergency',
      title: '紧急系统修复',
      startTime: new Date(),
      endTime: new Date(Date.now() + 60 * 60 * 1000), // 1小时后
      reason: '修复关键安全漏洞',
      impact: [
        '系统暂时不可用',
        '所有功能受影响'
      ],
      preparations: [
        '请立即保存当前工作',
        '系统将在修复后恢复'
      ],
      contactInfo: '紧急技术支持'
    });
    
    console.log('✅ 系统维护通知功能测试完成');
    
  } catch (error) {
    console.error('❌ 系统维护通知功能测试失败:', error);
  }
}

// 测试错误告警推送功能
async function testErrorAlert() {
  console.log('\n🎯 测试错误告警推送功能...');
  
  try {
    const systemEventService = require('../services/systemEventService');
    
    // 测试不同严重程度的错误告警
    const testAlerts = [
      {
        severity: 'critical',
        errorType: '数据库服务宕机',
        errorMessage: '主数据库连接完全失效，无法处理任何请求',
        systemComponent: '数据库服务',
        stackTrace: 'Error: ECONNREFUSED\n    at Connection.connect',
        resolution: '立即重启数据库服务',
        affectedUsers: '所有用户'
      },
      {
        severity: 'high',
        errorType: 'API响应超时',
        errorMessage: '用户认证接口响应时间超过30秒',
        systemComponent: '认证服务',
        stackTrace: 'TimeoutError: Request timeout after 30000ms',
        resolution: '检查认证服务性能，考虑扩容',
        affectedUsers: '登录用户'
      },
      {
        severity: 'medium',
        errorType: '缓存连接失败',
        errorMessage: 'Redis连接池连接数不足',
        systemComponent: 'Redis缓存',
        stackTrace: 'Error: Redis connection pool exhausted',
        resolution: '增加Redis连接池大小',
        affectedUsers: '部分用户'
      },
      {
        severity: 'low',
        errorType: '文件上传慢',
        errorMessage: '图片上传平均耗时超过5秒',
        systemComponent: '文件服务',
        stackTrace: 'Warning: Upload performance degraded',
        resolution: '优化文件上传策略',
        affectedUsers: '上传用户'
      }
    ];
    
    for (const alert of testAlerts) {
      console.log(`🚨 测试${alert.severity}级别错误告警: ${alert.errorType}`);
      await systemEventService.sendErrorAlert(alert);
      
      // 等待一秒避免发送太频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 测试预定义错误方法
    console.log('🔧 测试预定义错误告警方法...');
    
    const mockError = new Error('测试数据库连接错误');
    mockError.stack = 'Error: 测试数据库连接错误\n    at testFunction (test.js:123:45)';
    
    await systemEventService.sendDatabaseError(mockError);
    await systemEventService.sendAPIError(mockError, '/api/users');
    await systemEventService.sendFeishuServiceError(mockError);
    await systemEventService.sendFileUploadError(mockError, 'test-image.jpg');
    
    console.log('✅ 错误告警推送功能测试完成');
    
  } catch (error) {
    console.error('❌ 错误告警推送功能测试失败:', error);
  }
}

// 测试服务状态和统计功能
async function testServiceStatus() {
  console.log('\n🎯 测试服务状态和统计功能...');
  
  try {
    const scheduledReportService = require('../services/scheduledReportService');
    const systemEventService = require('../services/systemEventService');
    
    // 测试定时报告服务状态
    console.log('📊 定时报告服务状态:');
    const reportStatus = scheduledReportService.getStatus();
    console.log(JSON.stringify(reportStatus, null, 2));
    
    // 测试系统事件服务统计
    console.log('🔍 错误统计信息:');
    const errorStats = systemEventService.getErrorStatistics();
    console.log(JSON.stringify(errorStats, null, 2));
    
    // 测试维护计划
    console.log('🔧 维护计划:');
    const maintenanceSchedule = systemEventService.getMaintenanceSchedule();
    console.log(JSON.stringify(maintenanceSchedule, null, 2));
    
    console.log('✅ 服务状态和统计功能测试完成');
    
  } catch (error) {
    console.error('❌ 服务状态和统计功能测试失败:', error);
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🧪 开始扩展功能测试...\n');
  
  try {
    await testHolidayService();
    await testMonthlyReport();
    await testMaintenanceNotification();
    await testErrorAlert();
    await testServiceStatus();
    
    console.log('\n🎉 所有扩展功能测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error);
  }
  
  // 退出进程
  process.exit(0);
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testHolidayService,
  testMonthlyReport,
  testMaintenanceNotification,
  testErrorAlert,
  testServiceStatus,
  runAllTests
}; 
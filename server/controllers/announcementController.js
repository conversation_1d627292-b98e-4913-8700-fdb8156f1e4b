const { Announcement, User } = require('../models');
const { Op } = require('sequelize');
const path = require('path');
const fs = require('fs');
const config = require('../config/config');
const { validationResult } = require('express-validator');

/**
 * 获取公告列表
 * 支持分页、排序、筛选等功能
 */
exports.getAnnouncements = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      type,
      status,
      search = '',
      sort = 'newest' 
    } = req.query;
    
    // 计算分页偏移量
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const where = {};
    
    // 根据类型筛选
    if (type) {
      where.type = type;
    }
    
    // 根据状态筛选
    if (status) {
      where.status = status;
    } else if (req.query.showAll !== 'true' && !req.admin) {
      // 非管理员默认只显示激活状态的公告，除非特别要求显示所有
      where.status = 'active';
    }
    
    // 搜索标题或内容
    if (search) {
      where[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { content: { [Op.like]: `%${search}%` } }
      ];
    }
    
    // 确定排序方式
    let order;
    switch (sort) {
      case 'oldest':
        order = [['createdAt', 'ASC']];
        break;
      case 'title-asc':
        order = [['title', 'ASC']];
        break;
      case 'title-desc':
        order = [['title', 'DESC']];
        break;
      case 'newest':
      default:
        order = [['createdAt', 'DESC']];
    }
    
    // 执行查询
    const { count, rows } = await Announcement.findAndCountAll({
      where,
      order,
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });
    
    // 转换状态值为前端格式
    const transformedRows = rows.map(row => {
      const data = row.toJSON();
      // 将 inactive 转换为 draft
      if (data.status === 'inactive') {
        data.status = 'draft';
      }
      
      // 确保contentHtml存在
      if (!data.contentHtml) {
        // 如果contentHtml不存在，使用content
        data.contentHtml = data.content;
      }
      
      // 处理图片URL
      if (data.imageUrl) {
        // 确保图片URL格式正确
        if (data.imageUrl.startsWith('native-resource://')) {
          data.imageUrl = data.imageUrl.replace('native-resource://', '/');
        }
      }
      
      // 处理多图片URL
      if (data.imageUrls && Array.isArray(data.imageUrls)) {
        // 确保每个图片URL格式正确
        data.imageUrls = data.imageUrls.map(url => {
          if (url && url.startsWith('native-resource://')) {
            return url.replace('native-resource://', '/');
          }
          return url;
        });
      } else if (data.imageUrl && (!data.imageUrls || !Array.isArray(data.imageUrls))) {
        // 如果没有多图但有单图，则初始化多图数组
        data.imageUrls = [data.imageUrl];
      }
      
      return data;
    });
    
    // 返回结果
    res.json({
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      data: transformedRows
    });
    
  } catch (error) {
    console.error('获取公告列表失败:', error);
    res.status(500).json({ message: '获取公告列表失败', error: error.message });
  }
};

/**
 * 获取单个公告详情
 */
exports.getAnnouncementById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const announcement = await Announcement.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });
    
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    // 转换状态为前端格式
    const responseData = announcement.toJSON();
    if (responseData.status === 'inactive') {
      responseData.status = 'draft';
    }
    
    // 确保contentHtml存在，如果不存在则回退到content
    if (!responseData.contentHtml) {
      responseData.contentHtml = responseData.content;
    } else {
      // 验证contentHtml是否为有效的JSON格式
      try {
        // 如果是JSON字符串但不是数组或对象格式，尝试解析检查
        if (typeof responseData.contentHtml === 'string' && 
            !(responseData.contentHtml.startsWith('[') || responseData.contentHtml.startsWith('{'))) {
          // 非JSON格式，保持原样
        }
        // 已经是正确的JSON格式，无需处理
      } catch (error) {
        console.error('处理contentHtml失败:', error);
        // 解析失败时回退到普通内容
        responseData.contentHtml = responseData.content;
      }
    }
    
    // 处理图片URL
    if (responseData.imageUrl) {
      // 确保图片URL格式正确
      if (responseData.imageUrl.startsWith('native-resource://')) {
        responseData.imageUrl = responseData.imageUrl.replace('native-resource://', '/');
      }
    }
    
    // 处理多图片URL
    if (responseData.imageUrls && Array.isArray(responseData.imageUrls)) {
      // 确保每个图片URL格式正确
      responseData.imageUrls = responseData.imageUrls.map(url => {
        if (url && url.startsWith('native-resource://')) {
          return url.replace('native-resource://', '/');
        }
        return url;
      });
    } else if (responseData.imageUrl && (!responseData.imageUrls || !Array.isArray(responseData.imageUrls))) {
      // 如果没有多图但有单图，则初始化多图数组
      responseData.imageUrls = [responseData.imageUrl];
    }
    
    res.json(responseData);
    
  } catch (error) {
    console.error('获取公告详情失败:', error);
    res.status(500).json({ message: '获取公告详情失败', error: error.message });
  }
};

/**
 * 创建公告
 */
exports.createAnnouncement = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { title, content, contentHtml, status, type, imageUrl, imageUrls } = req.body;
    
    // 验证必填字段
    if (!title || !content) {
      return res.status(400).json({ message: '标题和内容为必填项' });
    }
    
    // 验证状态值并转换
    let dbStatus = status;
    if (status === 'draft') {
      dbStatus = 'inactive';
    } else if (status === 'active') {
      dbStatus = 'active';
    } else {
      return res.status(400).json({ message: '状态值无效，必须为 draft 或 active' });
    }
    
    // 创建公告记录 - 构建基本数据对象
    const announcementData = {
      title,
      content,
      status: dbStatus,
      type: type || '系统更新',
      createdBy: req.user.id
    };
    
    // 如果提供了contentHtml，添加到数据对象
    if (contentHtml) {
      announcementData.contentHtml = contentHtml;
    }
    
    // 处理图片URL
    // 优先使用新的imageUrls字段（多图支持）
    if (imageUrls && Array.isArray(imageUrls)) {
      announcementData.imageUrls = imageUrls;
      // 兼容单图字段，将第一张图片设为主图
      if (imageUrls.length > 0) {
        announcementData.imageUrl = imageUrls[0];
      }
    } 
    // 兼容旧版本单图模式
    else if (imageUrl) {
      announcementData.imageUrl = imageUrl;
      // 同时将单图也设置到多图字段中
      announcementData.imageUrls = [imageUrl];
    }
    
    // 创建公告
    const announcement = await Announcement.create(announcementData);
    
    // 转换状态为前端格式
    const responseData = announcement.toJSON();
    responseData.status = responseData.status === 'inactive' ? 'draft' : 'active';
    
    res.status(201).json(responseData);
  } catch (error) {
    // 处理不同类型的错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: error.message });
    }
    
    console.error('创建公告错误:', error);
    res.status(500).json({ message: '创建公告失败', error: error.message });
  }
};

/**
 * 更新公告
 */
exports.updateAnnouncement = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { id } = req.params;
    const { title, content, contentHtml, status, type, imageUrl, imageUrls } = req.body;
    
    // 查找公告
    const announcement = await Announcement.findByPk(id);
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    // 验证状态值（如果提供）
    let dbStatus = status;
    if (status) {
      if (status === 'draft') {
        dbStatus = 'inactive';
      } else if (status === 'active') {
        dbStatus = 'active';
      } else {
        return res.status(400).json({ message: '状态值无效，必须为 draft 或 active' });
      }
    }
    
    // 更新公告数据对象
    const updateData = {
      title: title !== undefined ? title : announcement.title,
      content: content !== undefined ? content : announcement.content
    };
    
    // 如果提供了contentHtml，更新HTML内容
    if (contentHtml !== undefined) {
      updateData.contentHtml = contentHtml;
    }
    
    // 更新公告类型（如果提供）
    if (type !== undefined) {
      updateData.type = type;
    }
    
    // 处理图片URL
    // 优先处理多图片
    if (imageUrls !== undefined) {
      updateData.imageUrls = imageUrls && Array.isArray(imageUrls) ? imageUrls : null;
      
      // 同步更新单图字段（使用第一张图片）
      if (Array.isArray(imageUrls) && imageUrls.length > 0) {
        updateData.imageUrl = imageUrls[0];
      } else {
        updateData.imageUrl = null;
      }
    }
    // 如果没有提供多图但提供了单图
    else if (imageUrl !== undefined) {
      updateData.imageUrl = imageUrl;
      
      // 将单图同步到多图字段
      if (imageUrl) {
        updateData.imageUrls = [imageUrl];
      } else {
        updateData.imageUrls = null;
      }
    }
    
    // 只有在提供status时才更新状态
    if (status) {
      updateData.status = dbStatus;
    }
    
    // 更新公告
    await announcement.update(updateData);
    
    // 重新获取更新后的公告（包含关联数据）
    const updatedAnnouncement = await Announcement.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });
    
    // 转换状态返回给前端
    const responseData = updatedAnnouncement.toJSON();
    responseData.status = responseData.status === 'inactive' ? 'draft' : 'active';
    
    res.json(responseData);
  } catch (error) {
    console.error('更新公告错误:', error);
    res.status(500).json({ message: '更新公告失败', error: error.message });
  }
};

/**
 * 更新公告状态
 * 需要管理员权限
 */
exports.updateAnnouncementStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // 验证状态，兼容draft作为inactive状态
    if (!status) {
      return res.status(400).json({ message: '状态不能为空' });
    }
    
    // 转换状态值，将draft转为inactive以符合数据库模型
    let dbStatus = status;
    if (status === 'draft') {
      dbStatus = 'inactive';
    } else if (status === 'active') {
      dbStatus = 'active';
    } else {
      return res.status(400).json({ message: '无效的状态值，必须为draft或active' });
    }
    
    // 查找公告
    const announcement = await Announcement.findByPk(id);
    
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    // 更新状态
    await announcement.update({ status: dbStatus });
    
    // 返回前转换回前端使用的状态
    const responseData = announcement.toJSON();
    responseData.status = responseData.status === 'inactive' ? 'draft' : 'active';
    
    res.json(responseData);
    
  } catch (error) {
    console.error('更新公告状态失败:', error);
    res.status(500).json({ message: '更新公告状态失败', error: error.message });
  }
};

/**
 * 删除公告
 * 需要管理员权限
 */
exports.deleteAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找公告
    const announcement = await Announcement.findByPk(id);
    
    if (!announcement) {
      return res.status(404).json({ message: '公告不存在' });
    }
    
    // 删除公告
    await announcement.destroy();
    
    res.json({ message: '公告已删除' });
    
  } catch (error) {
    console.error('删除公告失败:', error);
    res.status(500).json({ message: '删除公告失败', error: error.message });
  }
}; 
const intelligentScheduleService = require('../services/intelligentScheduleService');
const holidayService = require('../services/holidayService');

/**
 * 发送时间调度控制器
 */
class SendingScheduleController {
  /**
   * 获取调度列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSchedules(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        pageSize: parseInt(req.query.pageSize) || 20,
        notificationType: req.query.notificationType,
        scheduleType: req.query.scheduleType,
        enabled: req.query.enabled !== undefined ? req.query.enabled === 'true' : undefined,
        search: req.query.search
      };
      
      const schedules = await intelligentScheduleService.getSchedules(options);
      res.json({
        code: 0,
        data: schedules,
        message: '获取调度列表成功'
      });
    } catch (error) {
      console.error('获取调度列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取调度列表失败: ' + error.message
      });
    }
  }

  /**
   * 获取调度详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getScheduleById(req, res) {
    try {
      const id = req.params.id;
      const schedule = await intelligentScheduleService.getScheduleById(id);
      
      res.json({
        code: 0,
        data: schedule,
        message: '获取调度详情成功'
      });
    } catch (error) {
      console.error('获取调度详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取调度详情失败: ' + error.message
      });
    }
  }

  /**
   * 创建调度
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createSchedule(req, res) {
    try {
      const scheduleData = req.body;
      const schedule = await intelligentScheduleService.createSchedule(scheduleData);
      
      res.json({
        code: 0,
        data: schedule,
        message: '创建调度成功'
      });
    } catch (error) {
      console.error('创建调度失败:', error);
      res.status(500).json({
        code: 500,
        message: '创建调度失败: ' + error.message
      });
    }
  }

  /**
   * 更新调度
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateSchedule(req, res) {
    try {
      const id = req.params.id;
      const scheduleData = req.body;
      const schedule = await intelligentScheduleService.updateSchedule(id, scheduleData);
      
      res.json({
        code: 0,
        data: schedule,
        message: '更新调度成功'
      });
    } catch (error) {
      console.error('更新调度失败:', error);
      res.status(500).json({
        code: 500,
        message: '更新调度失败: ' + error.message
      });
    }
  }

  /**
   * 删除调度
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteSchedule(req, res) {
    try {
      const id = req.params.id;
      await intelligentScheduleService.deleteSchedule(id);
      
      res.json({
        code: 0,
        message: '删除调度成功'
      });
    } catch (error) {
      console.error('删除调度失败:', error);
      res.status(500).json({
        code: 500,
        message: '删除调度失败: ' + error.message
      });
    }
  }

  /**
   * 启用/禁用调度
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async toggleSchedule(req, res) {
    try {
      const id = req.params.id;
      const { enabled } = req.body;
      
      if (enabled === undefined) {
        return res.status(400).json({
          code: 400,
          message: '参数错误: 缺少enabled字段'
        });
      }
      
      const schedule = await intelligentScheduleService.toggleSchedule(id, enabled);
      
      res.json({
        code: 0,
        data: schedule,
        message: enabled ? '启用调度成功' : '禁用调度成功'
      });
    } catch (error) {
      console.error('切换调度状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '切换调度状态失败: ' + error.message
      });
    }
  }

  /**
   * 计算下一次发送时间
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async calculateNextSendTime(req, res) {
    try {
      const { scheduleId, messageType, options } = req.body;
      
      if (!scheduleId) {
        return res.status(400).json({
          code: 400,
          message: '参数错误: 缺少scheduleId字段'
        });
      }
      
      const nextSendTime = await intelligentScheduleService.calculateNextSendTime(
        scheduleId,
        messageType || 'default',
        options || {}
      );
      
      res.json({
        code: 0,
        data: {
          nextSendTime,
          formattedTime: nextSendTime.toLocaleString()
        },
        message: '计算下一次发送时间成功'
      });
    } catch (error) {
      console.error('计算下一次发送时间失败:', error);
      res.status(500).json({
        code: 500,
        message: '计算下一次发送时间失败: ' + error.message
      });
    }
  }

  /**
   * 获取系统指标
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSystemMetrics(req, res) {
    try {
      const metrics = intelligentScheduleService.systemMetrics;
      
      res.json({
        code: 0,
        data: {
          cpu: metrics.cpu,
          memory: metrics.memory,
          lastUpdated: metrics.lastUpdated
        },
        message: '获取系统指标成功'
      });
    } catch (error) {
      console.error('获取系统指标失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取系统指标失败: ' + error.message
      });
    }
  }

  /**
   * 获取节假日信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getHolidays(req, res) {
    try {
      const { year, month } = req.query;
      
      if (!year || !month) {
        return res.status(400).json({
          code: 400,
          message: '参数错误: 缺少year或month字段'
        });
      }
      
      const holidays = holidayService.getHolidaysInMonth(parseInt(year), parseInt(month));
      
      res.json({
        code: 0,
        data: holidays,
        message: '获取节假日信息成功'
      });
    } catch (error) {
      console.error('获取节假日信息失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取节假日信息失败: ' + error.message
      });
    }
  }

  /**
   * 获取指定日期是否是工作日
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async checkWorkday(req, res) {
    try {
      const { date } = req.query;
      
      if (!date) {
        return res.status(400).json({
          code: 400,
          message: '参数错误: 缺少date字段'
        });
      }
      
      const isWorkday = holidayService.isWorkday(date);
      const isHoliday = holidayService.isHoliday(date);
      
      res.json({
        code: 0,
        data: {
          date,
          isWorkday,
          isHoliday
        },
        message: '检查工作日状态成功'
      });
    } catch (error) {
      console.error('检查工作日状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '检查工作日状态失败: ' + error.message
      });
    }
  }
}

module.exports = new SendingScheduleController(); 
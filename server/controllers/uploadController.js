const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
// 我们不需要在这里重复导入，因为路由已经处理了认证
// const { checkAuth } = require('../middlewares/auth');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads/images');
console.log('上传目录绝对路径:', uploadDir);
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
  console.log('创建上传目录成功:', uploadDir);
}

// 文件存储配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    console.log('上传文件目标目录:', uploadDir);
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名，保留原始扩展名
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    console.log('生成的文件名:', uniqueFilename);
    cb(null, uniqueFilename);
  }
});

// 文件过滤器 - 只接受图片
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    console.log('文件类型验证通过:', file.mimetype);
    cb(null, true);
  } else {
    console.log('文件类型验证失败:', file.mimetype);
    cb(new Error('不支持的文件类型，只允许上传图片文件 (JPEG, PNG, GIF, WEBP)'), false);
  }
};

// 上传配置
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB 限制
  fileFilter: fileFilter
});

// 上传图片处理函数
exports.uploadImage = (req, res) => {
  console.log('接收到图片上传请求');
  
  // 打印请求信息
  console.log('请求头:', JSON.stringify(req.headers));
  
  // 注意：在使用 multer 路由中间件的情况下，这里不应该再次调用 upload.single
  // 但为了向后兼容性，我们检查请求是否已经被 multer 处理过
  if (req.file) {
    console.log('请求已被 multer 中间件处理，文件数据:', req.file);
    handleUploadedFile(req, res);
    return;
  }
  
  // 单文件上传（如果还未处理）
  const uploadSingle = upload.single('file');
  
  uploadSingle(req, res, function (err) {
    if (err) {
      console.error('上传图片错误:', err);
      return res.status(400).json({
        errno: 1,
        message: err.message || '上传失败'
      });
    }
    
    if (!req.file) {
      console.error('没有文件被上传');
      return res.status(400).json({
        errno: 1,
        message: '没有文件被上传'
      });
    }
    
    console.log('文件信息:', req.file);
    
    // 组装完整URL
    const baseUrl = process.env.SERVER_URL || `${req.protocol}://${req.get('host')}`;
    const imagePath = `/uploads/images/${req.file.filename}`;
    const imageUrl = `${baseUrl}${imagePath}`;
    
    console.log('图片上传成功，URL:', imageUrl);
    
    // 返回成功响应 - Wang Editor 格式
    return res.status(200).json({
      errno: 0,
      data: {
        url: imageUrl,
        alt: req.file.originalname,
        href: imageUrl
      }
    });
  });
};

// 处理粘贴上传图片 - 用于富文本编辑器
exports.pasteImage = (req, res) => {
  console.log('接收到粘贴图片上传请求');
  
  upload.single('file')(req, res, function(err) {
    if (err) {
      console.error('粘贴图片上传错误:', err);
      return res.status(500).json({
        errno: 1,
        message: err.message || '粘贴图片上传失败'
      });
    }

    if (!req.file) {
      console.error('没有接收到粘贴图片文件');
      return res.status(400).json({
        errno: 1,
        message: '没有接收到粘贴图片文件'
      });
    }

    try {
      const baseUrl = process.env.SERVER_URL || `${req.protocol}://${req.get('host')}`;
      const imagePath = `/uploads/images/${req.file.filename}`;
      const imageUrl = `${baseUrl}${imagePath}`;
      
      console.log('粘贴图片上传成功，URL:', imageUrl);
      
      // 返回 Wang Editor 格式的响应
      return res.status(200).json({
        errno: 0,
        data: {
          url: imageUrl,
          alt: req.file.originalname || '粘贴的图片',
          href: null
        }
      });
    } catch (error) {
      console.error('处理粘贴图片错误:', error);
      return res.status(500).json({
        errno: 1,
        message: error.message || '处理粘贴图片失败'
      });
    }
  });
};

// 删除文件
exports.deleteFile = (req, res) => {
  const filename = req.params.filename;
  
  // 安全检查，防止目录遍历攻击
  if (!filename || filename.includes('/') || filename.includes('..')) {
    return res.status(400).json({
      success: false,
      message: '无效的文件名'
    });
  }
  
  const filePath = path.join(uploadDir, filename);
  
  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      message: '文件不存在'
    });
  }
  
  try {
    fs.unlinkSync(filePath);
    console.log('删除文件成功:', filename);
    return res.status(200).json({
      success: true,
      message: '文件已成功删除'
    });
  } catch (error) {
    console.error('删除文件错误:', error);
    return res.status(500).json({
      success: false,
      message: '删除文件时出错'
    });
  }
};

// 处理已上传的文件（被其他中间件如multer处理过的）
const handleUploadedFile = (req, res) => {
  try {
    if (!req.file) {
      console.error('没有找到已上传的文件');
      return res.status(400).json({
        errno: 1,
        message: '没有文件被上传'
      });
    }
    
    console.log('处理已上传的文件:', req.file);
    
    // 组装完整URL
    const baseUrl = process.env.SERVER_URL || `${req.protocol}://${req.get('host')}`;
    const imagePath = `/uploads/images/${req.file.filename}`;
    const imageUrl = `${baseUrl}${imagePath}`;
    
    console.log('图片URL:', imageUrl);
    
    // 返回成功响应 - Wang Editor 格式
    return res.status(200).json({
      errno: 0,
      data: {
        url: imageUrl,
        alt: req.file.originalname,
        href: imageUrl
      }
    });
  } catch (error) {
    console.error('处理已上传文件时出错:', error);
    return res.status(500).json({
      errno: 1,
      message: error.message || '处理上传文件失败'
    });
  }
}; 
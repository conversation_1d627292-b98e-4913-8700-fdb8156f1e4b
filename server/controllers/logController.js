const { Log, User, sequelize } = require('../models');
const { Op } = require('sequelize');

// 创建日志记录（系统内部调用，不暴露为API）
const createLog = async (logData, req = null) => {
  try {
    // 如果有请求对象，获取IP地址和用户信息
    if (req) {
      // 获取客户端IP地址，先尝试从X-Forwarded-For获取，再尝试从请求对象获取
      let ipAddress = req.headers['x-forwarded-for'] || 
                      req.headers['x-real-ip'] || 
                      req.ip || 
                      req.connection.remoteAddress || 
                      '';
      
      // 调试日志：仅在开发环境中记录原始IP地址信息
      if (process.env.NODE_ENV === 'development') {
        console.log('===== IP地址处理 =====');
        console.log('X-Forwarded-For:', req.headers['x-forwarded-for']);
        console.log('X-Real-IP:', req.headers['x-real-ip']);
        console.log('req.ip:', req.ip);
        console.log('connection.remoteAddress:', req.connection.remoteAddress);
        console.log('原始IP地址:', ipAddress);
      }
      
      // 处理IPv6格式的IP地址（如 ::ffff:127.0.0.1）
      if (ipAddress.includes('::ffff:')) {
        ipAddress = ipAddress.split('::ffff:')[1];
      }
      
      // 处理本地IPv6地址
      if (ipAddress === '::1') {
        ipAddress = '127.0.0.1';
      }
      
      // 如果是逗号分隔的多个IP（X-Forwarded-For可能包含多个IP），取第一个
      if (ipAddress && ipAddress.includes(',')) {
        ipAddress = ipAddress.split(',')[0].trim();
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('处理后的IP地址:', ipAddress);
        console.log('=====================');
      }
      
      if (req.user) {
        logData.userId = req.user.id;
        logData.username = req.user.username;
      }
      
      logData.ipAddress = ipAddress;
      
      // 添加设备信息
      const userAgent = req.headers['user-agent'];
      if (userAgent) {
        logData.deviceInfo = userAgent;
      }
    }
    
    // 确保日志数据格式正确
    if (logData.oldValue && typeof logData.oldValue !== 'string') {
      logData.oldValue = JSON.stringify(logData.oldValue);
    }
    
    if (logData.newValue && typeof logData.newValue !== 'string') {
      logData.newValue = JSON.stringify(logData.newValue);
    }
    
    const log = await Log.create(logData);
    return log;
  } catch (error) {
    console.error('创建日志记录失败:', error);
    return null;
  }
};

// 用户登录日志
const logUserLogin = async (user, req = null) => {
  try {
    return await createLog({
      action: Log.ACTIONS.USER_LOGIN,
      entityType: Log.ENTITY_TYPES.USER,
      entityId: user.id,
      userId: user.id,
      username: user.username,
      description: `用户 ${user.username} 登录系统`
    }, req);
  } catch (error) {
    console.error('记录用户登录日志失败:', error);
    return null;
  }
};

// 用户退出日志
const logUserLogout = async (user, req = null) => {
  try {
    return await createLog({
      action: Log.ACTIONS.USER_LOGOUT,
      entityType: Log.ENTITY_TYPES.USER,
      entityId: user.id,
      userId: user.id,
      username: user.username,
      description: `用户 ${user.username} 退出系统`
    }, req);
  } catch (error) {
    console.error('记录用户退出日志失败:', error);
    return null;
  }
};

// 用户密码变更日志
const logUserPasswordChange = async (user, req = null) => {
  try {
    return await createLog({
      action: Log.ACTIONS.USER_PASSWORD_CHANGE,
      entityType: Log.ENTITY_TYPES.USER,
      entityId: user.id,
      userId: user.id,
      username: user.username,
      description: `用户 ${user.username} 修改了密码`
    }, req);
  } catch (error) {
    console.error('记录用户密码变更日志失败:', error);
    return null;
  }
};

// 用户操作日志（创建/修改/删除）
const logUserAction = async (action, user, performedBy, oldValue = null, req = null) => {
  try {
    let description = '';
    switch(action) {
      case Log.ACTIONS.USER_REGISTER:
        description = `创建了用户 ${user.username}`;
        break;
      case Log.ACTIONS.USER_UPDATE:
        description = `更新了用户 ${user.username} 的信息`;
        break;
      case Log.ACTIONS.USER_DELETE:
        description = `删除了用户 ${user.username}`;
        break;
    }
    
    // 如果是管理员操作，添加操作者信息
    if (performedBy && performedBy.id !== user.id) {
      description = `管理员 ${performedBy.username} ${description}`;
    }
    
    return await createLog({
      action,
      entityType: Log.ENTITY_TYPES.USER,
      entityId: user.id,
      userId: performedBy ? performedBy.id : user.id,
      username: performedBy ? performedBy.username : user.username,
      oldValue: oldValue,
      newValue: JSON.stringify(user),
      description
    }, req);
  } catch (error) {
    console.error('记录用户操作日志失败:', error);
    return null;
  }
};

// 商品操作日志
const logProductAction = async (action, product, user, oldValue = null, req = null) => {
  try {
    let description = '';
    switch(action) {
      case Log.ACTIONS.PRODUCT_CREATE:
        description = `用户 ${user.username} 创建了商品 ${product.name}`;
        break;
      case Log.ACTIONS.PRODUCT_UPDATE:
        description = `用户 ${user.username} 更新了商品 ${product.name}`;
        break;
      case Log.ACTIONS.PRODUCT_DELETE:
        description = `用户 ${user.username} 删除了商品 ${product.name}`;
        break;
      case Log.ACTIONS.PRODUCT_STATUS_CHANGE:
        description = `用户 ${user.username} 修改了商品 ${product.name} 的状态为 ${product.status}`;
        break;
    }
    
    return await createLog({
      action,
      entityType: Log.ENTITY_TYPES.PRODUCT,
      entityId: product.id,
      userId: user.id,
      username: user.username,
      oldValue: oldValue,
      newValue: JSON.stringify(product),
      description
    }, req);
  } catch (error) {
    console.error('记录商品操作日志失败:', error);
    return null;
  }
};

// 文件操作日志
const logFileAction = async (action, file, user, req = null) => {
  try {
    let description = '';
    switch(action) {
      case Log.ACTIONS.FILE_UPLOAD:
        description = `用户 ${user.username} 上传了文件 ${file.originalName || file.filename}`;
        break;
      case Log.ACTIONS.FILE_DELETE:
        description = `用户 ${user.username} 删除了文件 ${file.originalName || file.filename}`;
        break;
    }
    
    return await createLog({
      action,
      entityType: Log.ENTITY_TYPES.FILE,
      entityId: file.id || 0,
      userId: user.id,
      username: user.username,
      newValue: JSON.stringify(file),
      description
    }, req);
  } catch (error) {
    console.error('记录文件操作日志失败:', error);
    return null;
  }
};

// 兑换操作日志
const logExchangeAction = async (action, exchange, user, oldValue = null, req = null) => {
  try {
    let description = '';
    switch(action) {
      case Log.ACTIONS.EXCHANGE_CREATE:
        description = `用户 ${user.username} 创建了兑换记录 #${exchange.id}`;
        break;
      case Log.ACTIONS.EXCHANGE_STATUS_UPDATE:
        description = `用户 ${user.username} 更新了兑换记录 #${exchange.id} 的状态为 ${exchange.status}`;
        break;
      case Log.ACTIONS.EXCHANGE_CANCEL:
        description = `用户 ${user.username} 取消了兑换记录 #${exchange.id}`;
        break;
    }
    
    return await createLog({
      action,
      entityType: Log.ENTITY_TYPES.EXCHANGE,
      entityId: exchange.id,
      userId: user.id,
      username: user.username,
      oldValue: oldValue,
      newValue: JSON.stringify(exchange),
      description
    }, req);
  } catch (error) {
    console.error('记录兑换操作日志失败:', error);
    return null;
  }
};

// 数据导入导出日志
const logDataAction = async (action, details, user, req = null) => {
  try {
    let description = '';
    switch(action) {
      case Log.ACTIONS.DATA_IMPORT:
        description = `用户 ${user.username} 导入了${details.type}数据，共${details.count}条记录`;
        break;
      case Log.ACTIONS.DATA_EXPORT:
        description = `用户 ${user.username} 导出了${details.type}数据，共${details.count}条记录`;
        break;
    }
    
    return await createLog({
      action,
      entityType: Log.ENTITY_TYPES.SYSTEM,
      entityId: 0,
      userId: user.id,
      username: user.username,
      newValue: JSON.stringify(details),
      description
    }, req);
  } catch (error) {
    console.error('记录数据操作日志失败:', error);
    return null;
  }
};

// 获取日志列表（分页、筛选）
const getLogs = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    // 构建筛选条件
    const where = {};
    
    // 按操作类型筛选 (支持多选，逗号分隔)
    if (req.query.action) {
      const actions = req.query.action.split(',').map(a => a.trim()).filter(a => a);
      if (actions.length > 0) {
        where.action = { [Op.in]: actions };
      }
    }
    
    // 按实体类型筛选 (支持多选，逗号分隔)
    if (req.query.entityType) {
      const entityTypes = req.query.entityType.split(',').map(et => et.trim()).filter(et => et);
      if (entityTypes.length > 0) {
        where.entityType = { [Op.in]: entityTypes };
      }
    }
    
    // 按实体ID筛选
    if (req.query.entityId) {
      where.entityId = parseInt(req.query.entityId);
    }
    
    // 按用户名筛选（模糊查询）
    if (req.query.username) {
      where.username = { [Op.like]: `%${req.query.username}%` };
    }
    
    // 按日期范围筛选
    if (req.query.startDate && req.query.endDate) {
      where.createdAt = {
        [Op.between]: [
          new Date(req.query.startDate), 
          new Date(req.query.endDate)
        ]
      };
    } else if (req.query.startDate) {
      where.createdAt = { [Op.gte]: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      where.createdAt = { [Op.lte]: new Date(req.query.endDate) };
    }
    
    // 按IP地址筛选
    if (req.query.ipAddress) {
      where.ipAddress = { [Op.like]: `%${req.query.ipAddress}%` };
    }
    
    // 查询数据
    const { count, rows } = await Log.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
    
    return res.status(200).json({
      success: true,
      data: rows,
      pagination: {
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('获取日志列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取日志列表失败',
      error: error.message
    });
  }
};

// 获取日志详情
const getLogById = async (req, res) => {
  try {
    const log = await Log.findByPk(req.params.id);
    
    if (!log) {
      return res.status(404).json({
        success: false,
        message: '日志记录不存在'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: log
    });
  } catch (error) {
    console.error('获取日志详情失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取日志详情失败',
      error: error.message
    });
  }
};

// 获取日志统计数据
const getLogStats = async (req, res) => {
  try {
    console.log("开始执行统计查询...");
    
    // 按日期统计日志数量
    const dateStart = req.query.dateStart || new Date(new Date().setDate(new Date().getDate() - 30));
    const dateEnd = req.query.dateEnd || new Date();
    
    console.log("日期范围:", dateStart, "至", dateEnd);
    
    let logCountsByDate = [];
    let logCountsByAction = [];
    let logCountsByEntityType = [];
    let userLoginStats = [];
    let productActionStats = [];
    
    try {
      // 使用MySQL兼容的日期格式化
      logCountsByDate = await Log.findAll({
        attributes: [
          [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
          [sequelize.fn('count', sequelize.col('id')), 'count']
        ],
        where: {
          createdAt: {
            [Op.between]: [dateStart, dateEnd]
          }
        },
        group: [sequelize.fn('DATE', sequelize.col('createdAt'))],
        order: [[sequelize.fn('DATE', sequelize.col('createdAt')), 'ASC']]
      });
      console.log("日期统计查询成功:", logCountsByDate.length);
    } catch (error) {
      console.error("日期统计查询失败:", error);
      // 提供默认数据
      logCountsByDate = [];
    }
    
    try {
      // 按操作类型统计
      logCountsByAction = await Log.findAll({
        attributes: [
          'action',
          [sequelize.fn('count', sequelize.col('id')), 'count']
        ],
        group: ['action'],
        order: [[sequelize.fn('count', sequelize.col('id')), 'DESC']]
      });
      console.log("操作类型统计查询成功:", logCountsByAction.length);
    } catch (error) {
      console.error("操作类型统计查询失败:", error);
      // 提供默认数据
      logCountsByAction = [];
    }
    
    try {
      // 按实体类型统计
      logCountsByEntityType = await Log.findAll({
        attributes: [
          'entityType',
          [sequelize.fn('count', sequelize.col('id')), 'count']
        ],
        group: ['entityType'],
        order: [[sequelize.fn('count', sequelize.col('id')), 'DESC']]
      });
      console.log("实体类型统计查询成功:", logCountsByEntityType.length);
    } catch (error) {
      console.error("实体类型统计查询失败:", error);
      // 提供默认数据
      logCountsByEntityType = [];
    }
    
    try {
      // 用户登录统计
      userLoginStats = await Log.findAll({
        attributes: [
          'username',
          [sequelize.fn('count', sequelize.col('id')), 'loginCount'],
          [sequelize.fn('MAX', sequelize.col('createdAt')), 'lastLoginTime']
        ],
        where: {
          action: Log.ACTIONS.USER_LOGIN,
          createdAt: {
            [Op.between]: [dateStart, dateEnd]
          }
        },
        group: ['username'],
        order: [[sequelize.fn('count', sequelize.col('id')), 'DESC']]
      });
      console.log("用户登录统计查询成功:", userLoginStats.length);
    } catch (error) {
      console.error("用户登录统计查询失败:", error);
      userLoginStats = [];
    }
    
    try {
      // 商品操作统计
      productActionStats = await Log.findAll({
        attributes: [
          'action',
          [sequelize.fn('count', sequelize.col('id')), 'count']
        ],
        where: {
          entityType: Log.ENTITY_TYPES.PRODUCT,
          createdAt: {
            [Op.between]: [dateStart, dateEnd]
          }
        },
        group: ['action'],
        order: [[sequelize.fn('count', sequelize.col('id')), 'DESC']]
      });
      console.log("商品操作统计查询成功:", productActionStats.length);
    } catch (error) {
      console.error("商品操作统计查询失败:", error);
      productActionStats = [];
    }
    
    return res.status(200).json({
      success: true,
      data: {
        byDate: logCountsByDate,
        byAction: logCountsByAction,
        byEntityType: logCountsByEntityType,
        userLogins: userLoginStats,
        productActions: productActionStats
      }
    });
  } catch (error) {
    console.error('获取日志统计数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取日志统计数据失败',
      error: error.message
    });
  }
};

// 导出日志（CSV格式）
const exportLogs = async (req, res) => {
  try {
    // 构建筛选条件，与getLogs方法类似
    const where = {};
    
    // 按操作类型筛选
    if (req.query.action) {
      where.action = req.query.action;
    }
    
    // 按实体类型筛选
    if (req.query.entityType) {
      where.entityType = req.query.entityType;
    }
    
    // 按实体ID筛选
    if (req.query.entityId) {
      where.entityId = parseInt(req.query.entityId);
    }
    
    // 按用户筛选
    if (req.query.userId) {
      where.userId = parseInt(req.query.userId);
    }
    
    // 按用户名筛选（模糊查询）
    if (req.query.username) {
      where.username = { [Op.like]: `%${req.query.username}%` };
    }
    
    // 按日期范围筛选
    if (req.query.startDate && req.query.endDate) {
      where.createdAt = {
        [Op.between]: [
          new Date(req.query.startDate), 
          new Date(req.query.endDate)
        ]
      };
    } else if (req.query.startDate) {
      where.createdAt = { [Op.gte]: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      where.createdAt = { [Op.lte]: new Date(req.query.endDate) };
    }
    
    // 按IP地址筛选
    if (req.query.ipAddress) {
      where.ipAddress = { [Op.like]: `%${req.query.ipAddress}%` };
    }
    
    // 查询数据，不分页，最多导出10000条
    const logs = await Log.findAll({
      where,
      order: [['createdAt', 'DESC']],
      limit: 10000
    });
    
    if (logs.length === 0) {
      return res.status(404).json({
        success: false,
        message: '没有符合条件的日志数据可导出'
      });
    }
    
    // 格式化为CSV数据
    const csvHeader = "ID,操作类型,实体类型,实体ID,操作用户,IP地址,操作描述,操作时间\n";
    let csvContent = csvHeader;
    
    logs.forEach(log => {
      // 预处理字段，避免CSV注入和格式问题
      const description = log.description ? log.description.replace(/"/g, '""').replace(/,/g, '，') : '';
      const actionName = getActionDisplayName(log.action);
      const entityTypeName = getEntityTypeDisplayName(log.entityType);
      const formattedDate = new Date(log.createdAt).toISOString().replace('T', ' ').substring(0, 19);
      
      csvContent += `${log.id},"${actionName}","${entityTypeName}",${log.entityId},"${log.username || ''}","${log.ipAddress || ''}","${description}","${formattedDate}"\n`;
    });
    
    // 设置文件名
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const filename = `logs_export_${date}.csv`;
    
    // 设置响应头
    res.setHeader('Content-Type', 'text/csv;charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    
    // 返回CSV数据
    return res.send(csvContent);
  } catch (error) {
    console.error('导出日志失败:', error);
    return res.status(500).json({
      success: false,
      message: '导出日志失败',
      error: error.message
    });
  }
};

// 获取操作类型显示名称
const getActionDisplayName = (action) => {
  const actionMap = {
    'user_login': '用户登录',
    'user_logout': '用户退出',
    'user_register': '用户注册',
    'user_update': '用户更新',
    'user_delete': '用户删除',
    'user_password_change': '用户密码变更',
    'product_create': '商品创建',
    'product_update': '商品更新',
    'product_delete': '商品删除',
    'product_status_change': '商品状态变更',
    'stock_update': '库存变更',
    'exchange_create': '创建兑换',
    'exchange_status_update': '兑换状态变更',
    'exchange_cancel': '取消兑换',
    'file_upload': '文件上传',
    'file_delete': '文件删除',
    'data_import': '数据导入',
    'data_export': '数据导出',
    'system_logs_cleanup': '日志清理'
  };
  return actionMap[action] || action;
};

// 获取实体类型显示名称
const getEntityTypeDisplayName = (entityType) => {
  const entityMap = {
    'user': '用户',
    'product': '商品',
    'exchange': '兑换',
    'stock': '库存',
    'file': '文件',
    'system': '系统'
  };
  return entityMap[entityType] || entityType;
};

module.exports = {
  createLog,
  logUserLogin,
  logUserLogout,
  logUserPasswordChange,
  logUserAction,
  logProductAction,
  logFileAction,
  logExchangeAction,
  logDataAction,
  getLogs,
  getLogById,
  getLogStats,
  exportLogs
}; 
const db = require('../models');
const User = db.User;
const bcrypt = require('bcryptjs');
const { Op } = require('sequelize');
const fs = require('fs');
const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const path = require('path');
const Excel = require('exceljs');
const { logUserAction, logUserPasswordChange } = require('../controllers/logController');
const Log = require('../models/log');

// 获取用户列表
exports.getUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';

    // 构建查询条件
    const where = {};
    if (search) {
      where.username = { [Op.like]: `%${search}%` };
    }

    const { count, rows } = await User.findAndCountAll({
      where,
      attributes: ['id', 'username', 'email', 'role', 'department', 'departmentPath', 'workplace', 'mobile', 'createdAt', 'updatedAt', 'lastLoginAt', 'lastLoginIp', 'points'],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    return res.json({
      data: rows,
      total: count,
      page,
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return res.status(500).json({ message: '获取用户列表失败，请稍后重试' });
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    console.log('===== 创建用户请求开始 =====');
    console.log('请求体:', JSON.stringify(req.body, null, 2));
    
    // 提取请求数据
    let { username, password, email, role, department, workplace, workplaceId, mobile } = req.body;

    // 验证必填字段
    if (!username || !password || !email) {
      return res.status(400).json({ message: '用户名、密码和邮箱为必填项' });
    }

    // 验证邮箱格式
    const isValidEmail = (email) => {
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    };
    
    if (!isValidEmail(email)) {
      return res.status(400).json({ message: '无效的邮箱格式' });
    }
    
    // 验证邮箱后缀
    if (!email.endsWith('@guanghe.tv')) {
      return res.status(400).json({ message: '邮箱必须以@guanghe.tv结尾' });
    }

    // 验证密码长度
    if (password.length < 6) {
      return res.status(400).json({ message: '密码长度至少为6个字符' });
    }

    // 验证角色
    if (role && !['admin', 'user'].includes(role)) {
      return res.status(400).json({ message: '无效的角色值，只能是"admin"或"user"' });
    }

    // 处理手机号码前缀
    if (mobile && mobile.startsWith('+86')) {
      mobile = mobile.substring(3);
    }

    // 验证手机号码格式（如果提供）
    if (mobile && !/^1[3-9]\d{9}$/.test(mobile)) {
      return res.status(400).json({ message: '无效的手机号码格式' });
    }

    // 检查邮箱是否已存在
    const existingUserByEmail = await User.findOne({ where: { email } });
    if (existingUserByEmail) {
      return res.status(409).json({ 
        message: '该邮箱已被注册',
        code: 'EMAIL_ALREADY_EXIST' 
      });
    }

    console.log(`===== 开始创建用户 ${username} =====`);
    
    // 直接加密密码，而不是依赖模型钩子
    const salt = await bcrypt.genSalt(10);
    console.log(`生成盐值: ${salt}`);
    
    const hashedPassword = await bcrypt.hash(password, salt);
    console.log(`密码哈希: ${hashedPassword.substring(0, 15)}...`);
    
    // 验证哈希是否有效
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log(`哈希验证: ${isValid ? '成功' : '失败'}`);
    
    // 创建新用户，禁用钩子避免双重加密
    const user = await User.create({
      username,
      email,
      password: hashedPassword,
      role: role || 'user',
      department: department || null,
      workplace: workplace || null,
      workplaceId: workplaceId || null,
      mobile: mobile || null
    }, { hooks: false });

    // 验证新创建的用户密码是否可以匹配
    const verifyUser = await User.findByPk(user.id);
    const passwordCheck = await bcrypt.compare(password, verifyUser.password);
    console.log(`最终密码验证: ${passwordCheck ? '成功' : '失败'}`);
    
    console.log(`===== 用户 ${username} 创建完成 =====`);

    // 记录用户注册日志
    await logUserAction(
      Log.ACTIONS.USER_REGISTER,
      user,
      req.user, // 创建用户的管理员
      null,
      req
    );

    // 移除敏感字段
    const userData = user.get();
    delete userData.password;

    return res.status(201).json({
      message: '用户创建成功',
      user: userData
    });
  } catch (error) {
    console.error('创建用户失败:', error);
    
    // 改进错误处理，提供更明确的错误信息
    if (error.name === 'SequelizeUniqueConstraintError') {
      // 处理唯一约束错误
      const field = error.errors && error.errors[0] && error.errors[0].path;
      if (field === 'username') {
        return res.status(409).json({ 
          message: '用户名已存在',
          code: 'USERNAME_ALREADY_EXIST'
        });
      } else if (field === 'email') {
        return res.status(409).json({ 
          message: '邮箱已被注册',
          code: 'EMAIL_ALREADY_EXIST'
        });
      }
    } else if (error.name === 'SequelizeValidationError') {
      // 处理验证错误
      const errorMessages = error.errors.map(err => err.message).join(', ');
      return res.status(400).json({ 
        message: `验证失败: ${errorMessages}`,
        code: 'VALIDATION_ERROR'
      });
    }
    
    // 默认错误处理
    return res.status(500).json({ 
      message: '创建用户失败，请稍后重试',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 不能删除自己
    if (parseInt(id) === req.user.id) {
      return res.status(400).json({ message: '不能删除自己的账户' });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 保存用户数据用于日志记录
    const userData = { ...user.get() };
    
    await user.destroy();
    
    // 记录用户删除日志
    try {
      await logUserAction(Log.ACTIONS.USER_DELETE, userData, req.user, JSON.stringify(userData), req);
    } catch (logError) {
      console.error('记录用户删除日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('删除用户失败:', error);
    return res.status(500).json({ message: '删除用户失败，请稍后重试' });
  }
};

// 更新用户部门
exports.updateUserDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { department } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 保存更新前的用户数据，用于日志记录
    const oldUserData = { ...user.get() };
    
    // 执行更新
    await user.update({ department });

    // 记录用户更新日志
    try {
      await logUserAction(Log.ACTIONS.USER_UPDATE, user, req.user, JSON.stringify(oldUserData), req);
    } catch (logError) {
      console.error('记录用户部门更新日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({
      message: '部门更新成功',
      user: {
        id: user.id,
        username: user.username,
        department: user.department
      }
    });
  } catch (error) {
    console.error('更新用户部门失败:', error);
    return res.status(500).json({ message: '更新用户部门失败，请稍后重试' });
  }
};

// 更新用户邮箱
exports.updateUserEmail = async (req, res) => {
  try {
    const { id } = req.params;
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: '邮箱不能为空' });
    }
    
    // 验证邮箱格式
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({ message: '无效的邮箱格式' });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 检查邮箱是否已被其他用户使用
    const existingUser = await User.findOne({ where: { email, id: { [Op.ne]: id } } });
    if (existingUser) {
      return res.status(409).json({ message: '邮箱已被其他用户使用' });
    }

    // 保存更新前的用户数据，用于日志记录
    const oldUserData = { ...user.get() };

    // 更新邮箱
    await user.update({ email });

    // 记录用户更新日志
    try {
      await logUserAction(Log.ACTIONS.USER_UPDATE, user, req.user, JSON.stringify(oldUserData), req);
    } catch (logError) {
      console.error('记录用户邮箱更新日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({
      message: '邮箱更新成功',
      user: {
        id: user.id,
        username: user.username,
        email: user.email
      }
    });
  } catch (error) {
    console.error('更新用户邮箱失败:', error);
    return res.status(500).json({ message: '更新用户邮箱失败，请稍后重试' });
  }
};

// 更新用户手机号码
exports.updateUserMobile = async (req, res) => {
  try {
    const { id } = req.params;
    let { mobile } = req.body;

    if (!mobile) {
      return res.status(400).json({ message: '手机号码不能为空' });
    }
    
    // 去除可能的+86前缀
    if (mobile.startsWith('+86')) {
      mobile = mobile.substring(3);
    }
    
    // 验证手机号码格式（中国大陆手机号格式）
    if (!/^1[3-9]\d{9}$/.test(mobile)) {
      return res.status(400).json({ message: '无效的手机号码格式' });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 保存更新前的用户数据，用于日志记录
    const oldUserData = { ...user.get() };

    // 更新手机号码
    await user.update({ mobile });

    // 记录用户更新日志
    try {
      await logUserAction(Log.ACTIONS.USER_UPDATE, user, req.user, JSON.stringify(oldUserData), req);
    } catch (logError) {
      console.error('记录用户手机号码更新日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({
      message: '手机号码更新成功',
      user: {
        id: user.id,
        username: user.username,
        mobile: user.mobile
      }
    });
  } catch (error) {
    console.error('更新用户手机号码失败:', error);
    return res.status(500).json({ message: '更新用户手机号码失败，请稍后重试' });
  }
};

// 更新用户角色
exports.updateUserRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    // 验证角色值
    if (!role || !['admin', 'user'].includes(role)) {
      return res.status(400).json({ message: '角色值无效，必须为 admin 或 user' });
    }

    // 不能修改自己的角色
    if (parseInt(id) === req.user.id) {
      return res.status(400).json({ message: '不能修改自己的角色' });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 保存更新前的用户数据，用于日志记录
    const oldUserData = { ...user.get() };
    
    // 执行更新
    await user.update({ role });

    // 记录用户更新日志
    try {
      await logUserAction(Log.ACTIONS.USER_UPDATE, user, req.user, JSON.stringify(oldUserData), req);
    } catch (logError) {
      console.error('记录用户角色更新日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({
      message: '角色更新成功',
      user: {
        id: user.id,
        username: user.username,
        role: user.role
      }
    });
  } catch (error) {
    console.error('更新用户角色失败:', error);
    return res.status(500).json({ message: '更新用户角色失败，请稍后重试' });
  }
};

// 重置用户密码
exports.resetUserPassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { password } = req.body;

    // 验证密码
    if (!password || password.length < 6) {
      return res.status(400).json({ message: '密码无效，长度必须至少为6个字符' });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);
    await user.update({ password: hashedPassword });

    // 记录密码重置日志
    try {
      await logUserPasswordChange(user, req);
    } catch (logError) {
      console.error('记录密码重置日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({ message: '密码重置成功' });
  } catch (error) {
    console.error('重置用户密码失败:', error);
    return res.status(500).json({ message: '重置用户密码失败，请稍后重试' });
  }
};

// 批量导入用户
exports.importUsers = async (req, res) => {
  try {
    console.log('===== 开始处理批量导入用户请求 =====');
    
    // 检查请求体和文件
    console.log('请求头:', JSON.stringify(req.headers, null, 2));
    console.log('请求路径:', req.path);
    console.log('中间件状态:', {
      'express-fileupload是否可用': req.files ? '是' : '否',
      'multer是否使用': req.file ? '是' : '否'
    });
    
    // 使用express-fileupload中间件处理上传文件
    if (!req.files || !req.files.file) {
      console.error('未接收到上传文件 - req.files:', req.files);
      console.error('req.file (multer):', req.file);
      return res.status(400).json({ message: '未上传文件或文件上传处理中间件未正确配置' });
    }
    
    const uploadedFile = req.files.file;
    console.log('接收到文件:', {
      文件名: uploadedFile.name,
      大小: uploadedFile.size,
      类型: uploadedFile.mimetype,
      'tempFilePath': uploadedFile.tempFilePath || '未使用临时文件'
    });
    
    // 检查文件是否有效
    if (!uploadedFile.name || uploadedFile.size === 0) {
      console.error('上传的文件无效:', uploadedFile);
      return res.status(400).json({ message: '上传的文件无效或损坏' });
    }

    const results = [];
    const errors = [];
    let successCount = 0;
    const fileExt = path.extname(uploadedFile.name).toLowerCase();
    
    // 确保临时目录存在
    const tempDir = path.join(__dirname, '..', 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // 生成临时文件路径
    const tempFilePath = path.join(tempDir, `${Date.now()}-${uploadedFile.name}`);
    
    // 将上传的文件保存到临时目录
    await uploadedFile.mv(tempFilePath);
    console.log('文件已保存到临时目录:', tempFilePath);

    if (fileExt === '.csv') {
      // 处理CSV文件
      fs.createReadStream(tempFilePath)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          // 删除临时文件
          fs.unlinkSync(tempFilePath);
          
          // 验证是否有有效数据
          if (results.length === 0) {
            return res.status(400).json({ message: '没有读取到有效数据，请检查文件内容' });
          }
          
          await processImportData(results, errors, successCount, res);
        })
        .on('error', (error) => {
          console.error('CSV解析错误:', error);
          // 尝试删除临时文件
          try {
            if (fs.existsSync(tempFilePath)) {
              fs.unlinkSync(tempFilePath);
            }
          } catch (e) {
            console.error('删除临时文件失败:', e);
          }
          
          return res.status(500).json({ 
            message: '解析CSV文件失败，请确保文件格式正确',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
          });
        });
    } else if (fileExt === '.xlsx' || fileExt === '.xls') {
      // 处理Excel文件
      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.readFile(tempFilePath);
        
        const worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
          fs.unlinkSync(tempFilePath); // 删除临时文件
          return res.status(400).json({ message: '找不到工作表，请确保Excel文件包含有效的工作表' });
        }
        
        const headers = [];
        const headerRow = worksheet.getRow(1);
        let hasValidHeaders = false;
        
        // 获取表头
        headerRow.eachCell((cell, colNumber) => {
          // 确保表头值是字符串
          let headerValue = '';
          try {
            if (cell.value !== null && cell.value !== undefined) {
              // 处理不同类型的单元格值
              if (typeof cell.value === 'object') {
                // 日期或富文本类型
                if (cell.value.text) {
                  headerValue = cell.value.text;
                } else if (cell.value.result) {
                  headerValue = cell.value.result.toString();
                } else {
                  headerValue = '';
                }
              } else {
                headerValue = cell.value.toString();
              }
            }
            headers[colNumber - 1] = headerValue.toLowerCase();
            
            // 检查是否包含必要的表头
            if (['username', 'password', 'email'].includes(headerValue.toLowerCase())) {
              hasValidHeaders = true;
            }
          } catch (error) {
            console.error(`处理表头第${colNumber}列出错:`, error);
            headers[colNumber - 1] = '';
          }
        });
        
        console.log('检测到的表头:', headers);
        
        if (!hasValidHeaders) {
          fs.unlinkSync(tempFilePath); // 删除临时文件
          return res.status(400).json({ 
            message: '无效的表头格式，请确保包含username、password和email字段',
            headers: headers
          });
        }
        
        // 从第二行开始读取数据
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber > 1) { // 跳过表头
            const rowData = {};
            let hasData = false;
            
            row.eachCell((cell, colNumber) => {
              try {
                const header = headers[colNumber - 1];
                if (header) {
                  // 处理不同类型的单元格值
                  let cellValue = '';
                  if (cell.value !== null && cell.value !== undefined) {
                    if (typeof cell.value === 'object') {
                      // 日期类型
                      if (cell.value instanceof Date) {
                        cellValue = cell.value.toISOString();
                      } 
                      // 富文本类型
                      else if (cell.value.text) {
                        cellValue = cell.value.text;
                      } 
                      // 公式结果
                      else if (cell.value.result) {
                        cellValue = cell.value.result.toString();
                      } 
                      // 其他对象类型
                      else {
                        cellValue = JSON.stringify(cell.value);
                      }
                    } else {
                      cellValue = cell.value.toString();
                    }
                  }
                  rowData[header] = cellValue;
                  hasData = true;
                }
              } catch (error) {
                console.error(`处理第${rowNumber}行第${colNumber}列数据出错:`, error);
                // 对于错误的单元格，设置为空字符串
                if (headers[colNumber - 1]) {
                  rowData[headers[colNumber - 1]] = '';
                }
              }
            });
            
            console.log(`读取到第${rowNumber}行数据:`, rowData);
            
            // 只有当有数据且必填字段不全部为空时才添加行数据
            if (hasData && (rowData.username || rowData.password || rowData.email)) {
              results.push(rowData);
            } else {
              console.warn(`第${rowNumber}行数据不完整或为空，已跳过`, rowData);
            }
          }
        });
        
        // 删除临时文件
        fs.unlinkSync(tempFilePath);
        
        console.log(`总共读取到${results.length}条有效数据`);
        
        if (results.length === 0) {
          return res.status(400).json({ message: '没有读取到有效数据，请检查文件内容' });
        }
        
        // 处理导入数据
        await processImportData(results, errors, successCount, res);
      } catch (error) {
        console.error('处理Excel文件失败:', error);
        // 尝试删除临时文件
        try {
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
          }
        } catch (e) {
          console.error('删除临时文件失败:', e);
        }
        
        return res.status(500).json({ 
          message: '处理Excel文件失败，请确保文件格式正确',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
      }
    } else {
      // 删除临时文件
      fs.unlinkSync(tempFilePath);
      return res.status(400).json({ message: '不支持的文件格式，请上传CSV或Excel文件' });
    }
  } catch (error) {
    console.error('批量导入用户失败:', error);
    
    // 尝试删除临时文件
    try {
      if (typeof tempFilePath === 'string' && fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
        console.log('已清理临时文件:', tempFilePath);
      }
    } catch (err) {
      console.error('删除临时文件失败:', err);
    }
    
    // 提供更详细的错误信息
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({ 
        message: '导入失败：存在重复的用户名或邮箱',
        error: error.message
      });
    } else if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ 
        message: '导入失败：数据验证错误',
        error: error.message
      });
    }
    return res.status(500).json({ 
      message: '批量导入用户失败，请稍后重试',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 处理导入的用户数据
async function processImportData(results, errors, successCount, res) {
  try {
    for (const row of results) {
      try {
        // 验证必填字段
        if (!row.username || !row.password || !row.email) {
          errors.push({
            line: results.indexOf(row) + 2, // +2是因为有标题行，并且索引从0开始
            message: '用户名、密码和邮箱为必填项',
            data: row
          });
          continue;
        }
        
        // 验证邮箱格式
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
          errors.push({
            line: results.indexOf(row) + 2,
            message: '邮箱格式无效',
            data: row
          });
          continue;
        }
        
        // 验证邮箱后缀
        if (!row.email.endsWith('@guanghe.tv')) {
          errors.push({
            line: results.indexOf(row) + 2,
            message: '邮箱必须以@guanghe.tv结尾',
            data: row
          });
          continue;
        }
        
        // 验证密码长度
        if (row.password.length < 6) {
          errors.push({
            line: results.indexOf(row) + 2,
            message: '密码长度必须至少为6个字符',
            data: row
          });
          continue;
        }
        
        // 验证角色值
        if (row.role && !['admin', 'user'].includes(row.role)) {
          errors.push({
            line: results.indexOf(row) + 2,
            message: '角色值无效，必须为 admin 或 user',
            data: row
          });
          continue;
        }
        
        // 验证手机号码格式（如果提供）
        if (row.mobile && !/^1[3-9]\d{9}$/.test(row.mobile)) {
          errors.push({
            line: results.indexOf(row) + 2,
            message: '无效的手机号码格式',
            data: row
          });
          continue;
        }
        
        // 检查邮箱是否已存在
        const existingUser = await User.findOne({ where: { email: row.email } });
        if (existingUser) {
          errors.push({
            line: results.indexOf(row) + 2,
            message: '邮箱已被使用',
            data: row
          });
          continue;
        }
        
        // 加密密码
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(row.password, salt);
        
        // 创建用户
        await User.create({
          username: row.username,
          password: hashedPassword,
          email: row.email,
          role: row.role || 'user',
          department: row.department || null,
          workplace: row.workplace || null,
          workplaceId: row.workplaceId || null,
          mobile: row.mobile || null,
          authType: 'password' // 导入的用户默认使用密码认证
        }, { hooks: false }); // 禁用钩子避免二次加密
        
        successCount++;
      } catch (error) {
        console.error('导入用户数据行处理错误:', error);
        errors.push({
          line: results.indexOf(row) + 2,
          message: error.message,
          data: row
        });
      }
    }

    console.log(`导入结果: 总数=${results.length}, 成功=${successCount}, 失败=${errors.length}`);
    
    return res.json({
      message: `批量导入完成，成功: ${successCount}，失败: ${errors.length}`,
      total: results.length,
      success: successCount,
      errors: errors
    });
  } catch (error) {
    console.error('处理导入数据失败:', error);
    return res.status(500).json({ 
      message: '处理导入数据失败，请稍后重试',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined 
    });
  }
}

// 导出用户
exports.exportUsers = async (req, res) => {
  try {
    const search = req.query.search || '';
    const format = req.query.format || 'csv'; // 默认为CSV格式

    // 构建查询条件
    const where = {};
    if (search) {
      where.username = { [Op.like]: `%${search}%` };
    }

    // 查询用户
    const users = await User.findAll({
      where,
      attributes: ['id', 'username', 'email', 'role', 'department', 'workplace', 'mobile', 'createdAt', 'updatedAt'],
      order: [['createdAt', 'DESC']]
    });

    // 准备导出数据
    const exportData = users.map(user => {
      const userData = user.get();
      return {
        id: userData.id,
        username: userData.username,
        email: userData.email || '',
        mobile: userData.mobile || '',
        role: userData.role,
        department: userData.department || '',
        workplace: userData.workplace || '',
        createdAt: new Date(userData.createdAt).toLocaleString('zh-CN'),
        updatedAt: new Date(userData.updatedAt).toLocaleString('zh-CN')
      };
    });

    // 创建临时文件夹
    const tempDir = path.join(__dirname, '..', 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }

    // 创建文件名
    const timestamp = new Date().getTime();
    
    if (format === 'csv') {
      // 导出为CSV
      const filename = `users_export_${timestamp}.csv`;
      const filepath = path.join(tempDir, filename);

      // 配置CSV写入器
      const csvWriter = createCsvWriter({
        path: filepath,
        header: [
          { id: 'id', title: 'ID' },
          { id: 'username', title: '姓名' },
          { id: 'email', title: '邮箱' },
          { id: 'mobile', title: '手机号码' },
          { id: 'role', title: '角色' },
          { id: 'department', title: '部门' },
          { id: 'workplace', title: '职场' },
          { id: 'createdAt', title: '创建时间' },
          { id: 'updatedAt', title: '更新时间' }
        ]
      });

      // 写入数据
      await csvWriter.writeRecords(exportData);

      // 发送文件
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      
      const fileStream = fs.createReadStream(filepath);
      fileStream.pipe(res);
      
      // 设置结束后删除临时文件
      fileStream.on('end', () => {
        fs.unlinkSync(filepath);
      });
    } else if (format === 'xlsx') {
      // 导出为Excel
      const filename = `users_export_${timestamp}.xlsx`;
      const filepath = path.join(tempDir, filename);

      // 创建工作簿和工作表
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('用户列表');

      // 添加表头
      worksheet.columns = [
        { header: 'ID', key: 'id', width: 10 },
        { header: '姓名', key: 'username', width: 20 },
        { header: '邮箱', key: 'email', width: 30 },
        { header: '手机号码', key: 'mobile', width: 20 },
        { header: '角色', key: 'role', width: 15 },
        { header: '部门', key: 'department', width: 20 },
        { header: '职场', key: 'workplace', width: 20 },
        { header: '创建时间', key: 'createdAt', width: 20 },
        { header: '更新时间', key: 'updatedAt', width: 20 }
      ];

      // 设置表头样式
      worksheet.getRow(1).font = { bold: true };

      // 添加数据
      exportData.forEach(user => {
        worksheet.addRow(user);
      });

      // 写入文件
      await workbook.xlsx.writeFile(filepath);

      // 发送文件
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      
      const fileStream = fs.createReadStream(filepath);
      fileStream.pipe(res);
      
      // 设置结束后删除临时文件
      fileStream.on('end', () => {
        fs.unlinkSync(filepath);
      });
    } else {
      return res.status(400).json({ message: '不支持的导出格式' });
    }
  } catch (error) {
    console.error('导出用户列表失败:', error);
    return res.status(500).json({ message: '导出用户列表失败，请稍后重试' });
  }
};

// 下载用户导入模板
exports.downloadTemplate = async (req, res) => {
  try {
    const format = req.query.format || 'csv';
    const templatePath = path.join(__dirname, '../templates');
    let filePath;
    let contentType;
    
    if (format === 'xlsx' || format === 'xls') {
      filePath = path.join(templatePath, 'user_import_template.xlsx');
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    } else {
      filePath = path.join(templatePath, 'user_import_template.csv');
      contentType = 'text/csv';
    }
    
    // 检查模板文件是否存在，不存在则创建
    if (!fs.existsSync(filePath)) {
      await createTemplate(format, filePath);
    }
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="user_import_template.${format}"`);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('下载模板失败:', error);
    return res.status(500).json({ message: '下载模板失败，请稍后重试' });
  }
};

// 用户修改自己的密码
exports.updatePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = req.user.id;

    // 验证请求字段
    if (!oldPassword || !newPassword) {
      return res.status(400).json({ message: '原密码和新密码为必填项' });
    }

    // 验证新密码长度
    if (newPassword.length < 6) {
      return res.status(400).json({ message: '新密码长度必须至少为6个字符' });
    }

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 验证原密码
    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: '原密码不正确' });
    }

    // 检查新密码是否与旧密码相同
    if (oldPassword === newPassword) {
      return res.status(400).json({ message: '新密码不能与原密码相同' });
    }

    // 加密新密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // 更新密码
    await user.update({ password: hashedPassword }, { hooks: false });

    // 记录密码变更日志
    try {
      await logUserPasswordChange(user, req);
    } catch (logError) {
      console.error('记录密码变更日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('修改密码失败:', error);
    return res.status(500).json({ message: '修改密码失败，请稍后重试' });
  }
};

// 创建导入模板文件
const createTemplate = async (format, filePath) => {
  try {
    const header = ['username', 'password', 'email', 'role', 'department', 'workplace', 'mobile'];
    const data = [
      ['张三', 'password123', '<EMAIL>', 'user', '研发部', '北京', '13800000000']
    ];

    if (format === 'xlsx' || format === 'xls') {
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('用户模板');
      
      // 添加表头
      worksheet.addRow(header);
      
      // 添加示例数据
      data.forEach(row => {
        worksheet.addRow(row);
      });
      
      // 添加格式说明
      const noteSheet = workbook.addWorksheet('说明');
      noteSheet.addRow(['字段', '说明', '是否必填', '示例值']);
      noteSheet.addRow(['username', '用户姓名', '是', '张三']);
      noteSheet.addRow(['password', '密码', '是', 'password123']);
      noteSheet.addRow(['email', '邮箱', '是', '<EMAIL>']);
      noteSheet.addRow(['role', '角色（admin或user）', '否，默认user', 'user']);
      noteSheet.addRow(['department', '部门', '否', '研发部']);
      noteSheet.addRow(['workplace', '职场', '否', '北京']);
      noteSheet.addRow(['mobile', '手机号码', '否', '13800000000']);
      
      // 设置列宽
      noteSheet.getColumn(1).width = 15;
      noteSheet.getColumn(2).width = 40;
      noteSheet.getColumn(3).width = 15;
      noteSheet.getColumn(4).width = 25;
      
      await workbook.xlsx.writeFile(filePath);
    } else {
      // 创建CSV
      const csvWriter = createCsvWriter({
        path: filePath,
        header: header.map(h => ({ id: h, title: h }))
      });
      
      await csvWriter.writeRecords(data.map(row => {
        const obj = {};
        header.forEach((key, index) => {
          obj[key] = row[index];
        });
        return obj;
      }));
    }
    
    console.log(`模板文件 ${filePath} 创建成功`);
  } catch (error) {
    console.error('创建模板文件失败:', error);
    throw error;
  }
};

// 更新用户职场
exports.updateUserWorkplace = async (req, res) => {
  try {
    const { id } = req.params;
    const { workplace, workplaceId } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 保存更新前的用户数据，用于日志记录
    const oldUserData = { ...user.get() };
    
    // 执行更新
    await user.update({ 
      workplace,
      workplaceId: workplaceId || null
    });

    // 记录用户更新日志
    try {
      await logUserAction(Log.ACTIONS.USER_UPDATE, user, req.user, JSON.stringify(oldUserData), req);
    } catch (logError) {
      console.error('记录用户职场更新日志失败:', logError);
      // 继续处理，不影响主流程
    }

    return res.json({
      message: '职场更新成功',
      user: {
        id: user.id,
        username: user.username,
        workplace: user.workplace,
        workplaceId: user.workplaceId
      }
    });
  } catch (error) {
    console.error('更新用户职场失败:', error);
    return res.status(500).json({ message: '更新用户职场失败，请稍后重试' });
  }
};

// 批量删除用户
exports.batchDeleteUsers = async (req, res) => {
  try {
    const { userIds } = req.body;

    // 验证必填字段
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ message: '用户ID列表不能为空' });
    }

    // 检查是否包含当前用户ID
    if (userIds.includes(req.user.id)) {
      return res.status(400).json({ message: '不能删除自己的账户' });
    }

    // 查找所有需要删除的用户
    const users = await User.findAll({
      where: {
        id: {
          [Op.in]: userIds
        }
      }
    });

    if (users.length === 0) {
      return res.status(404).json({ message: '未找到任何要删除的用户' });
    }

    // 保存用户数据用于日志记录
    const usersData = users.map(user => user.get());
    
    // 删除用户
    await User.destroy({
      where: {
        id: {
          [Op.in]: userIds
        }
      }
    });
    
    // 记录批量删除日志
    for (const userData of usersData) {
      try {
        await logUserAction(
          Log.ACTIONS.USER_BULK_DELETE,
          userData,
          req.user,
          JSON.stringify(userData),
          req
        );
      } catch (logError) {
        console.error(`记录用户${userData.id}批量删除日志失败:`, logError);
        // 继续处理，不影响主流程
      }
    }

    return res.json({
      message: `成功删除 ${users.length} 个用户`,
      deletedCount: users.length
    });
  } catch (error) {
    console.error('批量删除用户失败:', error);
    return res.status(500).json({ message: '批量删除用户失败，请稍后重试' });
  }
};

/**
 * 获取用户活跃度数据
 */
exports.getUserActivity = async (req, res) => {
  try {
    console.log('调用用户活跃度数据API');
    
    // 检查是否为管理员
    if (!req.user || req.user.role !== 'admin') {
      console.log('非管理员尝试访问用户活跃度数据');
      return res.status(403).json({ 
        message: '无权访问管理员功能',
        authenticated: !!req.user,
        userRole: req.user ? req.user.role : null
      });
    }
    
    // 获取请求参数
    const period = req.query.period || '30days'; // 默认30天
    
    // 设置日期范围
    let startDate, endDate;
    const now = new Date();
    endDate = now;
    
    switch(period) {
      case '7days':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 30);
        break;
      case '90days':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 90);
        break;
      default:
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 30);
    }
    
    console.log(`查询用户活跃度，时间段:${period}，起始日期:${startDate.toISOString()}, 结束日期:${endDate.toISOString()}`);
    
    // 生成时间区间数组
    const timeSlots = [];
    const dayCount = period === '7days' ? 7 : (period === '30days' ? 30 : 90);
    
    for (let i = 0; i < dayCount; i++) {
      const d = new Date(endDate);
      d.setDate(d.getDate() - (dayCount - 1 - i));
      // 格式为 MM-DD
      const dateStr = `${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
      timeSlots.push({
        date: dateStr,
        start: new Date(d.getFullYear(), d.getMonth(), d.getDate()),
        end: new Date(d.getFullYear(), d.getMonth(), d.getDate(), 23, 59, 59)
      });
    }
    
    // 1. 查询每日活跃用户数（有登录或交易记录）
    const activeUserData = [];
    
    // 查询所有登录日志
    const loginLogs = await db.Log.findAll({
      where: {
        action: 'user_login',
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: ['userId', 'createdAt']
    });
    
    // 查询所有交易记录
    const exchangeLogs = await db.Exchange.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: ['userId', 'createdAt']
    });
    
    // 2. 查询每日新增用户数
    const newUserData = [];
    
    // 获取新用户注册数据
    const newUsers = await User.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: ['id', 'createdAt']
    });
    
    // 统计每个时间段的活跃用户和新增用户
    timeSlots.forEach(slot => {
      // 当天活跃用户ID集合
      const activeUserIds = new Set();
      
      // 筛选当天登录的用户
      loginLogs.forEach(log => {
        const logDate = new Date(log.createdAt);
        if (logDate >= slot.start && logDate <= slot.end && log.userId) {
          activeUserIds.add(log.userId);
        }
      });
      
      // 筛选当天有交易的用户
      exchangeLogs.forEach(exchange => {
        const exchangeDate = new Date(exchange.createdAt);
        if (exchangeDate >= slot.start && exchangeDate <= slot.end && exchange.userId) {
          activeUserIds.add(exchange.userId);
        }
      });
      
      // 筛选当天注册的新用户
      const dailyNewUsers = newUsers.filter(user => {
        const userDate = new Date(user.createdAt);
        return userDate >= slot.start && userDate <= slot.end;
      });
      
      // 保存当天活跃用户数和新增用户数
      activeUserData.push(activeUserIds.size);
      newUserData.push(dailyNewUsers.length);
    });
    
    // 返回结果
    const result = {
      timeLabels: timeSlots.map(slot => slot.date),
      activeUsers: activeUserData,
      newUsers: newUserData,
      period
    };
    
    console.log('用户活跃度数据处理完成');
    return res.json(result);
  } catch (error) {
    console.error('获取用户活跃度数据失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
}; 
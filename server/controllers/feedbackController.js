const db = require('../models');
const { Op } = require('sequelize');
const Feedback = db.Feedback;
const User = db.User;
const { createNotification } = require('./notificationController');

// 获取反馈列表（管理员）
exports.getFeedbackList = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const whereClause = {};
    
    // 状态筛选
    if (req.query.status && ['pending', 'processing', 'resolved', 'rejected'].includes(req.query.status)) {
      whereClause.status = req.query.status;
    }
    
    // 类型筛选
    if (req.query.type && ['general', 'bug', 'feature', 'question'].includes(req.query.type)) {
      whereClause.type = req.query.type;
    }
    
    // 搜索关键词（标题或内容）
    if (req.query.search) {
      whereClause[Op.or] = [
        { title: { [Op.like]: `%${req.query.search}%` } },
        { content: { [Op.like]: `%${req.query.search}%` } }
      ];
    }
    
    const { count, rows } = await Feedback.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
    
    return res.status(200).json({
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
      data: rows
    });
  } catch (error) {
    console.error('获取反馈列表失败:', error);
    return res.status(500).json({ message: '服务器错误，获取反馈列表失败' });
  }
};

// 获取单个反馈详情（管理员）
exports.getFeedbackById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const feedback = await Feedback.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        }
      ]
    });
    
    if (!feedback) {
      return res.status(404).json({ message: '反馈不存在' });
    }
    
    return res.status(200).json(feedback);
  } catch (error) {
    console.error('获取反馈详情失败:', error);
    return res.status(500).json({ message: '服务器错误，获取反馈详情失败' });
  }
};

// 创建反馈（用户）
exports.createFeedback = async (req, res) => {
  try {
    const { title, content, type = 'general' } = req.body;
    const userId = req.user.id; // 从JWT中获取用户ID
    
    // 验证必填字段
    if (!title || !content) {
      return res.status(400).json({ message: '标题和内容为必填项' });
    }
    
    // 验证反馈类型
    if (type && !['general', 'bug', 'feature', 'question'].includes(type)) {
      return res.status(400).json({ message: '无效的反馈类型' });
    }
    
    const feedback = await Feedback.create({
      title,
      content,
      type,
      userId,
      status: 'pending'
    });

    // 运营数据推送：用户反馈通知
    try {
      const operationalNotificationService = require('../services/operationalNotificationService');
      const userWithDetails = await User.findByPk(userId, {
        attributes: ['id', 'username', 'email', 'mobile', 'department', 'departmentPath']
      });
      
      await operationalNotificationService.sendFeedbackAlert(feedback, userWithDetails);
    } catch (alertError) {
      console.error('用户反馈通知发送失败:', alertError.message);
      // 不影响主流程
    }
    
    return res.status(201).json({
      message: '反馈提交成功',
      feedback
    });
  } catch (error) {
    console.error('创建反馈失败:', error);
    return res.status(500).json({ message: '服务器错误，创建反馈失败' });
  }
};

// 更新反馈状态和回复（管理员）
exports.updateFeedback = async (req, res) => {
  try {
    const id = req.params.id;
    const { status, adminReply } = req.body;
    
    console.log('更新反馈状态，ID:', id);
    console.log('用户权限信息:', { 
      id: req.user.id, 
      username: req.user.username, 
      role: req.user.role, 
      isAdmin: req.user.isAdmin,
      hasAdminRole: req.user.role === 'admin'
    });
    
    // 额外权限验证，确保只有管理员可以更新反馈
    if (req.user.role !== 'admin') {
      console.error('非管理员尝试更新反馈:', req.user.username);
      return res.status(403).json({ 
        message: '需要管理员权限',
        details: '只有管理员可以回复和更新反馈状态'
      });
    }
    
    // 验证状态值
    if (status && !['pending', 'processing', 'completed'].includes(status)) {
      return res.status(400).json({ message: '无效的状态值' });
    }
    
    const feedback = await Feedback.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        }
      ]
    });
    
    if (!feedback) {
      return res.status(404).json({ message: '反馈不存在' });
    }
    
    // 记录旧值，用于检测变化
    const oldAdminReply = feedback.adminReply;
    const oldStatus = feedback.status;
    
    // 更新字段
    const updateData = {};
    if (status) updateData.status = status;
    if (adminReply) {
      updateData.adminReply = adminReply;
    }
    
    await feedback.update(updateData);
    
    // 如果管理员添加了回复或者状态变更，发送通知给用户
    if ((adminReply && adminReply !== oldAdminReply) || (status && status !== oldStatus)) {
      try {
        // 创建通知
        await createNotification({
          type: 'feedback',
          sourceId: feedback.id,
          title: '您的反馈有了新回复',
          content: adminReply 
            ? `您的反馈「${feedback.title}」已收到回复: ${adminReply.substring(0, 100)}${adminReply.length > 100 ? '...' : ''}`
            : `您的反馈「${feedback.title}」状态已更新为: ${getStatusText(status)}`,
          recipientId: feedback.user.id
        });
      } catch (error) {
        console.error('创建反馈回复通知失败:', error);
        // 不阻止主流程
      }
    }
    
    console.log('反馈更新成功，ID:', id);
    return res.json({
      message: '反馈更新成功',
      feedback
    });
  } catch (error) {
    console.error('更新反馈失败:', error);
    return res.status(500).json({ message: '更新反馈失败，请稍后重试' });
  }
};

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成'
  };
  return statusMap[status] || status;
}

// 获取用户自己的反馈列表
exports.getUserFeedback = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Feedback.findAndCountAll({
      where: { userId: req.user.id },
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    return res.status(200).json({
      success: true,
      data: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取用户反馈列表失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后再试' });
  }
};

// 用户提交反馈
exports.submitFeedback = async (req, res) => {
  try {
    const { type, title, content } = req.body;
    const userId = req.user.id;

    // 验证必填字段
    if (!type || !title || !content) {
      return res.status(400).json({ message: '请提供完整的反馈信息' });
    }

    // 验证反馈类型
    if (!['product', 'feature', 'bug', 'other'].includes(type)) {
      return res.status(400).json({ message: '无效的反馈类型' });
    }

    // 创建反馈
    const feedback = await Feedback.create({
      userId,
      type,
      title,
      content,
      status: 'pending'  // 默认为待处理状态
    });

    // 为所有管理员创建通知
    const admins = await User.findAll({
      where: { role: 'admin' }
    });
    
    const notificationPromises = [];
    for (const admin of admins) {
      notificationPromises.push(createNotification({
        type: 'feedback',
        sourceId: feedback.id,
        title: '新用户反馈',
        content: `用户 ${req.user.username} 提交了新的反馈: ${feedback.title}`,
        recipientId: admin.id
      }));
    }
    
    // 使用Promise.all等待所有通知创建完成
    await Promise.all(notificationPromises);

    return res.status(201).json({
      message: '反馈提交成功',
      feedback
    });
  } catch (error) {
    console.error('提交反馈失败:', error);
    return res.status(500).json({ message: '提交反馈失败，请稍后重试' });
  }
};

// 获取当前用户的反馈列表
exports.getUserFeedbacks = async (req, res) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;

    const { count, rows } = await Feedback.findAndCountAll({
      where: { userId },
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username']
        }
      ]
    });

    return res.json({
      data: rows,
      total: count,
      page,
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取用户反馈列表失败:', error);
    return res.status(500).json({ message: '获取反馈列表失败，请稍后重试' });
  }
};

// 获取所有反馈列表（管理员可用）
exports.getAllFeedbacks = async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const status = req.query.status;
    const type = req.query.type;
    const search = req.query.search;

    // 构建查询条件
    const where = {};
    if (status) {
      where.status = status;
    }
    if (type) {
      where.type = type;
    }
    if (search) {
      where[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { content: { [Op.like]: `%${search}%` } }
      ];
    }

    console.log('获取所有反馈列表，查询条件:', JSON.stringify(where));
    console.log('分页信息:', { page, limit, offset });
    console.log('用户权限信息:', { 
      id: req.user.id,
      username: req.user.username,
      role: req.user.role,
      isAdmin: req.user.isAdmin,
      hasPermission: req.user.role === 'admin'
    });

    // 额外权限验证，确保只有管理员可以访问
    if (req.user.role !== 'admin') {
      console.error('非管理员尝试访问所有反馈列表:', req.user.username);
      return res.status(403).json({ 
        message: '需要管理员权限',
        details: '只有管理员可以查看所有反馈'
      });
    }

    const { count, rows } = await Feedback.findAndCountAll({
      where,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username']
        }
      ]
    });

    return res.json({
      data: rows,
      total: count,
      page,
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取反馈列表失败:', error);
    return res.status(500).json({ message: '获取反馈列表失败，请稍后重试' });
  }
};

// 获取反馈详情
exports.getFeedbackDetail = async (req, res) => {
  try {
    const id = req.params.id;
    
    console.log('获取反馈详情，ID:', id);
    console.log('用户权限信息:', { 
      id: req.user.id, 
      username: req.user.username, 
      role: req.user.role, 
      isAdmin: req.user.isAdmin,
      hasAdminRole: req.user.role === 'admin'
    });
    
    // 验证ID格式
    if (!id || isNaN(Number(id))) {
      console.log('无效的反馈ID:', id);
      return res.status(400).json({ message: '无效的反馈ID' });
    }
    
    const feedback = await Feedback.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username']
        }
      ]
    });

    if (!feedback) {
      console.log('找不到反馈，ID:', id);
      return res.status(404).json({ message: '反馈不存在' });
    }
    
    console.log('找到反馈:', {
      id: feedback.id,
      title: feedback.title,
      userId: feedback.userId,
      type: feedback.type,
      status: feedback.status
    });

    // 管理员或所有者可以查看
    const isAdmin = req.user.role === 'admin'; // 只使用role字段判断
    const isOwner = parseInt(feedback.userId, 10) === parseInt(req.user.id, 10);
    
    console.log('权限检查:', {
      isAdmin,
      isOwner,
      feedbackUserId: parseInt(feedback.userId, 10),
      requestUserId: parseInt(req.user.id, 10),
      canAccess: isAdmin || isOwner
    });
    
    if (!isAdmin && !isOwner) {
      console.log('权限检查失败: 用户无权访问此反馈');
      return res.status(403).json({ 
        message: '无权查看该反馈',
        details: '只有管理员或反馈创建者可以查看反馈详情'
      });
    }

    console.log('权限检查通过，返回反馈详情');
    return res.json(feedback);
  } catch (error) {
    console.error('获取反馈详情失败:', error);
    return res.status(500).json({ message: '获取反馈详情失败，请稍后重试' });
  }
}; 
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit');
const moment = require('moment');
const { sequelize, Op } = require('../config/database');

// 数据库模型
const Exchange = require('../models/exchange');
const Product = require('../models/product');
const User = require('../models/user');
const Category = require('../models/category');

/**
 * 导出仪表盘数据为Excel或PDF
 */
exports.exportDashboardData = async (req, res) => {
  try {
    const { format, period } = req.query;
    
    // 日志请求信息
    console.log(`[ExportController] 导出仪表盘数据，格式: ${format}, 周期: ${period || 'month'}, 用户: ${req.user?.username || '未知用户'}`);

    // 验证格式参数
    if (!format || (format !== 'excel' && format !== 'pdf')) {
      console.error(`[ExportController] 无效的导出格式: ${format}`);
      return res.status(400).json({ success: false, message: '无效的导出格式' });
    }

    // 验证周期参数
    const validPeriods = ['week', 'month', 'year'];
    const normalizedPeriod = period || 'month';
    if (!validPeriods.includes(normalizedPeriod)) {
      console.warn(`[ExportController] 无效的周期: ${normalizedPeriod}，将使用默认值"month"`);
    }

    // 获取仪表盘综合数据
    console.log(`[ExportController] 开始获取仪表盘数据，周期: ${normalizedPeriod}`);
    let dashboardData;
    try {
      dashboardData = await getDashboardData(normalizedPeriod);
      console.log('[ExportController] 仪表盘数据获取成功');
    } catch (dataError) {
      console.error('[ExportController] 获取仪表盘数据失败:', dataError);
      return res.status(500).json({ 
        success: false, 
        message: '获取仪表盘数据失败', 
        error: dataError.message 
      });
    }
    
    // 根据请求的格式导出数据
    console.log(`[ExportController] 开始导出${format === 'excel' ? 'Excel' : 'PDF'}数据`);
    try {
    if (format === 'excel') {
        await exportToExcel(res, dashboardData, normalizedPeriod);
    } else {
        await exportToPDF(res, dashboardData, normalizedPeriod);
      }
      console.log(`[ExportController] ${format === 'excel' ? 'Excel' : 'PDF'}数据导出成功`);
    } catch (exportError) {
      console.error(`[ExportController] ${format === 'excel' ? 'Excel' : 'PDF'}数据导出失败:`, exportError);
      // 如果响应头尚未发送，则返回错误信息
      if (!res.headersSent) {
        return res.status(500).json({ 
          success: false, 
          message: `导出${format === 'excel' ? 'Excel' : 'PDF'}数据失败`, 
          error: exportError.message 
        });
      } else {
        // 如果已经发送了部分响应，则尝试结束流
        try {
          res.end();
        } catch (endError) {
          console.error('[ExportController] 结束响应失败:', endError);
        }
      }
    }
  } catch (error) {
    console.error('[ExportController] 导出仪表盘数据出错:', error);
    // 如果响应头尚未发送，则返回错误信息
    if (!res.headersSent) {
      return res.status(500).json({ 
        success: false, 
        message: '导出数据失败', 
        error: error.message || '服务器内部错误' 
      });
    } else {
      // 如果已经发送了部分响应，则尝试结束流
      try {
        res.end();
      } catch (endError) {
        console.error('[ExportController] 结束响应失败:', endError);
      }
    }
  }
};

/**
 * 获取仪表盘综合数据
 */
async function getDashboardData(period = 'month') {
  try {
    console.log(`[ExportController] getDashboardData - 开始获取仪表盘数据，期间: ${period}`);
    
    // 获取实际统计数据（不再使用测试数据）
    const statsData = await getStatsData();
    console.log('[ExportController] 仪表盘数据获取成功');
    
    // 获取销售趋势数据
    const salesTrendData = await getSalesTrendData(period);
    console.log('[ExportController] 成功获取销售趋势数据');
    
    // 获取分类数据
    const categoryData = await getCategoryData();
    console.log('[ExportController] 成功获取分类数据');
    
    // 获取最近订单
    const recentOrders = await getRecentOrders();
    console.log('[ExportController] 成功获取最近订单数据');
    
    console.log('[ExportController] getDashboardData - 成功获取所有实际数据');
    
    return {
      stats: statsData,
      salesTrend: salesTrendData,
      categories: categoryData,
      recentOrders: recentOrders
    };
  } catch (error) {
    console.error('[ExportController] getDashboardData - 获取仪表盘数据出错:', error);
    // 发生错误时使用测试数据
    console.warn('[ExportController] 获取实际数据失败，使用备用测试数据');
    
    // 备用测试数据
    const statsData = {
      totalSales: { value: 1258, growth: 15, trend: 'up' },
      rmbSales: { value: 25680.50, growth: 12, trend: 'up' },
      lySales: { value: 3750, growth: 8, trend: 'up' },
      activeUsers: { value: 156, growth: 25, trend: 'up' },
      totalProducts: { value: 89, growth: 5, trend: 'up' },
      totalFeedbacks: { value: 45, growth: 10, trend: 'up' }
    };
    
    // 备用销售趋势数据
    const salesTrendData = {
      timeLabels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
      rmbSalesData: [3500, 4200, 3800, 5500, 7000, 6200, 8500],
      lySalesData: [250, 360, 420, 380, 550, 480, 680],
      orderData: [25, 32, 28, 35, 42, 38, 50],
      period
    };
    
    // 备用分类数据
    const categoryData = [
      { name: '电子产品', productCount: 35 },
      { name: '办公用品', productCount: 22 },
      { name: '书籍', productCount: 18 },
      { name: '服装', productCount: 14 }
    ];
    
    // 备用最近订单
    const recentOrders = [
      {
        id: '202405001',
        date: '2024-05-10',
        customer: '张三',
        productName: 'MacBook Pro',
        amount: 11999,
        paymentMethod: 'rmb',
        status: '已完成',
        quantity: 1
      },
      {
        id: '202405002',
        date: '2024-05-11',
        customer: '李四',
        productName: 'AirPods Pro',
        amount: 1999,
        paymentMethod: 'rmb',
        status: '已发货',
        quantity: 1
      }
    ];
    
    return {
      stats: statsData,
      salesTrend: salesTrendData,
      categories: categoryData,
      recentOrders: recentOrders
    };
  }
}

/**
 * 获取统计数据
 */
async function getStatsData() {
  try {
    console.log('[ExportController] 开始获取统计数据');
    
    // 计算30天前的日期
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // 计算60天前的日期（用于计算环比增长）
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    
    // 获取订单总数
    const totalSalesResult = await Exchange.count({
      where: {
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        }
      }
    });
    const totalSales = totalSalesResult || 0;
    console.log('[ExportController] 总订单数:', totalSales);
    
    // 最近30天的订单
    const lastMonthSalesResult = await Exchange.count({
      where: {
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [sequelize.Op.gte]: thirtyDaysAgo
        }
      }
    });
    const lastMonthSales = lastMonthSalesResult || 0;
    
    // 30-60天前的订单
    const previousMonthSalesResult = await Exchange.count({
      where: {
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [sequelize.Op.gte]: sixtyDaysAgo,
          [sequelize.Op.lt]: thirtyDaysAgo
        }
      }
    });
    const previousMonthSales = previousMonthSalesResult || 0;
    
    // 计算订单增长率
    const salesGrowth = previousMonthSales === 0 
      ? 100 
      : Math.round(((lastMonthSales - previousMonthSales) / previousMonthSales) * 100);
    
    // 获取人民币收入
    const rmbSalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'rmb',
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        }
      },
      raw: true
    });
    const totalRmbSales = (rmbSalesResult && rmbSalesResult.total) ? parseFloat(rmbSalesResult.total) : 0;
    console.log('[ExportController] 人民币收入:', totalRmbSales);
    
    // 最近30天的人民币收入
    const lastMonthRmbResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'rmb',
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [sequelize.Op.gte]: thirtyDaysAgo
        }
      },
      raw: true
    });
    const lastMonthRmbSales = (lastMonthRmbResult && lastMonthRmbResult.total) ? parseFloat(lastMonthRmbResult.total) : 0;
    
    // 30-60天前的人民币收入
    const previousMonthRmbResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'rmb',
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [sequelize.Op.gte]: sixtyDaysAgo,
          [sequelize.Op.lt]: thirtyDaysAgo
        }
      },
      raw: true
    });
    const previousMonthRmbSales = (previousMonthRmbResult && previousMonthRmbResult.total) ? parseFloat(previousMonthRmbResult.total) : 0;
    
    // 计算人民币收入增长率
    const rmbGrowth = previousMonthRmbSales === 0 
      ? 100 
      : Math.round(((lastMonthRmbSales - previousMonthRmbSales) / previousMonthRmbSales) * 100);
    
    // 获取光年币收入
    const lySalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'ly',
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        }
      },
      raw: true
    });
    const totalLySales = (lySalesResult && lySalesResult.total) ? parseFloat(lySalesResult.total) : 0;
    console.log('[ExportController] 光年币收入:', totalLySales);
    
    // 最近30天的光年币收入
    const lastMonthLyResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'ly',
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [sequelize.Op.gte]: thirtyDaysAgo
        }
      },
      raw: true
    });
    const lastMonthLySales = (lastMonthLyResult && lastMonthLyResult.total) ? parseFloat(lastMonthLyResult.total) : 0;
    
    // 30-60天前的光年币收入
    const previousMonthLyResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'ly',
        status: {
          [sequelize.Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [sequelize.Op.gte]: sixtyDaysAgo,
          [sequelize.Op.lt]: thirtyDaysAgo
        }
      },
      raw: true
    });
    const previousMonthLySales = (previousMonthLyResult && previousMonthLyResult.total) ? parseFloat(previousMonthLyResult.total) : 0;
    
    // 计算光年币收入增长率
    const lyGrowth = previousMonthLySales === 0 
      ? 100 
      : Math.round(((lastMonthLySales - previousMonthLySales) / previousMonthLySales) * 100);
    
    // 获取活跃用户数
    const activeUsersResult = await User.count({
      where: {
        lastLoginAt: {
          [sequelize.Op.gte]: thirtyDaysAgo
        }
      }
    });
    const activeUsers = activeUsersResult || 0;
    
    // 获取前一个月的活跃用户数
    const previousMonthActiveUsersResult = await User.count({
      where: {
        lastLoginAt: {
          [sequelize.Op.gte]: sixtyDaysAgo,
          [sequelize.Op.lt]: thirtyDaysAgo
        }
      }
    });
    const previousMonthActiveUsers = previousMonthActiveUsersResult || 0;
    
    // 计算用户增长率
    const userGrowth = previousMonthActiveUsers === 0 
      ? 100 
      : Math.round(((activeUsers - previousMonthActiveUsers) / previousMonthActiveUsers) * 100);
    
    // 获取商品总数
    const totalProductsResult = await Product.count();
    const totalProducts = totalProductsResult || 0;
    
    console.log('[ExportController] 统计数据获取完成');
    
    // 构建返回对象
    return {
      totalSales: {
        value: totalSales,
        growth: salesGrowth,
        trend: salesGrowth >= 0 ? 'up' : 'down'
      },
      rmbSales: {
        value: totalRmbSales,
        growth: rmbGrowth,
        trend: rmbGrowth >= 0 ? 'up' : 'down'
      },
      lySales: {
        value: totalLySales,
        growth: lyGrowth,
        trend: lyGrowth >= 0 ? 'up' : 'down'
      },
      activeUsers: {
        value: activeUsers,
        growth: userGrowth,
        trend: userGrowth >= 0 ? 'up' : 'down'
      },
      totalProducts: {
        value: totalProducts,
        growth: 0,
        trend: 'up'
      }
    };
  } catch (error) {
    console.error('[ExportController] 获取统计数据出错:', error);
    // 发生错误时返回一个基本的数据结构，避免完全失败
    return {
      totalSales: { value: 0, growth: 0, trend: 'up' },
      rmbSales: { value: 0, growth: 0, trend: 'up' },
      lySales: { value: 0, growth: 0, trend: 'up' },
      activeUsers: { value: 0, growth: 0, trend: 'up' },
      totalProducts: { value: 0, growth: 0, trend: 'up' }
    };
  }
}

/**
 * 获取销售趋势数据
 */
async function getSalesTrendData(period = 'month') {
  try {
    console.log('[ExportController] 开始获取销售趋势数据，周期:', period);
    let timeLabels = [];
    let dateFormat;
    let startDate;
    let endDate = new Date();
    
    // 设置时间范围和格式
    switch (period) {
      case 'week':
        // 过去7天
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
        dateFormat = 'MM-DD';
        
        // 生成日期标签
        for (let i = 0; i < 7; i++) {
          const date = new Date(startDate);
          date.setDate(date.getDate() + i);
          timeLabels.push(moment(date).format(dateFormat));
        }
        break;
        
      case 'month':
        // 过去30天
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        dateFormat = 'MM-DD';
        
        // 生成日期标签
        for (let i = 0; i < 30; i++) {
          const date = new Date(startDate);
          date.setDate(date.getDate() + i);
          timeLabels.push(moment(date).format(dateFormat));
        }
        break;
        
      case 'year':
        // 过去12个月
        startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 11);
        startDate.setDate(1);
        dateFormat = 'YYYY-MM';
        
        // 生成月份标签
        for (let i = 0; i < 12; i++) {
          const date = new Date(startDate);
          date.setMonth(date.getMonth() + i);
          timeLabels.push(moment(date).format(dateFormat));
        }
        break;
        
      default:
        // 默认过去30天
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        dateFormat = 'MM-DD';
        
        // 生成日期标签
        for (let i = 0; i < 30; i++) {
          const date = new Date(startDate);
          date.setDate(date.getDate() + i);
          timeLabels.push(moment(date).format(dateFormat));
        }
    }
    
    console.log('[ExportController] 生成的时间标签:', timeLabels.length);
    
    // 每日数据初始化
    const rmbSalesMap = new Map();
    const lySalesMap = new Map();
    const orderMap = new Map();
    
    // 初始化映射
    timeLabels.forEach(label => {
      rmbSalesMap.set(label, 0);
      lySalesMap.set(label, 0);
      orderMap.set(label, 0);
    });
    
    try {
      // 获取所有相关期间的订单
      const orders = await Exchange.findAll({
        where: {
          createdAt: {
            [Op.between]: [startDate, endDate]
          }
        },
        attributes: ['id', 'createdAt', 'paymentMethod', 'totalAmount', 'status']
      });
      
      console.log('[ExportController] 获取到订单数:', orders.length);
      
      // 处理订单数据
      orders.forEach(order => {
        const date = moment(order.createdAt).format(dateFormat);
        
        // 增加订单计数
        if (timeLabels.includes(date)) {
          orderMap.set(date, (orderMap.get(date) || 0) + 1);
          
          // 计算销售额
          if (['approved', 'shipped', 'completed'].includes(order.status)) {
            if (order.paymentMethod === 'rmb') {
              rmbSalesMap.set(date, (rmbSalesMap.get(date) || 0) + parseFloat(order.totalAmount || 0));
            } else if (order.paymentMethod === 'ly') {
              lySalesMap.set(date, (lySalesMap.get(date) || 0) + parseFloat(order.totalAmount || 0));
            }
          }
        }
      });
    } catch (err) {
      console.error('[ExportController] 获取订单数据出错:', err);
    }
    
    // 按照时间标签构建数据数组
    const rmbSeries = timeLabels.map(label => rmbSalesMap.get(label) || 0);
    const lySeries = timeLabels.map(label => lySalesMap.get(label) || 0);
    const orderSeries = timeLabels.map(label => orderMap.get(label) || 0);
    
    console.log('[ExportController] 销售趋势数据获取完成');
    
    return {
      timeLabels,
      rmbSalesData: rmbSeries,
      lySalesData: lySeries,
      orderData: orderSeries,
      period
    };
  } catch (error) {
    console.error('[ExportController] 获取销售趋势数据出错:', error);
    // 发生错误时返回一个基本的数据结构
    return {
      timeLabels: ['1月', '2月', '3月', '4月', '5月'],
      rmbSalesData: [0, 0, 0, 0, 0],
      lySalesData: [0, 0, 0, 0, 0],
      orderData: [0, 0, 0, 0, 0],
      period
    };
  }
}

/**
 * 获取分类数据
 */
async function getCategoryData() {
  try {
    console.log('[ExportController] 开始获取分类数据');
    const categories = await Category.findAll();
    
    // 获取每个分类下的商品数量
    const result = await Promise.all(categories.map(async (category) => {
      const productCount = await Product.count({
        where: { categoryId: category.id }
      });
      return {
        id: category.id,
        name: category.name,
        productCount
      };
    }));
    
    console.log('[ExportController] 分类数据获取完成，共', result.length, '个分类');
    return result;
  } catch (error) {
    console.error('[ExportController] 获取分类数据出错:', error);
    // 发生错误时返回空数组
    return [];
  }
}

/**
 * 获取最近订单
 */
async function getRecentOrders() {
  try {
    console.log('[ExportController] 开始获取最近订单');
    // 获取最近10个订单
    const orders = await Exchange.findAll({
      order: [['createdAt', 'DESC']],
      limit: 10,
      include: [
        { model: Product, as: 'product', attributes: ['name'] },
        { model: User, as: 'user', attributes: ['username'] }
      ]
    });
      
    const formattedOrders = orders.map(order => {
      return {
        id: order.id,
        date: moment(order.createdAt).format('YYYY-MM-DD'),
        customer: order.user ? order.user.username : '未知用户',
        productName: order.product ? order.product.name : '未知商品',
        amount: order.totalAmount,
        paymentMethod: order.paymentMethod,
        status: order.status,
        quantity: order.quantity || 1
      };
    });
    
    console.log('[ExportController] 最近订单数据获取完成，共', formattedOrders.length, '条订单');
    return formattedOrders;
  } catch (error) {
    console.error('[ExportController] 获取最近订单出错:', error);
    // 发生错误时返回空数组
    return [];
  }
}

/**
 * 导出仪表盘数据为Excel
 */
async function exportToExcel(res, data, period) {
  try {
    console.log('[ExportController] 开始导出Excel数据, 期间:', period);
    
    // 验证输入数据
    if (!data) {
      console.error('[ExportController] 数据为空');
      throw new Error('导出数据为空');
    }
    
    // 设置响应头
    console.log('[ExportController] 设置响应头');
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=dashboard-${period}-${moment().format('YYYY-MM-DD')}.xlsx`);
    
    console.log('[ExportController] 创建工作簿');
    const workbook = new ExcelJS.Workbook();
    workbook.creator = '光年汇兑系统';
    workbook.created = new Date();
    
    // 添加统计数据工作表
    console.log('[ExportController] 添加统计数据工作表');
    const statsSheet = workbook.addWorksheet('统计数据');
    statsSheet.columns = [
      { header: '指标', key: 'metric' },
      { header: '数值', key: 'value' },
      { header: '增长率', key: 'growth' },
      { header: '趋势', key: 'trend' }
    ];
    
    console.log('[ExportController] 验证stats数据');
    if (!data.stats) {
      console.error('[ExportController] stats数据为空');
      throw new Error('统计数据为空');
    }
    
    // 添加统计数据行
    console.log('[ExportController] 添加统计数据行');
    try {
    statsSheet.addRow({
      metric: '总订单数',
        value: data.stats.totalSales?.value || 0,
        growth: `${data.stats.totalSales?.growth || 0}%`,
        trend: (data.stats.totalSales?.trend === 'up') ? '上升' : '下降'
    });
    
    statsSheet.addRow({
      metric: '人民币收入',
        value: `¥${(data.stats.rmbSales?.value || 0).toFixed(2)}`,
        growth: `${data.stats.rmbSales?.growth || 0}%`,
        trend: (data.stats.rmbSales?.trend === 'up') ? '上升' : '下降'
    });
    
    statsSheet.addRow({
      metric: '光年币收入',
        value: `${data.stats.lySales?.value || 0} 个`,
        growth: `${data.stats.lySales?.growth || 0}%`,
        trend: (data.stats.lySales?.trend === 'up') ? '上升' : '下降'
    });
    
    statsSheet.addRow({
      metric: '活跃用户',
        value: data.stats.activeUsers?.value || 0,
        growth: `${data.stats.activeUsers?.growth || 0}%`,
        trend: (data.stats.activeUsers?.trend === 'up') ? '上升' : '下降'
    });
    
    statsSheet.addRow({
      metric: '商品总数',
        value: data.stats.totalProducts?.value || 0,
        growth: `${data.stats.totalProducts?.growth || 0}%`,
        trend: (data.stats.totalProducts?.trend === 'up') ? '上升' : '下降'
    });
    } catch (statsError) {
      console.error('[ExportController] 添加统计数据行失败:', statsError);
      throw new Error(`添加统计数据行失败: ${statsError.message}`);
    }
    
    // 设置列宽
    console.log('[ExportController] 设置统计表列宽');
    statsSheet.getColumn('metric').width = 15;
    statsSheet.getColumn('value').width = 15;
    statsSheet.getColumn('growth').width = 15;
    statsSheet.getColumn('trend').width = 15;
    
    // 添加销售趋势工作表
    console.log('[ExportController] 添加销售趋势工作表');
    const trendsSheet = workbook.addWorksheet('销售趋势');
    
    // 设置列标题
    const trendColumns = [
      { header: '日期', key: 'date' },
      { header: '人民币收入', key: 'rmb' },
      { header: '光年币收入', key: 'ly' },
      { header: '订单数量', key: 'orders' }
    ];
    trendsSheet.columns = trendColumns;
    
    console.log('[ExportController] 验证销售趋势数据');
    if (!data.salesTrend || !data.salesTrend.timeLabels) {
      console.warn('[ExportController] 销售趋势数据为空或格式错误，使用默认空数据');
      data.salesTrend = {
        timeLabels: [],
        rmbSalesData: [],
        lySalesData: [],
        orderData: []
      };
    }
    
    // 添加销售趋势数据
    console.log('[ExportController] 添加销售趋势数据');
    try {
      if (data.salesTrend.timeLabels.length > 0) {
    for (let i = 0; i < data.salesTrend.timeLabels.length; i++) {
      trendsSheet.addRow({
        date: data.salesTrend.timeLabels[i],
            rmb: data.salesTrend.rmbSalesData?.[i] || 0,
            ly: data.salesTrend.lySalesData?.[i] || 0,
            orders: data.salesTrend.orderData?.[i] || 0
      });
    }
      } else {
        // 添加示例数据
        trendsSheet.addRow({
          date: '示例数据',
          rmb: 0,
          ly: 0,
          orders: 0
        });
      }
    } catch (trendsError) {
      console.error('[ExportController] 添加销售趋势数据失败:', trendsError);
      throw new Error(`添加销售趋势数据失败: ${trendsError.message}`);
    }
    
    // 写入响应
    console.log('[ExportController] 写入Excel数据到响应');
    try {
    await workbook.xlsx.write(res);
      console.log('[ExportController] Excel数据写入成功');
    res.end();
    } catch (writeError) {
      console.error('[ExportController] 写入Excel数据失败:', writeError);
      throw new Error(`写入Excel数据失败: ${writeError.message}`);
    }
  } catch (error) {
    console.error('[ExportController] 导出Excel失败:', error);
    if (!res.headersSent) {
      res.status(500).json({ 
        success: false, 
        message: '导出Excel数据失败', 
        error: error.message 
      });
    } else {
      try {
        res.end();
      } catch (endError) {
        console.error('[ExportController] 结束响应失败:', endError);
      }
    }
  }
}

/**
 * 导出为PDF文件
 */
async function exportToPDF(res, data, period) {
  let doc = null;
  
  try {
    console.log('[ExportController] 开始导出PDF数据, 期间:', period);
    
    // 验证输入数据
    if (!data) {
      console.error('[ExportController] 数据为空');
      throw new Error('导出数据为空');
    }
    
    // 设置响应头
    console.log('[ExportController] 设置响应头');
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=dashboard-${period}-${moment().format('YYYY-MM-DD')}.pdf`);
    
    // 创建PDF文档 - 使用A4横向格式以获得更好的图表显示效果
    console.log('[ExportController] 创建PDF文档');
    doc = new PDFDocument({
      size: 'A4',
      layout: 'landscape', // 横向布局
      margin: 40,
      info: {
        Title: '光年汇兑系统仪表盘报告',
        Author: '光年汇兑系统',
        Subject: '数据分析报告',
        Keywords: '仪表盘,数据分析,报表',
        CreationDate: new Date()
      }
    });
    
    // 将PDF流式写入响应
    console.log('[ExportController] 将PDF流式写入响应');
    doc.pipe(res);
    
    // 确保字体目录存在
    const fontsDirPath = path.join(__dirname, '../assets/fonts');
    try {
      if (!fs.existsSync(path.join(__dirname, '../assets'))) {
        fs.mkdirSync(path.join(__dirname, '../assets'), { recursive: true });
        console.log('[ExportController] 创建assets目录成功');
      }
      
      if (!fs.existsSync(fontsDirPath)) {
        fs.mkdirSync(fontsDirPath, { recursive: true });
        console.log('[ExportController] 创建fonts目录成功');
      } else {
        console.log('[ExportController] fonts目录已存在');
        // 列出目录中的文件
        const fontFiles = fs.readdirSync(fontsDirPath);
        console.log('[ExportController] 可用字体文件:', fontFiles);
      }
    } catch (dirError) {
      console.error('[ExportController] 创建字体目录失败:', dirError);
    }

    // 尝试设置中文字体
    try {
      // 优先使用TTF格式文件，避免TTC格式兼容性问题
      const fontPath = path.join(__dirname, '../assets/fonts/wqy-zenhei.ttf');
      console.log('[ExportController] 尝试加载TTF字体文件:', fontPath);
      
      if (fs.existsSync(fontPath)) {
        try {
          doc.registerFont('WenQuanYi', fontPath);
          doc.font('WenQuanYi');
          console.log('[ExportController] 成功加载文泉驿正黑中文字体(TTF)');
        } catch (regError) {
          console.error('[ExportController] 注册文泉驿字体失败，详细错误:', regError);
          console.error('[ExportController] 错误堆栈:', regError.stack);
          
          // 尝试直接使用系统内置的通用字体
          try {
            doc.font('Helvetica');
            console.log('[ExportController] 使用Helvetica作为回退字体');
          } catch (fallbackError) {
            console.error('[ExportController] 回退字体设置失败:', fallbackError);
          }
        }
      } else {
        // 找不到字体文件时使用默认字体
        console.warn('[ExportController] 找不到文泉驿TTF字体文件，将使用默认字体');
        
        // 对于PDF.js，提供一个回退机制
        try {
          // 尝试使用系统内置的通用字体
          doc.font('Helvetica');
          console.log('[ExportController] 使用Helvetica作为回退字体');
        } catch (fallbackError) {
          console.error('[ExportController] 回退字体设置失败:', fallbackError);
        }
      }
    } catch (fontError) {
      console.error('[ExportController] 加载字体出错:', fontError);
      console.error('[ExportController] 错误堆栈:', fontError.stack);
      // 出错时也提供回退机制
      try {
        doc.font('Helvetica');
        console.log('[ExportController] 使用Helvetica作为回退字体');
      } catch (e) {
        console.error('[ExportController] 无法设置任何字体:', e);
      }
    }
    
    // 添加页眉
    console.log('[ExportController] 添加页眉');
    doc.fontSize(24)
       .fillColor('#333333');
    safeTextHandling(doc, '光年汇兑系统 - 仪表盘报告', { align: 'center' });
       
    doc.moveDown(0.5);
    doc.fontSize(12)
       .fillColor('#666666');
    safeTextHandling(doc, `报表生成时间: ${moment().format('YYYY-MM-DD HH:mm:ss')} | 数据周期: ${getPeriodName(period)}`, { align: 'center' });
       
    doc.moveDown(1);
    
    // 绘制统计卡片
    console.log('[ExportController] 绘制统计卡片');
    drawStatCards(doc, data.stats);
    
    // 添加页面分隔线
    doc.moveDown(1);
    drawHorizontalLine(doc);
    doc.moveDown(1);
    
    // 绘制销售趋势图
    console.log('[ExportController] 绘制销售趋势图');
    drawSalesTrend(doc, data.salesTrend);
    
    // 添加新页面
    doc.addPage();
    
    // 绘制分类饼图
    console.log('[ExportController] 绘制分类饼图');
    drawCategoryPie(doc, data.categories);
    
    // 绘制最近订单表格
    console.log('[ExportController] 绘制最近订单表格');
    doc.moveDown(1);
    drawRecentOrders(doc, data.recentOrders);
    
    // 添加页脚
    drawFooter(doc);
    
    // 结束文档
    console.log('[ExportController] 结束PDF文档');
    doc.end();
    console.log('[ExportController] PDF生成完成');
    
  } catch (error) {
    console.error('[ExportController] 导出PDF失败:', error);
    
    // 清理资源
    if (doc) {
      try {
        doc.end();
      } catch (docError) {
        console.error('[ExportController] 关闭PDF文档失败:', docError);
      }
    }
    
    // 如果响应已经发送了部分内容，则不能再发送错误响应
    if (!res.headersSent) {
      res.status(500).json({ 
        success: false, 
        message: '导出PDF数据失败', 
        error: error.message 
      });
    } else {
      // 尝试结束响应
      try {
        res.end();
      } catch (endError) {
        console.error('[ExportController] 结束响应失败:', endError);
      }
    }
  }
}

/**
 * 绘制水平分隔线
 */
function drawHorizontalLine(doc) {
  const width = doc.page.width - 2 * doc.page.margins.left;
  doc.moveTo(doc.page.margins.left, doc.y)
     .lineTo(doc.page.margins.left + width, doc.y)
     .strokeColor('#cccccc')
     .lineWidth(1)
     .stroke();
}

/**
 * 获取周期名称
 */
function getPeriodName(period) {
  const periodMap = {
    'week': '最近一周',
    'month': '最近一月',
    'year': '最近一年',
    '7days': '最近7天',
    '30days': '最近30天',
    '90days': '最近90天'
  };
  return periodMap[period] || period;
}

/**
 * 绘制统计卡片
 */
function drawStatCards(doc, stats) {
  if (!stats) {
    doc.fontSize(14);
    safeTextHandling(doc, '暂无统计数据', { align: 'center' });
    return;
  }
  
  console.log('[ExportController] 绘制统计卡片, 数据:', JSON.stringify(stats));
  
  const cardsPerRow = 3;
  const cardWidth = (doc.page.width - 2 * doc.page.margins.left - (cardsPerRow - 1) * 20) / cardsPerRow;
  const cardHeight = 100;
  const startY = doc.y;
  
  // 卡片配置
  const cards = [
    { 
      label: '总订单数', 
      value: stats.totalSales?.value || 0,
      growth: stats.totalSales?.growth || 0,
      trend: stats.totalSales?.trend || 'up',
      color: '#409EFF',
      formatter: (val) => val.toString().includes('个') ? val : val.toLocaleString()
    },
    { 
      label: '人民币收入', 
      value: stats.rmbSales?.value || 0,
      growth: stats.rmbSales?.growth || 0,
      trend: stats.rmbSales?.trend || 'up',
      color: '#F56C6C',
      formatter: (val) => {
        if (typeof val === 'string' && val.includes('¥')) return val;
        return `¥${parseFloat(val).toFixed(2)}`;
      }
    },
    { 
      label: '光年币收入', 
      value: stats.lySales?.value || 0,
      growth: stats.lySales?.growth || 0,
      trend: stats.lySales?.trend || 'up',
      color: '#E6A23C',
      formatter: (val) => {
        if (typeof val === 'string' && val.includes('光年币')) return val;
        return `${val.toLocaleString()} 个光年币`;
      }
    },
    { 
      label: '活跃用户', 
      value: stats.activeUsers?.value || 0,
      growth: stats.activeUsers?.growth || 0,
      trend: stats.activeUsers?.trend || 'up',
      color: '#67C23A',
      formatter: (val) => val.toString().includes('人') ? val : val.toLocaleString()
    },
    { 
      label: '商品总数', 
      value: stats.totalProducts?.value || 0,
      growth: stats.totalProducts?.growth || 0,
      trend: stats.totalProducts?.trend || 'up',
      color: '#909399',
      formatter: (val) => val.toString().includes('个') ? val : val.toLocaleString()
    },
    { 
      label: '反馈数量', 
      value: stats.totalFeedbacks?.value || 0,
      growth: stats.totalFeedbacks?.growth || 0,
      trend: stats.totalFeedbacks?.trend || 'up',
      color: '#9B59B6',
      formatter: (val) => val.toString().includes('条') ? val : val.toLocaleString()
    }
  ];
    
  // 绘制每张卡片
  cards.forEach((card, index) => {
    // 跳过没有数据的卡片
    if (card.value === undefined && index > 4) return;
    
    const row = Math.floor(index / cardsPerRow);
    const col = index % cardsPerRow;
    const x = doc.page.margins.left + col * (cardWidth + 20);
    const y = startY + row * (cardHeight + 20);
    
    try {
      // 绘制卡片背景
      doc.roundedRect(x, y, cardWidth, cardHeight, 5)
         .fillOpacity(0.1)
         .fillAndStroke(card.color, card.color);
         
      // 重置不透明度
      doc.fillOpacity(1);
      
      // 绘制标签
      doc.fontSize(12)
         .fillColor('#666666');
      safeTextHandling(doc, card.label, { x: x + 10, y: y + 10, width: cardWidth - 20 });
      
      // 绘制数值 - 添加错误处理
      try {
        const formattedValue = card.formatter(card.value);
        doc.fontSize(22)
           .fillColor('#333333');
        safeTextHandling(doc, formattedValue, { x: x + 10, y: y + 30, width: cardWidth - 20 });
      } catch (valueError) {
        console.error(`[ExportController] 格式化卡片值出错(${card.label}):`, valueError);
        doc.fontSize(22)
           .fillColor('#333333');
        safeTextHandling(doc, '--', { x: x + 10, y: y + 30, width: cardWidth - 20 });
      }
      
      // 绘制增长率
      const trendSymbol = card.trend === 'up' ? '↑' : '↓';
      const trendColor = card.trend === 'up' ? '#67C23A' : '#F56C6C';
      doc.fontSize(12)
         .fillColor(trendColor);
      safeTextHandling(doc, `${trendSymbol} ${Math.abs(card.growth)}%`, { x: x + 10, y: y + 65, width: cardWidth - 20 });
    } catch (cardError) {
      console.error(`[ExportController] 绘制卡片出错(${index}):`, cardError);
    }
  });
  
  // 更新y坐标
  doc.y = startY + Math.ceil(cards.length / cardsPerRow) * (cardHeight + 20);
}

/**
 * 绘制销售趋势图
 */
function drawSalesTrend(doc, salesTrend) {
  if (!salesTrend || !salesTrend.timeLabels || salesTrend.timeLabels.length === 0) {
    doc.fontSize(14);
    safeTextHandling(doc, '暂无销售趋势数据', { align: 'center' });
    return;
  }
  
  console.log('[ExportController] 绘制销售趋势图, 标签数:', salesTrend.timeLabels.length);
  
  // 标题
  doc.fontSize(16)
     .fillColor('#333333');
  safeTextHandling(doc, '销售趋势分析', { align: 'center' });
  
  doc.moveDown(0.5);
  
  // 绘制趋势图
  const chartWidth = doc.page.width - 2 * doc.page.margins.left;
  const chartHeight = 200;
  const startY = doc.y;
  
  // 定义图表区域
  const chartArea = {
    left: doc.page.margins.left,
    top: startY,
    right: doc.page.margins.left + chartWidth,
    bottom: startY + chartHeight
  };
  
  // 绘制图表边框
  doc.rect(chartArea.left, chartArea.top, chartWidth, chartHeight)
     .strokeColor('#e0e0e0')
     .lineWidth(1)
     .stroke();
  
  // 准备数据
  const timeLabels = salesTrend.timeLabels || [];
  const rmbData = salesTrend.rmbSalesData || [];
  const lyData = salesTrend.lySalesData || [];
  const orderData = salesTrend.orderData || [];
  
  // 确保数据长度一致
  const dataLength = Math.min(
    timeLabels.length,
    Math.max(rmbData.length, lyData.length, orderData.length)
  );
  
  // 如果没有有效数据，则添加一些示例数据
  if (dataLength === 0) {
    doc.fontSize(14).text('暂无有效的销售趋势数据，显示示例数据', { align: 'center' });
    doc.moveDown(2);
    return;
  }
      
  try {
    // 找出最大值以便缩放
    const maxRmb = Math.max(...rmbData.slice(0, dataLength), 1);
    const maxLy = Math.max(...lyData.slice(0, dataLength), 1);
    const maxOrder = Math.max(...orderData.slice(0, dataLength), 1);
    
    // 绘制水平网格线和标签
    const gridLines = 5;
    for (let i = 0; i <= gridLines; i++) {
      const y = chartArea.top + (chartHeight / gridLines) * i;
      
      // 绘制网格线
      doc.moveTo(chartArea.left, y)
         .lineTo(chartArea.right, y)
         .strokeColor('#e0e0e0')
         .lineWidth(0.5)
         .stroke();
      
      // 绘制Y轴标签
      const rmbValue = Math.round((1 - i / gridLines) * maxRmb);
      doc.fontSize(8)
         .fillColor('#666666')
         .text(`¥${rmbValue}`, chartArea.left - 25, y - 5, { width: 20, align: 'right' });
         
      // 绘制右侧Y轴标签(光年币)
      const lyValue = Math.round((1 - i / gridLines) * maxLy);
      doc.fontSize(8)
         .fillColor('#E6A23C')
         .text(`${lyValue}`, chartArea.right + 5, y - 5, { width: 20, align: 'left' });
    }
    
    // 绘制垂直网格线和X轴标签
    const labelInterval = Math.max(1, Math.floor(dataLength / 10)); // 确保不超过10个标签
    
    for (let index = 0; index < dataLength; index++) {
      const label = timeLabels[index] || '';
      const x = chartArea.left + (chartWidth / (dataLength - 1 || 1)) * index;
      
      // 绘制垂直网格线
      doc.moveTo(x, chartArea.top)
         .lineTo(x, chartArea.bottom)
         .strokeColor('#e0e0e0')
         .lineWidth(0.5)
         .stroke();
      
      // 绘制X轴标签(间隔显示)
      if (index % labelInterval === 0) {
        doc.fontSize(8)
           .fillColor('#666666')
           .text(label, x - 15, chartArea.bottom + 5, { width: 30, align: 'center' });
      }
    }
    
    // 绘制人民币收入线
    if (rmbData.length > 0) {
      drawLine(doc, timeLabels.slice(0, dataLength), rmbData.slice(0, dataLength), maxRmb, chartArea, '#F56C6C', 2);
    }
    
    // 绘制光年币收入线
    if (lyData.length > 0) {
      drawLine(doc, timeLabels.slice(0, dataLength), lyData.slice(0, dataLength), maxLy, chartArea, '#E6A23C', 2);
    }
    
    // 绘制订单量线
    if (orderData.length > 0) {
      drawLine(doc, timeLabels.slice(0, dataLength), orderData.slice(0, dataLength), maxOrder, chartArea, '#409EFF', 2);
    }
  } catch (chartError) {
    console.error('[ExportController] 绘制趋势图表出错:', chartError);
    doc.fontSize(14)
       .fillColor('#F56C6C')
       .text('绘制趋势图表时出错: ' + chartError.message, chartArea.left, chartArea.top + chartHeight / 2, {
         width: chartWidth,
         align: 'center'
       });
  }
  
  // 绘制图例
  const legendY = chartArea.bottom + 30;
  const legendSpacing = 100;
    
  // 人民币收入图例
  doc.rect(chartArea.left, legendY, 15, 10)
     .fillColor('#F56C6C')
     .fill();
  doc.fontSize(10)
     .fillColor('#333333');
  safeTextHandling(doc, '人民币收入', { x: chartArea.left + 20, y: legendY, width: 70 });
  
  // 光年币收入图例
  doc.rect(chartArea.left + legendSpacing, legendY, 15, 10)
     .fillColor('#E6A23C')
     .fill();
  doc.fontSize(10)
     .fillColor('#333333');
  safeTextHandling(doc, '光年币收入', { x: chartArea.left + legendSpacing + 20, y: legendY, width: 70 });
    
  // 订单量图例
  doc.rect(chartArea.left + 2 * legendSpacing, legendY, 15, 10)
     .fillColor('#409EFF')
     .fill();
  doc.fontSize(10)
     .fillColor('#333333');
  safeTextHandling(doc, '订单量', { x: chartArea.left + 2 * legendSpacing + 20, y: legendY, width: 70 });
  
  // 更新y坐标
  doc.y = legendY + 30;
}

/**
 * 绘制折线
 */
function drawLine(doc, labels, data, maxValue, chartArea, color, lineWidth) {
  if (!labels || !data || labels.length === 0 || data.length === 0) {
    console.warn('[ExportController] 绘制折线失败: 数据为空');
    return;
  }
  
  try {
    const chartWidth = chartArea.right - chartArea.left;
    const chartHeight = chartArea.bottom - chartArea.top;
    
    // 确保数据长度有效
    const effectiveLength = Math.min(labels.length, data.length);
    if (effectiveLength < 2) {
      console.warn('[ExportController] 绘制折线失败: 数据点不足', effectiveLength);
      return;
    }
    
    // 确保maxValue合法
    if (!maxValue || maxValue <= 0) {
      console.warn('[ExportController] 绘制折线失败: maxValue无效', maxValue);
      maxValue = Math.max(...data, 1);
    }
    
    // 开始绘制路径
    const firstY = chartArea.bottom - ((data[0] || 0) / maxValue) * chartHeight;
    doc.moveTo(chartArea.left, firstY);
    
    // 绘制每个数据点
    for (let index = 0; index < effectiveLength; index++) {
      const value = data[index] || 0;
      const x = chartArea.left + (chartWidth / (effectiveLength - 1)) * index;
      const y = chartArea.bottom - (value / maxValue) * chartHeight;
      
      doc.lineTo(x, y);
    }
    
    // 绘制线条
    doc.strokeColor(color)
       .lineWidth(lineWidth)
       .stroke();
    
    // 绘制数据点
    for (let index = 0; index < effectiveLength; index++) {
      const value = data[index] || 0;
      const x = chartArea.left + (chartWidth / (effectiveLength - 1)) * index;
      const y = chartArea.bottom - (value / maxValue) * chartHeight;
      
      doc.circle(x, y, 3)
         .fillColor(color)
         .fill();
    }
  } catch (error) {
    console.error('[ExportController] 绘制折线出错:', error);
  }
}

/**
 * 绘制分类饼图
 */
function drawCategoryPie(doc, categories) {
  // 标题
  doc.fontSize(16)
     .fillColor('#333333');
  safeTextHandling(doc, '商品分类占比', { align: 'center' });
  
  doc.moveDown(0.5);
  
  if (!categories || !Array.isArray(categories) || categories.length === 0) {
    doc.fontSize(14);
    safeTextHandling(doc, '暂无分类数据', { align: 'center' });
    return;
  }
  
  const startY = doc.y;
  const centerX = doc.page.margins.left + 200;
  const centerY = startY + 150;
  const radius = 120;
  
  // 准备数据
  const pieData = categories.map(category => ({
    name: category.name || '未知分类',
    value: category.productCount || 0
  })).filter(item => item.value > 0);
  
  if (pieData.length === 0) {
    doc.fontSize(14);
    safeTextHandling(doc, '暂无有效分类数据', { align: 'center' });
    return;
  }
  
  // 计算总和
  const total = pieData.reduce((sum, item) => sum + item.value, 0);
  
  // 定义颜色
  const colors = [
    '#409EFF', '#F56C6C', '#E6A23C', '#67C23A', '#909399',
    '#9B59B6', '#3498DB', '#2ECC71', '#F1C40F', '#E74C3C'
  ];
  
  // 绘制饼图
  let startAngle = 0;
  pieData.forEach((item, index) => {
    const percentage = item.value / total;
    const endAngle = startAngle + percentage * Math.PI * 2;
    const color = colors[index % colors.length];
    
    // 绘制扇形
    doc.moveTo(centerX, centerY)
       .arc(centerX, centerY, radius, startAngle, endAngle, false)
       .lineTo(centerX, centerY)
       .closePath()
       .fillColor(color)
       .fill();
    
    startAngle = endAngle;
  });
  
  // 绘制图例
  const legendStartX = centerX + radius + 50;
  const legendStartY = startY + 30;
  
  doc.fontSize(12)
     .fillColor('#333333');
  safeTextHandling(doc, '图例:', { x: legendStartX, y: legendStartY });
  
  pieData.forEach((item, index) => {
    const y = legendStartY + 30 + index * 25;
    const color = colors[index % colors.length];
    const percentage = Math.round((item.value / total) * 100);
    
    // 绘制颜色块
    doc.rect(legendStartX, y, 15, 15)
       .fillColor(color)
       .fill();
    
    // 绘制文本
    doc.fontSize(10)
       .fillColor('#333333');
    safeTextHandling(doc, `${item.name} (${percentage}%)`, { x: legendStartX + 25, y: y + 3, width: 150 });
  });
  
  // 更新y坐标
  doc.y = centerY + radius + 30;
}

/**
 * 绘制最近订单表格
 */
function drawRecentOrders(doc, orders) {
  // 标题
  doc.fontSize(16)
     .fillColor('#333333');
  safeTextHandling(doc, '最近订单', { align: 'center' });
  
  doc.moveDown(0.5);
  
  if (!orders || !Array.isArray(orders) || orders.length === 0) {
    doc.fontSize(14);
    safeTextHandling(doc, '暂无订单数据', { align: 'center' });
    return;
  }
  
  const startY = doc.y;
  const tableWidth = doc.page.width - 2 * doc.page.margins.left;
  
  // 定义列宽
  const columns = [
    { header: '订单ID', key: 'id', width: 0.15 * tableWidth },
    { header: '日期', key: 'date', width: 0.12 * tableWidth },
    { header: '客户', key: 'customer', width: 0.12 * tableWidth },
    { header: '商品', key: 'productName', width: 0.25 * tableWidth },
    { header: '支付方式', key: 'paymentMethod', width: 0.12 * tableWidth },
    { header: '金额', key: 'amount', width: 0.12 * tableWidth },
    { header: '状态', key: 'status', width: 0.12 * tableWidth }
  ];
     
  // 绘制表头
  let currentX = doc.page.margins.left;
  let currentY = startY;
  
  doc.fillColor('#f5f7fa')
     .rect(currentX, currentY, tableWidth, 30)
     .fill();
  
  columns.forEach(column => {
    doc.fontSize(10)
       .fillColor('#333333');
    safeTextHandling(doc, column.header, { x: currentX + 5, y: currentY + 10, width: column.width - 10 });
    
    currentX += column.width;
  });
  
  currentY += 30;
  
  // 绘制数据行
  const rowHeight = 25;
  const maxRows = Math.min(orders.length, 8); // 限制最多显示8行
  
  for (let i = 0; i < maxRows; i++) {
    const order = orders[i];
    currentX = doc.page.margins.left;
    
    // 绘制交替背景色
    if (i % 2 === 1) {
      doc.fillColor('#f5f7fa')
         .rect(currentX, currentY, tableWidth, rowHeight)
           .fill();
      }
      
    // 绘制边框
    doc.strokeColor('#e0e0e0')
       .rect(currentX, currentY, tableWidth, rowHeight)
         .stroke();
         
    // 订单ID
    doc.fontSize(9)
       .fillColor('#333333');
    safeTextHandling(doc, order.id || '', { x: currentX + 5, y: currentY + 8, width: columns[0].width - 10, ellipsis: true });
    currentX += columns[0].width;
    
    // 日期
    doc.fontSize(9)
       .fillColor('#333333');
    safeTextHandling(doc, order.date || '', { x: currentX + 5, y: currentY + 8, width: columns[1].width - 10, ellipsis: true });
    currentX += columns[1].width;
    
    // 客户
    doc.fontSize(9)
       .fillColor('#333333');
    safeTextHandling(doc, order.customer || '', { x: currentX + 5, y: currentY + 8, width: columns[2].width - 10, ellipsis: true });
    currentX += columns[2].width;
    
    // 商品
    doc.fontSize(9)
       .fillColor('#333333');
    safeTextHandling(doc, order.productName || '', { x: currentX + 5, y: currentY + 8, width: columns[3].width - 10, ellipsis: true });
    currentX += columns[3].width;
    
    // 支付方式
    const paymentText = order.paymentMethod === 'ly' ? '光年币' : '人民币';
    const paymentColor = order.paymentMethod === 'ly' ? '#E6A23C' : '#F56C6C';
    doc.fontSize(9)
       .fillColor(paymentColor);
    safeTextHandling(doc, paymentText, { x: currentX + 5, y: currentY + 8, width: columns[4].width - 10, ellipsis: true });
    currentX += columns[4].width;
    
    // 金额
    const amountText = order.paymentMethod === 'ly' 
      ? `${order.amount || 0} 光年币` 
      : `¥${(order.amount || 0).toFixed(2)}`;
    doc.fontSize(9)
       .fillColor('#333333');
    safeTextHandling(doc, amountText, { x: currentX + 5, y: currentY + 8, width: columns[5].width - 10, ellipsis: true });
    currentX += columns[5].width;
    
    // 状态
    let statusColor = '#67C23A'; // 默认绿色
    if (order.status === '待处理' || order.status === '处理中') {
      statusColor = '#409EFF';
    } else if (order.status === '已拒绝' || order.status === '已取消') {
      statusColor = '#F56C6C';
    } else if (order.status === '已发货') {
      statusColor = '#E6A23C';
    }
    
    doc.fontSize(9)
       .fillColor(statusColor);
    safeTextHandling(doc, order.status || '', { x: currentX + 5, y: currentY + 8, width: columns[6].width - 10, ellipsis: true });
    
    currentY += rowHeight;
    }
    
  // 如果有更多订单，添加提示
  if (orders.length > maxRows) {
    doc.fontSize(10)
       .fillColor('#909399');
    safeTextHandling(doc, `... 共${orders.length}条订单，显示前${maxRows}条`, { x: doc.page.margins.left, y: currentY + 5, align: 'center' });
  }
  
  // 更新y坐标
  doc.y = currentY + 30;
}

/**
 * 添加页脚
 */
function drawFooter(doc) {
  const pageBottom = doc.page.height - doc.page.margins.bottom;
  
  doc.fontSize(8)
     .fillColor('#999999');
  safeTextHandling(doc, 
     `光年汇兑系统 - 数据仪表盘报告 - 生成时间：${moment().format('YYYY-MM-DD HH:mm:ss')}`,
     { align: 'center', width: doc.page.width - 2 * doc.page.margins.left, y: pageBottom - 15, x: doc.page.margins.left }
  );
}

/**
 * 安全处理中文文本，确保PDF能正确显示
 * 如果中文字体加载失败，尝试使用英文替代或者使用符号标记
 */
function safeTextHandling(doc, text, options = {}) {
  if (!text) return;
  
  try {
    // 尝试直接使用当前字体渲染文本
    doc.text(text, options);
  } catch (error) {
    console.error('[ExportController] 渲染文本失败:', error);
    console.error('[ExportController] 问题文本:', text);
    
    try {
      // 尝试使用默认安全字体
      const originalFont = doc._font ? doc._font.name : 'unknown';
      console.log(`[ExportController] 从 ${originalFont} 临时切换到 Helvetica`);
      
      // 临时切换到Helvetica
      doc.font('Helvetica');
      
      // 替换中文字符为罗马字符或占位符
      const safeText = text.replace(/[\u4e00-\u9fa5]/g, '■');
      doc.text(safeText, options);
      
      // 尝试恢复原字体
      try {
        if (originalFont && originalFont !== 'unknown' && originalFont !== 'Helvetica') {
          doc.font(originalFont);
          console.log(`[ExportController] 已恢复到原字体 ${originalFont}`);
        }
      } catch (fontError) {
        console.error('[ExportController] 恢复原字体失败，继续使用Helvetica');
      }
    } catch (fallbackError) {
      console.error('[ExportController] 安全渲染文本失败:', fallbackError);
    }
  }
} 
const notificationDiagnosticService = require('../services/notificationDiagnosticService');

/**
 * 通知诊断控制器
 */
class NotificationDiagnosticController {
  /**
   * 获取诊断记录列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getDiagnosticResults(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        pageSize: parseInt(req.query.pageSize) || 20,
        diagnosticType: req.query.diagnosticType,
        status: req.query.status,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        sortBy: req.query.sortBy || 'created_at',
        sortOrder: req.query.sortOrder || 'DESC'
      };
      
      const results = await notificationDiagnosticService.getDiagnosticResults(options);
      res.json({
        code: 0,
        data: results,
        message: '获取诊断记录列表成功'
      });
    } catch (error) {
      console.error('获取诊断记录列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取诊断记录列表失败: ' + error.message
      });
    }
  }

  /**
   * 解决诊断问题
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async resolveIssue(req, res) {
    try {
      const id = req.params.id;
      const { resolution } = req.body;
      
      if (!resolution) {
        return res.status(400).json({
          code: 400,
          message: '参数错误: 缺少resolution字段'
        });
      }
      
      const diagnostic = await notificationDiagnosticService.resolveIssue(id, resolution);
      
      res.json({
        code: 0,
        data: diagnostic,
        message: '解决问题成功'
      });
    } catch (error) {
      console.error('解决问题失败:', error);
      res.status(500).json({
        code: 500,
        message: '解决问题失败: ' + error.message
      });
    }
  }

  /**
   * 测试Webhook连接
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testWebhookConnection(req, res) {
    try {
      const { webhookUrl } = req.body;
      
      if (!webhookUrl) {
        return res.status(400).json({
          code: 400,
          message: '参数错误: 缺少webhookUrl字段'
        });
      }
      
      const result = await notificationDiagnosticService.testWebhookConnection(webhookUrl);
      
      res.json({
        code: 0,
        data: result,
        message: result.success ? '连接测试成功' : '连接测试失败'
      });
    } catch (error) {
      console.error('连接测试失败:', error);
      res.status(500).json({
        code: 500,
        message: '连接测试失败: ' + error.message
      });
    }
  }

  /**
   * 获取系统健康状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSystemHealth(req, res) {
    try {
      const health = await notificationDiagnosticService.getSystemHealth();
      
      res.json({
        code: 0,
        data: health,
        message: '获取系统健康状态成功'
      });
    } catch (error) {
      console.error('获取系统健康状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取系统健康状态失败: ' + error.message
      });
    }
  }

  /**
   * 分析消息发送统计
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async analyzeMessageDelivery(req, res) {
    try {
      const { startDate, endDate, notificationType } = req.body;
      
      const result = await notificationDiagnosticService.analyzeMessageDelivery({
        startDate,
        endDate,
        notificationType
      });
      
      res.json({
        code: 0,
        data: result,
        message: '分析消息发送统计成功'
      });
    } catch (error) {
      console.error('分析消息发送统计失败:', error);
      res.status(500).json({
        code: 500,
        message: '分析消息发送统计失败: ' + error.message
      });
    }
  }

  /**
   * 运行全面诊断
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async runComprehensiveDiagnostic(req, res) {
    try {
      const result = await notificationDiagnosticService.runComprehensiveDiagnostic();
      
      res.json({
        code: 0,
        data: result,
        message: '运行全面诊断成功'
      });
    } catch (error) {
      console.error('运行全面诊断失败:', error);
      res.status(500).json({
        code: 500,
        message: '运行全面诊断失败: ' + error.message
      });
    }
  }
}

module.exports = new NotificationDiagnosticController(); 
const { User } = require('../models');
const { generateToken } = require('../utils/jwt');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const { logUserLogin, logUserLogout, logUserAction } = require('./logController');

/**
 * 用户注册 (管理员创建用户)
 */
exports.register = async (req, res) => {
  try {
    const { username, password, role, email, department, workplace } = req.body;

    // 验证邮箱后缀
    if (email && !email.endsWith('@guanghe.tv')) {
      return res.status(400).json({ message: '邮箱必须以@guanghe.tv结尾' });
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(409).json({ message: '邮箱已被注册' });
      }
    }

    // 创建新用户
    const user = await User.create({
      username,
      email: email || null,
      password, // 密码会在模型钩子中自动加密
      role: role || 'user',
      department: department || null,
      workplace: workplace || null
    });

    // 记录用户注册日志 - 管理员创建用户
    await logUserAction(
      require('../models/log').ACTIONS.USER_REGISTER,
      user,
      req.user, // 创建用户的管理员信息
      null,
      req
    );

    // 移除敏感字段
    const userWithoutPassword = { ...user.get() };
    delete userWithoutPassword.password;

    return res.status(201).json({
      message: '用户注册成功',
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('注册错误:', error);
    // 返回更详细的错误信息
    if (error.name === 'SequelizeUniqueConstraintError') {
      // 处理唯一约束错误
      if (error.fields && error.fields.email) {
        return res.status(409).json({ message: '邮箱已被注册' });
      }
    }
    return res.status(500).json({ 
      message: '服务器错误', 
      details: error.message 
    });
  }
};

/**
 * 公开用户注册 (仅限普通用户)
 */
exports.registerPublic = async (req, res) => {
  try {
    const { username, password, email, department, workplace, mobile } = req.body;

    // 验证必填字段
    if (!username || !password || !email || !department || !workplace) {
      return res.status(400).json({ message: '姓名、密码、邮箱、部门和职场均为必填项' });
    }

    // 验证密码长度
    if (password.length < 6) {
      return res.status(400).json({ message: '密码长度必须至少为6个字符' });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: '邮箱格式无效' });
    }

    // 验证邮箱后缀
    if (email && !email.endsWith('@guanghe.tv')) {
      return res.status(400).json({ message: '邮箱必须以@guanghe.tv结尾' });
    }

    // 验证手机号码格式（如果提供）
    if (mobile && !/^1[3-9]\d{9}$/.test(mobile)) {
      return res.status(400).json({ message: '无效的手机号码格式' });
    }

    // 检查邮箱是否已存在
    const existingEmail = await User.findOne({ where: { email } });
    if (existingEmail) {
      return res.status(409).json({ message: '邮箱已被注册' });
    }

    // 创建新用户 - 强制设置为普通用户角色
    const user = await User.create({
      username,
      email,
      password,
      role: 'user', // 强制设置为普通用户
      department,
      workplace,
      mobile: mobile || null
    });

    // 记录用户注册日志 - 用户自行注册
    await logUserAction(
      require('../models/log').ACTIONS.USER_REGISTER,
      user,
      user, // 用户自行注册，操作者就是用户本身
      null,
      req
    );

    // 移除敏感字段
    const userWithoutPassword = { ...user.get() };
    delete userWithoutPassword.password;

    return res.status(201).json({
      message: '注册成功',
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('注册错误:', error);
    // 返回更详细的错误信息
    if (error.name === 'SequelizeUniqueConstraintError') {
      // 处理唯一约束错误
      if (error.fields && error.fields.email) {
        return res.status(409).json({ message: '邮箱已被注册' });
      }
    }
    return res.status(500).json({ 
      message: '服务器错误', 
      details: error.message 
    });
  }
};

/**
 * 用户登录
 */
exports.login = async (req, res) => {
  try {
    const { username, email, password, rememberMe } = req.body;
    
    console.log('===== 登录请求处理开始 =====');
    console.log(`接收登录请求: 姓名=${username}, 邮箱=${email}, 时间=${new Date().toISOString()}`);
    console.log(`记住我: ${rememberMe ? '是' : '否'}`);
    
    // 验证输入
    if ((!username && !email) || !password) {
      console.log('登录失败: 姓名/邮箱或密码为空');
      return res.status(400).json({ message: '姓名/邮箱和密码为必填项' });
    }
    
    try {
      // 查询用户 (同时使用姓名和邮箱进行精确匹配)
      let user;
      
      if (username && email) {
        // 当同时提供了用户名和邮箱时，使用两者的组合查询
        console.log(`使用姓名和邮箱组合查询: ${username}, ${email}`);
        user = await User.findOne({ 
          where: {
            username: username,
            email: email
          },
          raw: false
        });
      } else if (email) {
        // 如果只提供了邮箱，则只使用邮箱查询
        console.log(`仅使用邮箱查询: ${email}`);
        user = await User.findOne({ 
          where: { email: email },
          raw: false
        });
      } else if (username) {
        // 为了向后兼容，当只提供用户名时，我们将找到第一个匹配该用户名的账户
        // 注意：这可能会导致登录到错误的账户，如果存在同名用户
        console.log(`仅使用姓名查询(不推荐): ${username}`);
        user = await User.findOne({ 
          where: { username: username },
          raw: false
        });
      }
      
      // 用户不存在
      if (!user) {
        console.log(`登录失败: 用户${username || email}不存在或姓名与邮箱不匹配`);
        if (username && email) {
          return res.status(401).json({ message: '姓名与邮箱不匹配或密码错误' });
        } else {
          return res.status(401).json({ message: '姓名/邮箱或密码错误' });
        }
      }
      
      console.log(`找到用户: ID=${user.id}, 姓名=${user.username}, 角色=${user.role}`);
      
      // 验证密码
      console.log('开始验证密码...');
      
      // 方法1: 使用bcrypt.compare直接比较
      const isPasswordValid = await bcrypt.compare(password, user.password);
      console.log(`密码验证结果: ${isPasswordValid ? '成功' : '失败'}`);
      
      if (!isPasswordValid) {
        console.log(`登录失败: 用户 ${username || email} 密码错误`);
        return res.status(401).json({ message: '姓名/邮箱或密码错误' });
      }
      
      // 获取客户端IP地址
      let ipAddress = req.headers['x-forwarded-for'] || 
                      req.headers['x-real-ip'] || 
                      req.ip || 
                      req.connection.remoteAddress || 
                      '';
      
      // 处理IPv6格式的IP地址（如 ::ffff:127.0.0.1）
      if (ipAddress.includes('::ffff:')) {
        ipAddress = ipAddress.split('::ffff:')[1];
      }
      
      // 处理本地IPv6地址
      if (ipAddress === '::1') {
        ipAddress = '127.0.0.1';
      }
      
      // 如果是逗号分隔的多个IP（X-Forwarded-For可能包含多个IP），取第一个
      if (ipAddress && ipAddress.includes(',')) {
        ipAddress = ipAddress.split(',')[0].trim();
      }
      
      // 更新用户最后登录时间和IP
      await user.update({
        lastLoginAt: new Date(),
        lastLoginIp: ipAddress
      });
      
      console.log(`更新用户登录信息: 时间=${new Date().toISOString()}, IP=${ipAddress}`);
      
      // 生成用户数据(不含密码)
      const userWithoutPassword = { ...user.get() };
      delete userWithoutPassword.password;
      
      // 生成JWT令牌，传入记住我参数
      const token = generateToken(userWithoutPassword, !!rememberMe);
      console.log(`为用户 ${username || email} 生成令牌成功, 长度=${token.length}`);
      
      // 记录用户登录
      await logUserLogin(user, req);
      
      // 返回登录成功响应
      console.log(`登录成功: 用户=${username || email}, 角色=${user.role}`);
      console.log('===== 登录请求处理完成 =====');
      
      return res.status(200).json({
        message: '登录成功',
        user: userWithoutPassword,
        token
      });
      
    } catch (dbError) {
      console.error('数据库操作错误:', dbError);
      return res.status(500).json({ message: '服务器数据库错误' });
    }
  } catch (error) {
    console.error('登录处理全局错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
};

/**
 * 获取当前用户资料
 */
exports.getProfile = async (req, res) => {
  try {
    // req.user 由 authenticate 中间件提供
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 移除敏感字段
    const userWithoutPassword = { ...user.get() };
    delete userWithoutPassword.password;

    return res.status(200).json({
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('获取用户资料错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
};

/**
 * 获取当前登录用户信息
 */
exports.getCurrentUser = async (req, res) => {
  try {
    // req.user 由 authenticate 中间件提供
    const { id, username, email, role, department, workplace } = req.user;
    
    return res.json({
      user: { id, username, email, role, department, workplace }
    });
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试' });
  }
};

// 添加用户退出方法
const logout = async (req, res) => {
  try {
    // 检查用户是否存在
    if (req.user) {
      // 记录用户退出
      await logUserLogout(req.user, req);
    }
    
    return res.status(200).json({
      success: true,
      message: '退出成功'
    });
  } catch (error) {
    console.error('退出失败:', error);
    return res.status(500).json({
      success: false,
      message: '退出失败',
      error: error.message
    });
  }
};

module.exports = {
  register: exports.register,
  registerPublic: exports.registerPublic,
  login: exports.login,
  logout,
  getProfile: exports.getProfile,
  getCurrentUser: exports.getCurrentUser
}; 
const { User, Notification } = require('../models');

// 获取当前用户的通知列表
exports.getNotifications = async (req, res) => {
  try {
    const userId = req.user.id;
    const notifications = await Notification.findAll({
      where: { recipientId: userId },
      order: [['createdAt', 'DESC']],
      include: [{ model: User, attributes: ['id', 'username'] }]
    });
    
    return res.json({
      success: true,
      data: notifications
    });
  } catch (error) {
    console.error('获取通知失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取通知失败',
      error: error.message
    });
  }
};

// 获取未读通知数量
exports.getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await Notification.count({
      where: { 
        recipientId: userId,
        isRead: false
      }
    });
    
    return res.json({
      success: true,
      data: { count }
    });
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取未读通知数量失败',
      error: error.message
    });
  }
};

// 标记通知为已读
exports.markAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    const notification = await Notification.findOne({
      where: { 
        id, 
        recipientId: userId 
      }
    });
    
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }
    
    notification.isRead = true;
    await notification.save();
    
    return res.json({
      success: true,
      message: '通知已标记为已读',
      data: notification
    });
  } catch (error) {
    console.error('标记通知失败:', error);
    return res.status(500).json({
      success: false,
      message: '标记通知失败',
      error: error.message
    });
  }
};

// 标记所有通知为已读
exports.markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    
    await Notification.update(
      { isRead: true },
      { where: { recipientId: userId, isRead: false } }
    );
    
    return res.json({
      success: true,
      message: '所有通知已标记为已读'
    });
  } catch (error) {
    console.error('标记所有通知失败:', error);
    return res.status(500).json({
      success: false,
      message: '标记所有通知失败',
      error: error.message
    });
  }
};

// 删除通知
exports.deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    const notification = await Notification.findOne({
      where: { 
        id, 
        recipientId: userId 
      }
    });
    
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }
    
    await notification.destroy();
    
    return res.json({
      success: true,
      message: '通知已删除'
    });
  } catch (error) {
    console.error('删除通知失败:', error);
    return res.status(500).json({
      success: false,
      message: '删除通知失败',
      error: error.message
    });
  }
};

// 创建新通知的工具函数
exports.createNotification = async (data) => {
  try {
    const notification = await Notification.create(data);
    return notification;
  } catch (error) {
    console.error('创建通知失败:', error);
    throw error;
  }
}; 
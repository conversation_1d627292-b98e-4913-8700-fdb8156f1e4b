const { Category, Product } = require('../models');
const { sequelize } = require('../config/database');

/**
 * 获取所有分类
 */
exports.getCategories = async (req, res) => {
  try {
    const categories = await Category.findAll({
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });
    
    res.json(categories);
  } catch (error) {
    console.error('获取分类列表失败:', error);
    res.status(500).json({ message: '获取分类列表失败', error: error.message });
  }
};

/**
 * 获取单个分类详情
 */
exports.getCategoryById = async (req, res) => {
  try {
    const categoryId = req.params.id;
    
    const category = await Category.findByPk(categoryId);
    
    if (!category) {
      return res.status(404).json({ message: '分类不存在' });
    }
    
    res.json(category);
  } catch (error) {
    console.error('获取分类详情失败:', error);
    res.status(500).json({ message: '获取分类详情失败', error: error.message });
  }
};

/**
 * 创建新分类
 */
exports.createCategory = async (req, res) => {
  try {
    const { name, description, sortOrder } = req.body;
    
    // 验证必填字段
    if (!name) {
      return res.status(400).json({ message: '分类名称为必填项' });
    }
    
    // 检查分类名称是否已存在
    const existingCategory = await Category.findOne({ where: { name } });
    if (existingCategory) {
      return res.status(409).json({ message: '分类名称已存在' });
    }
    
    // 创建分类
    const category = await Category.create({
      name,
      description,
      sortOrder: sortOrder || 0
    });
    
    res.status(201).json(category);
  } catch (error) {
    console.error('创建分类失败:', error);
    res.status(500).json({ message: '创建分类失败', error: error.message });
  }
};

/**
 * 更新分类信息
 */
exports.updateCategory = async (req, res) => {
  try {
    const categoryId = req.params.id;
    const { name, description, sortOrder } = req.body;
    
    // 查询分类是否存在
    const category = await Category.findByPk(categoryId);
    
    if (!category) {
      return res.status(404).json({ message: '分类不存在' });
    }
    
    // 如果修改了名称，检查新名称是否已存在
    if (name && name !== category.name) {
      const existingCategory = await Category.findOne({ where: { name } });
      if (existingCategory) {
        return res.status(409).json({ message: '分类名称已存在' });
      }
    }
    
    // 更新分类
    await category.update({
      name: name || category.name,
      description: description !== undefined ? description : category.description,
      sortOrder: sortOrder !== undefined ? sortOrder : category.sortOrder
    });
    
    res.json(category);
  } catch (error) {
    console.error('更新分类失败:', error);
    res.status(500).json({ message: '更新分类失败', error: error.message });
  }
};

/**
 * 删除分类
 */
exports.deleteCategory = async (req, res) => {
  try {
    const categoryId = req.params.id;
    
    // 查询分类是否存在
    const category = await Category.findByPk(categoryId);
    
    if (!category) {
      return res.status(404).json({ message: '分类不存在' });
    }
    
    // 检查分类下是否有商品
    const productsCount = await Product.count({ where: { categoryId } });
    
    if (productsCount > 0) {
      return res.status(400).json({ 
        message: '无法删除该分类，请先移除或重新分类该分类下的所有商品',
        productsCount
      });
    }
    
    // 删除分类
    await category.destroy();
    
    res.json({ message: '分类已成功删除' });
  } catch (error) {
    console.error('删除分类失败:', error);
    res.status(500).json({ message: '删除分类失败', error: error.message });
  }
};

/**
 * 获取分类及其商品数量
 */
exports.getCategoriesWithProductCount = async (req, res) => {
  try {
    console.log('调用分类商品统计API');
    
    // 获取所有分类
    const categories = await Category.findAll({
      order: [['sortOrder', 'ASC'], ['name', 'ASC']],
      attributes: {
        include: [
          [
            // 计算每个分类下的商品数量
            sequelize.literal(`(
              SELECT COUNT(*)
              FROM products
              WHERE products.categoryId = Category.id AND products.status = 'active'
            )`),
            'productCount'
          ],
          [
            // 计算每个分类下的总销量
            sequelize.literal(`(
              SELECT COALESCE(SUM(exchanges.quantity), 0)
              FROM exchanges
              JOIN products ON exchanges.productId = products.id
              WHERE products.categoryId = Category.id
              AND exchanges.status IN ('completed', 'shipped', 'approved')
            )`),
            'salesCount'
          ],
          [
            // 计算每个分类下的总销售额
            sequelize.literal(`(
              SELECT COALESCE(SUM(
                CASE 
                  WHEN exchanges.paymentMethod = 'ly' THEN exchanges.quantity * products.lyPrice
                  ELSE exchanges.quantity * products.rmbPrice
                END
              ), 0)
              FROM exchanges
              JOIN products ON exchanges.productId = products.id
              WHERE products.categoryId = Category.id
              AND exchanges.status IN ('completed', 'shipped', 'approved')
            )`),
            'salesAmount'
          ]
        ]
      }
    });
    
    // 格式化数据，为前端图表添加颜色和百分比
    const totalSales = categories.reduce((sum, cat) => sum + parseFloat(cat.dataValues.salesAmount || 0), 0);
    const formattedCategories = categories.map((category, index) => {
      const salesAmount = parseFloat(category.dataValues.salesAmount || 0);
      const percentage = totalSales > 0 ? (salesAmount / totalSales * 100).toFixed(2) : 0;
      
      // 生成随机但固定的颜色
      const colors = [
        '#3699FF', '#6993FF', '#E5EAEE', '#1BC5BD', '#8950FC',
        '#F64E60', '#FFA800', '#F3F6F9', '#D6D6E0', '#B5B5C3'
      ];
      
      return {
        id: category.id,
        name: category.name,
        description: category.description,
        sortOrder: category.sortOrder,
        productCount: parseInt(category.dataValues.productCount || 0),
        salesCount: parseInt(category.dataValues.salesCount || 0),
        salesAmount: salesAmount,
        percentage: parseFloat(percentage),
        color: colors[index % colors.length],
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      };
    });
    
    // 按销售额排序
    formattedCategories.sort((a, b) => b.salesAmount - a.salesAmount);
    
    console.log('分类商品统计完成');
    res.json(formattedCategories);
  } catch (error) {
    console.error('获取分类及商品数量失败:', error);
    res.status(500).json({ message: '获取分类及商品数量失败', error: error.message });
  }
}; 
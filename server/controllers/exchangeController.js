const db = require('../models');
const Exchange = db.Exchange;
const Product = db.Product;
const User = db.User;
const Feedback = db.Feedback;
const Category = db.Category;
const { Op } = require('sequelize');
const { createLog } = require('./logController');
const { createNotification } = require('./notificationController');
const { sequelize } = require('../config/database');
const { checkProductStock } = require('../services/stockAlertService');
const feishuBotService = require('../services/feishuBotService');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const { parse } = require('json2csv');
const os = require('os');
const { createReadStream, createWriteStream } = require('fs');

/**
 * 创建兑换申请
 */
exports.createExchange = async (req, res) => {
  try {
    const { productId, quantity, paymentMethod, contactInfo, location, remarks, paymentProofUrl } = req.body;
    const userId = req.user.id; // 从JWT中获取用户ID
    
    // 验证商品存在且有库存
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }
    
    if (product.stock < quantity) {
      return res.status(400).json({ message: '商品库存不足' });
    }
    
    // 验证支付方式与支付凭证
    if (paymentMethod === 'rmb' && !paymentProofUrl) {
      return res.status(400).json({ message: '使用人民币支付必须提供支付凭证' });
    }
    
    // 创建兑换记录
    const exchange = await Exchange.create({
      userId,
      productId,
      quantity,
      paymentMethod,
      contactInfo: contactInfo || '无需联系',
      location: location || '无需配送',
      remarks,
      paymentProofUrl, // 新增支付凭证URL字段
      status: 'pending'
    });
    
    // 更新商品库存和兑换数量
    await product.update({
      stock: product.stock - quantity,
      exchangeCount: (product.exchangeCount || 0) + quantity
    });
    
    // 检查是否需要发送库存告警
    await checkProductStock(product);
    
    // 记录兑换日志
    await createLog({
      action: 'exchange_create',
      entityType: 'exchange',
      entityId: exchange.id,
      oldValue: null,
      newValue: JSON.stringify(exchange),
      userId: req.user.id,
      username: req.user.username,
      description: `用户"${req.user.username}"兑换了"${product.name}"商品${quantity}个，光年币价格：${product.lyPrice}，人民币价格：${product.rmbPrice}`
    }, req);
    
    // 记录库存变更日志
    await createLog({
      action: 'stock_update',
      entityType: 'product',
      entityId: product.id,
      oldValue: JSON.stringify({ stock: product.stock + quantity }),
      newValue: JSON.stringify({ stock: product.stock }),
      userId: req.user.id,
      username: req.user.username,
      description: `由于兑换(ID: ${exchange.id})，商品"${product.name}"库存从${product.stock + quantity}减少到${product.stock}`
    }, req);
    
    // 为所有管理员创建通知
    const admins = await User.findAll({
      where: { role: 'admin' }
    });
    
    const notificationPromises = [];
    for (const admin of admins) {
      notificationPromises.push(createNotification({
        type: 'exchange',
        sourceId: exchange.id,
        title: '新兑换申请',
        content: `用户 ${req.user.username} 提交了新的兑换申请，商品: ${product.name}`,
        recipientId: admin.id
      }));
    }
    
    // 使用Promise.all等待所有通知创建完成
    await Promise.all(notificationPromises);
    
    // 重新查询获取完整的兑换记录（包括在beforeCreate钩子中生成的orderNumber）
    const createdExchange = await Exchange.findByPk(exchange.id, {
      include: [{
        model: Product,
        attributes: ['id', 'name', 'lyPrice', 'rmbPrice'],
        required: false
      }]
    });

    // 获取完整的交换记录数据
    let exchangeData = createdExchange.get({ plain: true });
    
    // 发送飞书群通知（仅针对飞书登录用户）
    try {
      // 获取用户完整信息
      const userWithDetails = await User.findByPk(req.user.id, {
        attributes: ['id', 'username', 'email', 'mobile', 'department', 'departmentPath', 'authType']
      });
      
      if (userWithDetails && userWithDetails.authType === 'feishu') {
        console.log('兑换申请: 检测到飞书用户，发送群通知...');
        await feishuBotService.sendExchangeNotification(
          exchangeData,
          exchangeData.Product,
          userWithDetails
        );
        console.log('兑换申请: 飞书群通知发送成功');
      }
    } catch (feishuError) {
      console.error('兑换申请: 飞书群通知发送失败，但不影响主流程:', feishuError.message);
      // 不阻止主流程，继续返回成功响应
    }
    
    // 运营数据推送：销售里程碑检查
    try {
      const operationalNotificationService = require('../services/operationalNotificationService');
      await operationalNotificationService.checkSalesMilestone(exchangeData);
    } catch (milestoneError) {
      console.error('兑换申请: 销售里程碑检查失败:', milestoneError.message);
      // 不影响主流程
    }

    // 运营数据推送：异常订单预警
    try {
      const operationalNotificationService = require('../services/operationalNotificationService');
      const userWithDetails = await User.findByPk(req.user.id, {
        attributes: ['id', 'username', 'email', 'mobile', 'department', 'departmentPath']
      });
      
      await operationalNotificationService.checkOrderAlert(
        exchangeData,
        exchangeData.Product,
        userWithDetails
      );
    } catch (alertError) {
      console.error('兑换申请: 异常订单预警检查失败:', alertError.message);
      // 不影响主流程
    }
    
    // 返回创建的兑换记录
    return res.status(201).json({
      message: '兑换申请已提交',
      data: exchangeData
    });
  } catch (error) {
    console.error('创建兑换申请失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试' });
  }
};

/**
 * 获取用户的兑换记录列表
 */
exports.getUserExchanges = async (req, res) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const status = req.query.status || null;
    
    // 构建查询条件
    const where = { userId };
    if (status) {
      where.status = status;
    }
    
    console.log('查询条件:', JSON.stringify(where));
    console.log('用户ID:', userId);
    
    // 优化查询，处理关联可能不存在的情况
    const { count, rows } = await Exchange.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [{
        model: Product,
        attributes: ['id', 'name', 'lyPrice', 'rmbPrice'],
        required: false // 设置为false，即使关联不存在也会返回记录
      }]
    });
    
    console.log('查询结果数量:', count);
    
    // 格式化数据，保留原始数据库中的orderNumber字段
    const formattedRows = rows.map(exchange => {
      const plainExchange = exchange.get ? exchange.get({ plain: true }) : exchange;
      return plainExchange;
    });
    
    return res.json({
      data: formattedRows,
      total: count,
      page,
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取兑换记录失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 获取用户最近一次兑换记录
 */
exports.getUserLastExchange = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 查询该用户最近的一条兑换记录
    const lastExchange = await Exchange.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']],
      include: [{
        model: Product,
        attributes: ['id', 'name', 'lyPrice', 'rmbPrice'],
        required: false
      }]
    });
    
    // 如果没有找到记录，返回空数据
    if (!lastExchange) {
      return res.json({
        data: null,
        message: '没有找到兑换记录'
      });
    }
    
    // 将数据转换为普通对象
    const plainExchange = lastExchange.get({ plain: true });
    
    return res.json({
      data: plainExchange
    });
  } catch (error) {
    console.error('获取最近兑换记录失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 获取兑换记录详情
 */
exports.getExchangeDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    console.log('获取兑换详情:', id, '用户ID:', userId, '用户角色:', req.user.role);
    
    // 构建查询条件
    const queryCondition = { 
      where: { 
        id,
        // 普通用户只能查看自己的兑换记录，管理员可以查看所有
        ...(req.user.role !== 'admin' ? { userId } : {})
      },
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'lyPrice', 'rmbPrice', 'description'],
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username', 'email', 'department'],
          required: false
        }
      ]
    };
    
    const exchange = await Exchange.findOne(queryCondition);
    
    if (!exchange) {
      return res.status(404).json({ message: '兑换记录不存在或无权访问' });
    }
    
    console.log('兑换记录查询成功');
    
    // 格式化为普通对象
    const exchangeData = exchange.get ? exchange.get({ plain: true }) : exchange;
    
    // 直接返回从数据库获取的对象
    return res.json(exchangeData);
  } catch (error) {
    console.error('获取兑换记录详情失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 取消兑换申请（仅限待处理状态的申请）
 */
exports.cancelExchange = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    const exchange = await Exchange.findOne({
      where: { 
        id,
        userId,
        status: 'pending' // 只能取消待处理的申请
      },
      include: [{
        model: Product,
        attributes: ['id', 'name', 'stock'],
        required: false
      }]
    });
    
    if (!exchange) {
      return res.status(404).json({ message: '兑换申请不存在或已处理，无法取消' });
    }
    
    const oldStatus = exchange.status;
    
    // 更新状态为取消
    await exchange.update({ status: 'cancelled' });
    
    // 增加商品库存
    const product = await Product.findByPk(exchange.productId);
    if (product) {
      const oldStock = product.stock;
      
      await product.update({
        stock: product.stock + exchange.quantity,
        exchangeCount: Math.max(0, product.exchangeCount - exchange.quantity)
      });
      
      // 记录库存变更日志
      await createLog({
        action: 'stock_update',
        entityType: 'product',
        entityId: product.id,
        oldValue: JSON.stringify({ stock: oldStock }),
        newValue: JSON.stringify({ stock: product.stock }),
        userId: req.user.id,
        username: req.user.username,
        description: `由于取消兑换(ID: ${exchange.id})，商品"${product.name}"库存从${oldStock}增加到${product.stock}`
      }, req);
    }
    
    // 记录兑换取消日志，使用正确的action类型
    await createLog({
      action: 'exchange_cancel',
      entityType: 'exchange',
      entityId: exchange.id,
      oldValue: JSON.stringify({ status: oldStatus }),
      newValue: JSON.stringify({ status: 'cancelled' }),
      userId: req.user.id,
      username: req.user.username,
      description: `用户"${req.user.username}"取消了兑换申请(ID: ${exchange.id})`
    }, req);
    
    return res.json({ message: '兑换申请已取消' });
  } catch (error) {
    console.error('取消兑换申请失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试' });
  }
};

/**
 * 管理员获取兑换申请列表
 */
exports.getExchangeList = async (req, res) => {
  try {
    // 检查是否为管理员
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: '无权访问管理员功能' });
    }
    
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const status = req.query.status || null;
    const search = req.query.search || '';
    
    // 构建查询条件
    const where = {};
    if (status) {
      where.status = status;
    }
    
    console.log('管理员查询兑换列表:', JSON.stringify(where), '搜索:', search);
    
    // 构建查询选项，处理关联可能不存在的情况
    const queryOptions = {
      where,
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'lyPrice', 'rmbPrice'],
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username', 'department', 'email'],
          required: false
        }
      ]
    };
    
    // 如果有搜索条件，修改查询策略：使用连接查询
    if (search) {
      // 先查询匹配搜索条件的用户ID
      const matchingUsers = await User.findAll({
        attributes: ['id'],
        where: {
          [Op.or]: [
            { username: { [Op.like]: `%${search}%` } },
            { department: { [Op.like]: `%${search}%` } },
            { email: { [Op.like]: `%${search}%` } }
          ]
        },
        raw: true
      });
      
      // 提取用户ID数组
      const userIds = matchingUsers.map(user => user.id);
      
      // 将用户ID条件添加到主查询中
      if (userIds.length > 0) {
        where.userId = {
          [Op.in]: userIds
        };
      } else {
        // 如果没有匹配的用户，返回空结果
        return res.json({
          data: [],
          total: 0,
          page,
          totalPages: 0
        });
      }
    }
    
    const { count, rows } = await Exchange.findAndCountAll(queryOptions);
    
    console.log('管理员查询结果数量:', count);
    
    // 格式化数据，保留原始数据库中的orderNumber字段
    const formattedRows = rows.map(exchange => {
      const plainExchange = exchange.get ? exchange.get({ plain: true }) : exchange;
      return plainExchange;
    });
    
    return res.json({
      data: formattedRows,
      total: count,
      page,
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取兑换申请列表失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 管理员更新兑换申请状态
 */
exports.updateExchangeStatus = async (req, res) => {
  try {
    // 检查是否为管理员
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: '无权访问管理员功能' });
    }
    
    const { id } = req.params;
    const { status, adminRemarks, trackingNumber, trackingCompany } = req.body;
    
    // 查找兑换记录
    const exchange = await Exchange.findByPk(id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'stock', 'exchangeCount'], // 确保包含exchangeCount字段
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username', 'email'],
          required: false
        }
      ]
    });
    
    if (!exchange) {
      return res.status(404).json({ message: '兑换记录不存在' });
    }
    
    const oldStatus = exchange.status;
    const oldData = { ...exchange.get() };
    
    console.log(`订单状态更新: ID=${id}, 旧状态=${oldStatus}, 新状态=${status}, 商品ID=${exchange.productId}`);
    
    // 如果订单状态已经是取消或拒绝，则不需要再处理库存和兑换量
    if (oldStatus === 'cancelled' || oldStatus === 'rejected') {
      // 如果当前已经是取消或拒绝状态，只更新必要的备注和物流信息
      await exchange.update({
        adminRemarks: adminRemarks || exchange.adminRemarks,
        trackingNumber: trackingNumber || exchange.trackingNumber,
        trackingCompany: trackingCompany || exchange.trackingCompany
      });
      
      return res.json({
        message: '兑换记录信息已更新，状态未改变',
        exchange
      });
    }
    
    // 处理特殊状态转换：如果订单被取消或拒绝，需要恢复库存和更新兑换量计数
    if ((status === 'cancelled' || status === 'rejected') && (oldStatus === 'pending' || oldStatus === 'approved' || oldStatus === 'shipped')) {
      // 获取完整的产品信息，不使用exchange关联的product，因为可能不完整
      const product = await Product.findByPk(exchange.productId);
      
      if (product) {
        const oldStock = product.stock;
        const oldExchangeCount = product.exchangeCount;
        
        console.log(`准备恢复库存: 商品ID=${product.id}, 商品名=${product.name}, 原库存=${oldStock}, 原兑换量=${oldExchangeCount}, 订单数量=${exchange.quantity}`);
        
        // 恢复库存和兑换量
        await product.update({
          stock: product.stock + exchange.quantity,
          exchangeCount: Math.max(0, product.exchangeCount - exchange.quantity)
        });
        
        console.log(`库存恢复完成: 商品ID=${product.id}, 新库存=${product.stock}, 新兑换量=${product.exchangeCount}`);
        
        // 根据操作类型生成适当的日志描述
        const actionType = status === 'cancelled' ? '取消' : '拒绝';
        const statusDesc = oldStatus === 'pending' ? '待处理' : (oldStatus === 'approved' ? '已批准' : '已发货');
        
        // 记录库存变更日志
        await createLog({
          action: 'stock_update',
          entityType: 'product',
          entityId: product.id,
          oldValue: JSON.stringify({ stock: oldStock, exchangeCount: oldExchangeCount }),
          newValue: JSON.stringify({ stock: product.stock, exchangeCount: product.exchangeCount }),
          userId: req.user.id,
          username: req.user.username,
          description: `由于管理员${actionType}${statusDesc}的兑换(ID: ${exchange.id})，商品"${product.name}"库存从${oldStock}增加到${product.stock}，兑换数量减少${exchange.quantity}`
        }, req);
      } else {
        console.error(`无法恢复库存: 找不到商品(ID: ${exchange.productId})`);
      }
    }
    
    // 更新兑换状态和备注
    await exchange.update({
      status,
      adminRemarks: adminRemarks || exchange.adminRemarks,
      trackingNumber: trackingNumber || exchange.trackingNumber,
      trackingCompany: trackingCompany || exchange.trackingCompany
    });
    
    // 记录状态变更日志
    await createLog({
      action: 'exchange_update',
      entityType: 'exchange',
      entityId: exchange.id,
      oldValue: JSON.stringify({ status: oldStatus }),
      newValue: JSON.stringify({ status }),
      userId: req.user.id,
      username: req.user.username,
      description: `管理员"${req.user.username}"将兑换申请(ID: ${exchange.id})的状态从"${getStatusText(oldStatus)}"修改为"${getStatusText(status)}"`
    }, req);
    
    // 向用户发送状态变更通知
    if (exchange.User && exchange.User.id && oldStatus !== status) {
      try {
        let notificationContent = '';
        
        // 根据不同状态生成不同的通知内容
        switch(status) {
          case 'approved':
            notificationContent = `您的兑换申请已批准，商品：${exchange.Product?.name || '未知商品'}`;
            break;
          case 'shipped':
            notificationContent = `您兑换的商品已发货，商品：${exchange.Product?.name || '未知商品'}${trackingNumber ? `，物流单号：${trackingNumber}` : ''}`;
            break;
          case 'completed':
            notificationContent = `您的兑换申请已完成，商品：${exchange.Product?.name || '未知商品'}`;
            break;
          case 'rejected':
            notificationContent = `很抱歉，您的兑换申请已被拒绝，商品：${exchange.Product?.name || '未知商品'}`;
            break;
          case 'cancelled':
            notificationContent = `您的兑换申请已被取消，商品：${exchange.Product?.name || '未知商品'}`;
            break;
          default:
            notificationContent = `您的兑换申请状态已更新为：${getStatusText(status)}，商品：${exchange.Product?.name || '未知商品'}`;
        }
        
        // 如果有管理员备注，添加到通知内容
        if (adminRemarks) {
          notificationContent += `，管理员备注：${adminRemarks}`;
        }
        
        // 创建通知
        await createNotification({
          type: 'exchange',
          sourceId: exchange.id,
          title: '兑换申请状态更新',
          content: notificationContent,
          recipientId: exchange.userId
        });
      } catch (error) {
        console.error('创建兑换状态通知失败:', error);
        // 不阻止主流程
      }
    }
    
    return res.json({
      message: '兑换记录状态已更新',
      exchange
    });
  } catch (error) {
    console.error('更新兑换记录状态失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试' });
  }
};

/**
 * 获取最近的兑换订单（管理员用）
 */
exports.getRecentExchanges = async (req, res) => {
  try {
    console.log('调用最近订单API，用户角色:', req.user ? req.user.role : '未认证');
    
    // 检查是否为管理员
    if (!req.user || req.user.role !== 'admin') {
      console.log('非管理员尝试访问订单数据');
      return res.status(403).json({ 
        message: '无权访问管理员功能',
        authenticated: !!req.user,
        userRole: req.user ? req.user.role : null
      });
    }
    
    const limit = parseInt(req.query.limit) || 7;
    console.log(`查询最近 ${limit} 条订单`);
    
    // 查询最近的兑换订单，包括产品和用户信息
    const exchanges = await Exchange.findAll({
      order: [['createdAt', 'DESC']],
      limit,
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'lyPrice', 'rmbPrice'],
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username', 'department'],
          required: false
        }
      ]
    });
    
    console.log(`找到 ${exchanges.length} 个订单记录`);
    
    // 添加支付方式统计
    const paymentStats = {
      ly: 0,
      rmb: 0,
      unknown: 0
    };
    
    exchanges.forEach(exchange => {
      if (exchange.paymentMethod === 'ly') {
        paymentStats.ly++;
      } else if (exchange.paymentMethod === 'rmb') {
        paymentStats.rmb++;
      } else {
        paymentStats.unknown++;
      }
    });
    
    console.log('订单支付方式统计:', JSON.stringify(paymentStats));
    
    // 如果没有记录，返回空数组
    if (exchanges.length === 0) {
      return res.json([]);
    }
    
    // 格式化数据以匹配前端需要的结构
    const formattedExchanges = exchanges.map(exchange => {
      // 使用plain JavaScript对象而不是Sequelize模型
      const exchangeData = exchange.get ? exchange.get({ plain: true }) : exchange;
      
      const product = exchangeData.Product || {};
      const user = exchangeData.User || {};
      
      // 记录详细的支付方式信息以便调试
      console.log(`处理订单 ID:${exchangeData.id}, 原始支付方式:${JSON.stringify(exchangeData.paymentMethod)}, 商品:${product.name || '未知'}`);
      
      // 计算订单金额
      let amount = 0;
      let finalPaymentMethod = 'rmb'; // 默认为人民币
      
      if (exchangeData.paymentMethod && (exchangeData.paymentMethod === 'ly' || exchangeData.paymentMethod.toString() === 'ly')) {
        finalPaymentMethod = 'ly';
        amount = (product.lyPrice || 0) * exchangeData.quantity || 0;
        console.log(`光年币订单: ${product.lyPrice || 0} * ${exchangeData.quantity || 1} = ${amount}`);
      } else {
        finalPaymentMethod = 'rmb';
        amount = parseFloat(product.rmbPrice || 0) * exchangeData.quantity || 0;
        console.log(`人民币订单: ${product.rmbPrice || 0} * ${exchangeData.quantity || 1} = ${amount}`);
      }
      
      // 将状态翻译为中文
      let status = '未知';
      switch(exchangeData.status) {
        case 'pending': status = '处理中'; break;
        case 'approved': status = '已批准'; break;
        case 'shipped': status = '已发货'; break;
        case 'completed': status = '已完成'; break;
        case 'rejected': status = '已拒绝'; break;
        case 'cancelled': status = '已取消'; break;
      }
      
      // 获取订单编号，优先使用格式化的订单编号
      let orderId = exchangeData.orderNumber;
      
      // 如果没有orderNumber，按照标准格式生成
      if (!orderId) {
        const today = new Date(exchangeData.createdAt || new Date());
        const dateStr = today.getFullYear().toString() + 
                       (today.getMonth() + 1).toString().padStart(2, '0') + 
                       today.getDate().toString().padStart(2, '0');
        
        const prefix = exchangeData.paymentMethod === 'ly' ? 'GNB-' : 'RMB-';
        orderId = prefix + dateStr + exchangeData.id.toString().padStart(8, '0');
      }
      
      // 确保返回明确的支付方式
      const formattedOrder = {
        id: orderId,
        rawId: exchangeData.id, // 保留原始ID用于后端操作
        customer: user.username || '未知用户',
        date: new Date(exchangeData.createdAt).toISOString().split('T')[0],
        amount: amount,
        status: status,
        productName: product.name || '未知商品',
        paymentMethod: finalPaymentMethod, // 明确设置支付方式
        quantity: exchangeData.quantity || 1
      };
      
      // 打印最终生成的订单数据
      console.log(`最终订单数据: ID=${formattedOrder.id}, 支付方式=${formattedOrder.paymentMethod}, 金额=${formattedOrder.amount}`);
      
      return formattedOrder;
    });
    
    console.log('成功格式化数据, 返回订单数量:', formattedExchanges.length);
    // 显示每个订单的支付方式
    formattedExchanges.forEach((order, index) => {
      console.log(`订单[${index}]: ID=${order.id}, 支付方式=${order.paymentMethod}, 金额=${order.amount}`);
    });
    
    return res.json(formattedExchanges);
  } catch (error) {
    console.error('获取最近订单失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 获取职场分布统计数据
 */
exports.getWorkplaceExchangeStats = async (req, res) => {
  try {
    console.log('[API] 调用职场分布统计数据API');
    
    // 检查是否为管理员
    if (!req.user || req.user.role !== 'admin') {
      console.log('[API] 非管理员尝试访问职场分布数据');
      return res.status(403).json({ 
        message: '无权访问管理员功能',
        authenticated: !!req.user,
        userRole: req.user ? req.user.role : null
      });
    }
    
    // 使用Sequelize查询按location分组并计算总数，分别统计光年币和人民币
    const workplaceStats = await Exchange.findAll({
      attributes: [
        'location',
        [sequelize.fn('COUNT', sequelize.col('Exchange.id')), 'count'],
        [sequelize.fn('SUM', sequelize.literal('CASE WHEN paymentMethod = "ly" THEN quantity * Product.lyPrice ELSE 0 END')), 'lyAmount'],
        [sequelize.fn('SUM', sequelize.literal('CASE WHEN paymentMethod = "rmb" THEN quantity * Product.rmbPrice ELSE 0 END')), 'rmbAmount']
      ],
      include: [{
        model: Product,
        attributes: [],
        required: true
      }],
      where: {
        status: {
          [Op.in]: ['completed', 'shipped', 'approved'] // 只统计已完成、已发货和已批准的订单
        },
        location: {
          [Op.not]: null, // 排除没有职场信息的订单
          [Op.ne]: '' // 排除空字符串
        }
      },
      group: ['location'],
      raw: true
    });

    console.log(`[API] 找到 ${workplaceStats.length} 个职场统计数据`);
    
    // 如果没有数据，提供一些默认数据以避免前端错误
    if (!workplaceStats || workplaceStats.length === 0) {
      console.log('[API] 没有找到职场统计数据，返回默认空数据');
      return res.json([
        { name: '暂无数据', value: 0, rmbAmount: 0, lyAmount: 0, count: 0 }
      ]);
    }
    
    // 格式化数据以匹配前端需要的结构，分别包含光年币和人民币数据
    const formattedStats = workplaceStats.map(stat => {
      // 处理null值，确保数值有效
      const rmbAmount = parseFloat(stat.rmbAmount || 0);
      const lyAmount = parseFloat(stat.lyAmount || 0);
      
      return {
        name: stat.location || '未知职场',
        value: rmbAmount + lyAmount,
        rmbAmount: rmbAmount,
        lyAmount: lyAmount,
        count: parseInt(stat.count, 10) || 0
      };
    });
    
    // 对结果排序，确保数据合理显示
    formattedStats.sort((a, b) => b.value - a.value);
    
    // 只返回前10个职场，避免图表过于拥挤
    const topWorkplaces = formattedStats.slice(0, 10);
    
    console.log('[API] 成功格式化职场统计数据，返回', topWorkplaces.length, '条数据');
    return res.json(topWorkplaces);
  } catch (error) {
    console.error('[API] 获取职场分布统计数据失败:', error);
    return res.status(500).json({ 
      message: '服务器错误，请稍后重试',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * 辅助函数：格式化金额，确保返回有效数字
 */
function formatAmount(amount, isInteger = false) {
  if (isInteger) {
    // 光年币需要是整数
    return parseInt(amount) || 0;
  } else {
    // 人民币保留两位小数
    return parseFloat(parseFloat(amount).toFixed(2)) || 0;
  }
}

/**
 * 获取仪表盘统计数据
 */
exports.getDashboardStats = async (req, res) => {
  try {
    console.log('[API-DEBUG] 正在获取仪表盘统计数据...');
    
    // 检查是否为管理员
    if (!req.user || req.user.role !== 'admin') {
      console.log('[API-DEBUG] 非管理员尝试访问仪表盘数据:', req.user ? req.user.username : '未认证用户');
      return res.status(403).json({ 
        message: '无权访问管理员功能',
        authenticated: !!req.user,
        userRole: req.user ? req.user.role : null
      });
    }
    
    console.log('[API-DEBUG] 管理员用户:', req.user.username, '授权获取仪表盘数据');
    
    // 定义时间范围
    const now = new Date();
    
    // 计算30天前的日期
    const thirtyDaysAgo = new Date(now);
    thirtyDaysAgo.setDate(now.getDate() - 30);
    
    // 计算60天前的日期
    const sixtyDaysAgo = new Date(now);
    sixtyDaysAgo.setDate(now.getDate() - 60);
    
    // --- 统计总订单数 ---
    const totalOrders = await Exchange.count({
      where: {
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        }
      }
    });
    console.log('[API-DEBUG] 总订单数:', totalOrders);
    
    // 计算近30天订单数
    const last30DaysOrders = await Exchange.count({
      where: {
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: thirtyDaysAgo
        }
      }
    });
    
    // 计算前30天的订单数 (30-60天)
    const previous30DaysOrders = await Exchange.count({
      where: {
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: sixtyDaysAgo,
          [Op.lt]: thirtyDaysAgo
        }
      }
    });
    
    // 计算订单增长率
    const salesGrowth = previous30DaysOrders === 0 
      ? (last30DaysOrders === 0 ? 0 : 100)
      : Math.round(((last30DaysOrders - previous30DaysOrders) / previous30DaysOrders) * 100);
    
    console.log('[API-DEBUG] 订单增长率:', salesGrowth, '%', '(', last30DaysOrders, '/', previous30DaysOrders, ')');
    
    // --- 统计光年币订单总收入 ---
    const lySalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'ly',
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        }
      },
      raw: true
    });
    
    // 从结果中获取总和值
    const lySales = lySalesResult && lySalesResult.total ? parseFloat(lySalesResult.total) : 0;
    console.log('[API-DEBUG] 光年币总收入(从totalAmount):', lySales);
    
    // 近30天光年币收入
    const last30DaysLySalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'ly',
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: thirtyDaysAgo
        }
      },
      raw: true
    });
    
    const last30DaysLySales = last30DaysLySalesResult && last30DaysLySalesResult.total 
      ? parseFloat(last30DaysLySalesResult.total) 
      : 0;
    
    // 前30天光年币收入
    const previous30DaysLySalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'ly',
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: sixtyDaysAgo,
          [Op.lt]: thirtyDaysAgo
        }
      },
      raw: true
    });
    
    const previous30DaysLySales = previous30DaysLySalesResult && previous30DaysLySalesResult.total 
      ? parseFloat(previous30DaysLySalesResult.total) 
      : 0;
    
    // 计算光年币收入增长率
    const lySalesGrowth = previous30DaysLySales === 0 
      ? (last30DaysLySales === 0 ? 0 : 100)
      : Math.round(((last30DaysLySales - previous30DaysLySales) / previous30DaysLySales) * 100);
    
    console.log('[API-DEBUG] 光年币收入增长率:', lySalesGrowth, '%', '(', last30DaysLySales, '/', previous30DaysLySales, ')');
    
    // --- 统计人民币订单总收入 ---
    const rmbSalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'rmb',
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        }
      },
      raw: true
    });
    
    // 从结果中获取总和值
    const rmbSales = rmbSalesResult && rmbSalesResult.total ? parseFloat(rmbSalesResult.total) : 0;
    console.log('[API-DEBUG] 人民币总收入(从totalAmount):', rmbSales);
    
    // 近30天人民币收入
    const last30DaysRmbSalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'rmb',
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: thirtyDaysAgo
        }
      },
      raw: true
    });
    
    const last30DaysRmbSales = last30DaysRmbSalesResult && last30DaysRmbSalesResult.total 
      ? parseFloat(last30DaysRmbSalesResult.total) 
      : 0;
    
    // 前30天人民币收入
    const previous30DaysRmbSalesResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'total']
      ],
      where: {
        paymentMethod: 'rmb',
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: sixtyDaysAgo,
          [Op.lt]: thirtyDaysAgo
        }
      },
      raw: true
    });
    
    const previous30DaysRmbSales = previous30DaysRmbSalesResult && previous30DaysRmbSalesResult.total 
      ? parseFloat(previous30DaysRmbSalesResult.total) 
      : 0;
    
    // 计算人民币收入增长率
    const rmbSalesGrowth = previous30DaysRmbSales === 0 
      ? (last30DaysRmbSales === 0 ? 0 : 100)
      : Math.round(((last30DaysRmbSales - previous30DaysRmbSales) / previous30DaysRmbSales) * 100);
    
    console.log('[API-DEBUG] 人民币收入增长率:', rmbSalesGrowth, '%', '(', last30DaysRmbSales, '/', previous30DaysRmbSales, ')');
    
    // --- 统计活跃用户数 ---
    const activeUsers = await Exchange.count({
      distinct: true,
      col: 'userId',
      where: {
        createdAt: {
          [Op.gte]: thirtyDaysAgo
        }
      }
    });
    console.log('[API-DEBUG] 30天内活跃用户数:', activeUsers);
    
    // 前30天活跃用户
    const previous30DaysActiveUsers = await Exchange.count({
      distinct: true,
      col: 'userId',
      where: {
        createdAt: {
          [Op.gte]: sixtyDaysAgo,
          [Op.lt]: thirtyDaysAgo
        }
      }
    });
    
    // 计算活跃用户增长率
    const activeUsersGrowth = previous30DaysActiveUsers === 0 
      ? (activeUsers === 0 ? 0 : 100)
      : Math.round(((activeUsers - previous30DaysActiveUsers) / previous30DaysActiveUsers) * 100);
    
    console.log('[API-DEBUG] 活跃用户增长率:', activeUsersGrowth, '%', '(', activeUsers, '/', previous30DaysActiveUsers, ')');
    
    // --- 统计总商品数 ---
    const totalProducts = await Product.count();
    
    // 获取30天前的商品总数（通过创建日期判断）
    const thirtyDaysAgoProductsCount = await Product.count({
      where: {
        createdAt: {
          [Op.lt]: thirtyDaysAgo
        }
      }
    });
    
    // 计算商品数增长率
    const totalProductsGrowth = thirtyDaysAgoProductsCount === 0 
      ? (totalProducts === 0 ? 0 : 100)
      : Math.round(((totalProducts - thirtyDaysAgoProductsCount) / thirtyDaysAgoProductsCount) * 100);
    
    console.log('[API-DEBUG] 商品总数增长率:', totalProductsGrowth, '%', '(', totalProducts, '/', thirtyDaysAgoProductsCount, ')');
    
    // --- 统计总反馈数 ---
    const totalFeedbacks = await Feedback.count();
    
    // 近30天反馈
    const last30DaysFeedbacks = await Feedback.count({
      where: {
        createdAt: {
          [Op.gte]: thirtyDaysAgo
        }
      }
    });
    
    // 前30天反馈
    const previous30DaysFeedbacks = await Feedback.count({
      where: {
        createdAt: {
          [Op.gte]: sixtyDaysAgo,
          [Op.lt]: thirtyDaysAgo
        }
      }
    });
    
    // 计算反馈数增长率
    const totalFeedbacksGrowth = previous30DaysFeedbacks === 0 
      ? (last30DaysFeedbacks === 0 ? 0 : 100)
      : Math.round(((last30DaysFeedbacks - previous30DaysFeedbacks) / previous30DaysFeedbacks) * 100);
    
    console.log('[API-DEBUG] 反馈数增长率:', totalFeedbacksGrowth, '%', '(', last30DaysFeedbacks, '/', previous30DaysFeedbacks, ')');
    
    // --- 统计总兑换数量 ---
    const totalExchangeResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('quantity')), 'total']
      ],
      where: {
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        }
      },
      raw: true
    });

    const totalExchangeQuantity = totalExchangeResult && totalExchangeResult.total ? parseInt(totalExchangeResult.total) : 0;
    console.log('[API-DEBUG] 总兑换数量:', totalExchangeQuantity);
    
    // 近30天兑换数量
    const last30DaysExchangeResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('quantity')), 'total']
      ],
      where: {
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: thirtyDaysAgo
        }
      },
      raw: true
    });
    
    const last30DaysExchangeQuantity = last30DaysExchangeResult && last30DaysExchangeResult.total 
      ? parseInt(last30DaysExchangeResult.total) 
      : 0;
    
    // 前30天兑换数量
    const previous30DaysExchangeResult = await Exchange.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('quantity')), 'total']
      ],
      where: {
        status: {
          [Op.in]: ['approved', 'shipped', 'completed']
        },
        createdAt: {
          [Op.gte]: sixtyDaysAgo,
          [Op.lt]: thirtyDaysAgo
        }
      },
      raw: true
    });
    
    const previous30DaysExchangeQuantity = previous30DaysExchangeResult && previous30DaysExchangeResult.total 
      ? parseInt(previous30DaysExchangeResult.total) 
      : 0;
    
    // 计算兑换数量增长率
    const totalExchangeQuantityGrowth = previous30DaysExchangeQuantity === 0 
      ? (last30DaysExchangeQuantity === 0 ? 0 : 100)
      : Math.round(((last30DaysExchangeQuantity - previous30DaysExchangeQuantity) / previous30DaysExchangeQuantity) * 100);
    
    console.log('[API-DEBUG] 兑换数量增长率:', totalExchangeQuantityGrowth, '%', '(', last30DaysExchangeQuantity, '/', previous30DaysExchangeQuantity, ')');
    
    // --- 统计总库存数量 ---
    const totalStockResult = await Product.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('stock')), 'total']
      ],
      raw: true
    });

    const totalStock = totalStockResult && totalStockResult.total ? parseInt(totalStockResult.total) : 0;
    console.log('[API-DEBUG] 总库存数量:', totalStock);
    
    // 计算库存变化
    // 注：库存是一个特殊指标，没有历史记录可查，只能通过当前值和订单量推算
    // 我们假设库存变化率基于近30天的订单量与总库存的比率
    const stockChangeRate = totalStock === 0 
      ? (last30DaysExchangeQuantity === 0 ? 0 : 100) 
      : Math.round((last30DaysExchangeQuantity / totalStock) * 100);
    
    // 库存趋势方向：如果近期兑换量大于0，趋势为下降，因为兑换减少库存
    const stockTrend = last30DaysExchangeQuantity > 0 ? 'down' : 'up';
    
    console.log('[API-DEBUG] 库存变化率:', stockChangeRate, '%', 'trend:', stockTrend);
    
    // 封装并返回统计数据
    const dashboardStats = {
      totalSales: {
        value: totalOrders,
        growth: salesGrowth,
        trend: salesGrowth >= 0 ? 'up' : 'down'
      },
      lySales: {
        value: formatAmount(lySales, true),
        unit: '个光年币',
        growth: lySalesGrowth,
        trend: lySalesGrowth >= 0 ? 'up' : 'down'
      },
      rmbSales: {
        value: formatAmount(rmbSales),
        unit: '元',
        growth: rmbSalesGrowth,
        trend: rmbSalesGrowth >= 0 ? 'up' : 'down'
      },
      activeUsers: {
        value: activeUsers,
        growth: activeUsersGrowth,
        trend: activeUsersGrowth >= 0 ? 'up' : 'down'
      },
      totalProducts: {
        value: totalProducts,
        growth: totalProductsGrowth,
        trend: totalProductsGrowth >= 0 ? 'up' : 'down'
      },
      totalFeedbacks: {
        value: totalFeedbacks,
        growth: totalFeedbacksGrowth,
        trend: totalFeedbacksGrowth >= 0 ? 'up' : 'down'
      },
      totalExchangeQuantity: {
        value: totalExchangeQuantity,
        growth: totalExchangeQuantityGrowth,
        trend: totalExchangeQuantityGrowth >= 0 ? 'up' : 'down'
      },
      totalStock: {
        value: totalStock,
        growth: Math.abs(stockChangeRate),
        trend: stockTrend
      }
    };
    
    console.log('[API-DEBUG] 返回的统计数据：', JSON.stringify(dashboardStats, null, 2));
    
    return res.json(dashboardStats);
  } catch (error) {
    console.error('获取仪表盘统计数据失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试' });
  }
};

/**
 * 获取销售趋势数据
 */
exports.getSalesTrend = async (req, res) => {
  try {
    console.log('调用销售趋势数据API');
    
    // 检查是否为管理员
    if (!req.user || req.user.role !== 'admin') {
      console.log('非管理员尝试访问销售趋势数据');
      return res.status(403).json({ 
        message: '无权访问管理员功能',
        authenticated: !!req.user,
        userRole: req.user ? req.user.role : null
      });
    }
    
    // 获取请求参数
    const period = req.query.period || 'month'; // 默认按月
    
    // 获取日期范围
    let startDate, endDate;
    const now = new Date();
    endDate = now;
    
    // 根据时间段设置起始日期
    switch(period) {
      case 'week':
        // 最近7天
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        // 最近30天
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 30);
        break;
      case 'year':
        // 最近12个月
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 12);
        break;
      default:
        // 默认30天
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 30);
    }
    
    console.log(`查询销售趋势，时间段:${period}，起始日期:${startDate.toISOString()}, 结束日期:${endDate.toISOString()}`);
    
    // 定义时间格式化函数
    const formatDate = (date, periodType) => {
      const d = new Date(date);
      if (periodType === 'week') {
        // 格式为 MM-DD
        return `${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
      } else if (periodType === 'month') {
        // 格式为 MM-DD
        return `${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
      } else {
        // 年份格式为 YYYY-MM
        return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}`;
      }
    };
    
    // 生成时间区间数组
    const timeSlots = [];
    const timeFormat = period === 'year' ? 'YYYY-MM' : 'MM-DD';
    
    if (period === 'week' || period === 'month') {
      // 按天分组
      const dayCount = period === 'week' ? 7 : 30;
      for (let i = 0; i < dayCount; i++) {
        const d = new Date(endDate);
        d.setDate(d.getDate() - (dayCount - 1 - i));
        timeSlots.push({
          date: formatDate(d, period),
          timestamp: d
        });
      }
    } else {
      // 按月分组
      for (let i = 0; i < 12; i++) {
        const d = new Date(endDate);
        d.setMonth(d.getMonth() - (11 - i));
        d.setDate(1); // 月初
        timeSlots.push({
          date: formatDate(d, period),
          timestamp: d
        });
      }
    }
    
    // 查询每个时间段的销售额和订单数
    const rmbSalesData = [];
    const lySalesData = [];
    const orderData = [];
    
    // 获取所有时间段内的交易数据
    const exchanges = await Exchange.findAll({
      attributes: [
        'id', 'quantity', 'paymentMethod', 'status', 'createdAt', 'totalAmount'
      ],
      where: {
        status: {
          [Op.in]: ['completed', 'shipped', 'approved']
        },
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      },
      order: [['createdAt', 'ASC']]
    });
    
    console.log(`查询到 ${exchanges.length} 条交易记录`);
    
    // 按时间段统计数据
    timeSlots.forEach(slot => {
      let slotStart, slotEnd;
      
      if (period === 'year') {
        // 按月计算，获取月初和月末
        const [year, month] = slot.date.split('-').map(Number);
        slotStart = new Date(year, month - 1, 1);
        slotEnd = new Date(year, month, 0, 23, 59, 59);
      } else {
        // 按天计算，获取当天开始和结束
        const currDate = new Date(slot.timestamp);
        slotStart = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate());
        slotEnd = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 23, 59, 59);
      }
      
      // 筛选该时间段的交易
      const slotExchanges = exchanges.filter(exchange => {
        const exchangeDate = new Date(exchange.createdAt);
        return exchangeDate >= slotStart && exchangeDate <= slotEnd;
      });
      
      // 计算该时间段的销售额(分光年币和人民币)和订单数
      let slotLySales = 0;
      let slotRmbSales = 0;
      
      slotExchanges.forEach(exchange => {
        // 使用totalAmount字段计算订单金额
        if (exchange.paymentMethod === 'ly') {
          slotLySales += parseFloat(exchange.totalAmount || 0);
        } else {
          slotRmbSales += parseFloat(exchange.totalAmount || 0);
        }
      });
      
      lySalesData.push(parseFloat(slotLySales.toFixed(2)));
      rmbSalesData.push(parseFloat(slotRmbSales.toFixed(2)));
      orderData.push(slotExchanges.length);
    });
    
    // 返回结果
    const result = {
      timeLabels: timeSlots.map(slot => slot.date),
      lySalesData,
      rmbSalesData,
      orderData,
      period,
      timeFormat
    };
    
    console.log('销售趋势数据处理完成');
    return res.json(result);
  } catch (error) {
    console.error('获取销售趋势数据失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

// 辅助函数：获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'approved': '已批准',
    'shipped': '已发货',
    'completed': '已完成',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
}

/**
 * 用户更新联系方式（仅限待处理和已批准状态）
 */
exports.updateContactInfo = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { contactInfo, location } = req.body;
    
    // 查找兑换记录
    const exchange = await Exchange.findOne({
      where: { 
        id,
        userId,
        // 只允许在待处理或已批准状态下修改
        status: {
          [Op.in]: ['pending', 'approved']
        }
      },
      include: [{
        model: Product,
        attributes: ['id', 'name'],
        required: false
      }]
    });
    
    if (!exchange) {
      return res.status(404).json({ 
        message: '兑换记录不存在或当前状态不允许修改联系方式' 
      });
    }
    
    const oldContactInfo = exchange.contactInfo;
    const oldLocation = exchange.location;
    
    // 更新联系方式
    await exchange.update({
      contactInfo,
      location
    });
    
    // 记录联系方式变更日志
    await createLog({
      action: 'exchange_contact_update',
      entityType: 'exchange',
      entityId: exchange.id,
      oldValue: JSON.stringify({ 
        contactInfo: oldContactInfo, 
        location: oldLocation 
      }),
      newValue: JSON.stringify({ 
        contactInfo, 
        location 
      }),
      userId: req.user.id,
      username: req.user.username,
      description: `用户"${req.user.username}"修改了订单(ID: ${exchange.id})的联系方式和位置信息`
    }, req);
    
    return res.json({ 
      message: '联系方式更新成功',
      data: exchange
    });
  } catch (error) {
    console.error('更新联系方式失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 管理员更新联系方式（所有状态）
 */
exports.adminUpdateContactInfo = async (req, res) => {
  try {
    // 检查是否为管理员
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: '无权访问管理员功能' });
    }
    
    const { id } = req.params;
    const { contactInfo, location, adminRemarks } = req.body;
    
    // 查找兑换记录
    const exchange = await Exchange.findByPk(id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username', 'email'],
          required: false
        }
      ]
    });
    
    if (!exchange) {
      return res.status(404).json({ message: '兑换记录不存在' });
    }
    
    const oldContactInfo = exchange.contactInfo;
    const oldLocation = exchange.location;
    
    // 更新联系方式和管理员备注
    await exchange.update({
      contactInfo,
      location,
      adminRemarks: adminRemarks || exchange.adminRemarks
    });
    
    // 记录联系方式变更日志
    await createLog({
      action: 'exchange_contact_update_admin',
      entityType: 'exchange',
      entityId: exchange.id,
      oldValue: JSON.stringify({ 
        contactInfo: oldContactInfo, 
        location: oldLocation 
      }),
      newValue: JSON.stringify({ 
        contactInfo, 
        location 
      }),
      userId: req.user.id,
      username: req.user.username,
      description: `管理员"${req.user.username}"修改了订单(ID: ${exchange.id})的联系方式和位置信息`
    }, req);
    
    // 向用户发送联系方式变更通知
    if (exchange.User && exchange.User.id) {
      try {
        const notificationContent = `您的订单(ID: ${exchange.id})联系方式已被管理员修改为：联系方式 - ${contactInfo}，位置 - ${location}`;
        
        // 创建通知
        await createNotification({
          type: 'exchange',
          sourceId: exchange.id,
          title: '订单联系方式更新',
          content: notificationContent,
          recipientId: exchange.userId
        });
      } catch (error) {
        console.error('创建联系方式更新通知失败:', error);
        // 不阻止主流程
      }
    }
    
    return res.json({ 
      message: '联系方式更新成功',
      data: exchange
    });
  } catch (error) {
    console.error('管理员更新联系方式失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 导出订单数据
 */
exports.exportExchanges = async (req, res) => {
  try {
    const format = req.query.format || 'csv';
    const status = req.query.status || '';
    const search = req.query.search || '';
    const startDate = req.query.startDate ? new Date(req.query.startDate) : null;
    const endDate = req.query.endDate ? new Date(req.query.endDate) : null;
    
    console.log(`开始导出订单数据，格式：${format}，状态过滤：${status}，搜索关键词：${search}`);
    
    // 构建查询条件
    const whereConditions = {};
    
    // 状态过滤
    if (status) {
      whereConditions.status = status;
    }
    
    // 日期范围过滤
    if (startDate && endDate) {
      whereConditions.createdAt = {
        [Op.between]: [startDate, endDate]
      };
    } else if (startDate) {
      whereConditions.createdAt = {
        [Op.gte]: startDate
      };
    } else if (endDate) {
      whereConditions.createdAt = {
        [Op.lte]: endDate
      };
    }
    
    // 查询订单数据，包括关联的用户和商品信息
    let exchanges = await Exchange.findAll({
      where: whereConditions,
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'lyPrice', 'rmbPrice'],
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username', 'email', 'department'],
          where: search ? {
            [Op.or]: [
              { username: { [Op.like]: `%${search}%` } },
              { email: { [Op.like]: `%${search}%` } },
              { department: { [Op.like]: `%${search}%` } }
            ]
          } : {},
          required: search ? true : false
        }
      ],
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`查询到 ${exchanges.length} 条订单记录`);
    
    // 记录操作日志
    await createLog({
      action: 'DATA_EXPORT',
      entityType: 'EXCHANGE',
      entityId: 0,
      userId: req.user.id,
      username: req.user.username,
      description: `管理员 ${req.user.username} 导出了订单数据，共 ${exchanges.length} 条记录`
    }, req);
    
    // 转换为纯JavaScript对象
    exchanges = exchanges.map(exchange => exchange.get({ plain: true }));
    
    // 根据格式导出数据
    if (format === 'excel') {
      await exportToExcel(res, exchanges);
    } else {
      await exportToCsv(res, exchanges);
    }
  } catch (error) {
    console.error('导出订单数据失败:', error);
    return res.status(500).json({ message: '导出订单数据失败', error: error.message });
  }
};

/**
 * 导入订单数据
 */
exports.importExchanges = async (req, res) => {
  try {
    // 检查是否上传了文件
    if (!req.files || !req.files.file) {
      return res.status(400).json({ message: '请选择要导入的文件' });
    }
    
    const file = req.files.file;
    const fileExtension = path.extname(file.name).toLowerCase();
    
    // 检查文件格式
    if (!['.csv', '.xlsx', '.xls'].includes(fileExtension)) {
      return res.status(400).json({ message: '只支持CSV或Excel文件格式' });
    }
    
    // 保存文件到临时目录
    const tempDir = os.tmpdir();
    const tempFilePath = path.join(tempDir, `import_${Date.now()}${fileExtension}`);
    
    await file.mv(tempFilePath);
    console.log(`文件保存到临时目录: ${tempFilePath}`);
    
    // 根据文件格式解析数据
    let ordersData = [];
    if (fileExtension === '.csv') {
      ordersData = await parseCSV(tempFilePath);
    } else {
      ordersData = await parseExcel(tempFilePath);
    }
    
    console.log(`解析到 ${ordersData.length} 条订单数据`);
    
    // 添加详细的数据结构调试
    if (ordersData.length > 0) {
      console.log('=== 解析数据调试信息 ===');
      console.log('第一条记录的字段:', Object.keys(ordersData[0]));
      console.log('第一条记录的完整数据:', JSON.stringify(ordersData[0], null, 2));
      
      // 检查关键字段是否存在
      const firstRow = ordersData[0];
      console.log('用户邮箱字段值:', firstRow['用户邮箱']);
      console.log('商品名称字段值:', firstRow['商品名称']);
      console.log('数量字段值:', firstRow['数量']);
      console.log('支付方式字段值:', firstRow['支付方式']);
      console.log('联系方式字段值:', firstRow['联系方式']);
      console.log('位置字段值:', firstRow['位置']);
      console.log('=== 调试信息结束 ===');
    }
    
    // 验证并保存数据
    const importResult = await processImportData(ordersData, req.user);
    
    // 记录操作日志
    await createLog({
      action: 'DATA_IMPORT',
      entityType: 'EXCHANGE',
      entityId: 0,
      userId: req.user.id,
      username: req.user.username,
      description: `管理员 ${req.user.username} 导入了订单数据，成功 ${importResult.success} 条，失败 ${importResult.errors.length} 条`
    }, req);
    
    // 删除临时文件
    fs.unlink(tempFilePath, (err) => {
      if (err) console.error('删除临时文件失败:', err);
    });
    
    return res.json(importResult);
  } catch (error) {
    console.error('导入订单数据失败:', error);
    return res.status(500).json({ message: '导入订单数据失败', error: error.message });
  }
};

/**
 * 获取订单导入模板
 */
exports.getExchangeTemplate = async (req, res) => {
  try {
    const format = req.query.format || 'csv';
    console.log(`生成订单导入模板，格式：${format}`);
    
    // 模板数据结构
    const templateHeaders = [
      '用户邮箱', '商品名称', '数量', '支付方式', '联系方式', '位置', '订单状态', '备注', '管理员备注'
    ];
    
    // 示例数据
    const exampleData = [
      {
        '用户邮箱': '<EMAIL>',
        '商品名称': '示例商品',
        '数量': '1',
        '支付方式': 'ly',
        '联系方式': '13800138000',
        '位置': '北京',
        '订单状态': 'pending',
        '备注': '用户备注',
        '管理员备注': '管理员备注'
      }
    ];
    
    if (format === 'excel') {
      // 创建工作簿和工作表
      const workbook = new ExcelJS.Workbook();
      workbook.creator = '光年汇兑系统';
      workbook.created = new Date();
      
      // 添加说明工作表
      const infoSheet = workbook.addWorksheet('使用说明');
      infoSheet.addRow(['订单导入模板说明']);
      infoSheet.addRow(['1. 用户邮箱：必填，用于关联用户，系统中必须存在该邮箱用户']);
      infoSheet.addRow(['2. 商品名称：必填，用于关联商品，系统中必须存在该商品']);
      infoSheet.addRow(['3. 数量：必填，订单商品数量，必须为正整数']);
      infoSheet.addRow(['4. 支付方式：必填，可选值：ly（光年币）或rmb（人民币）']);
      infoSheet.addRow(['5. 联系方式：必填，用户联系方式']);
      infoSheet.addRow(['6. 位置：必填，用户所在职场位置，可选值：北京、武汉、长沙、西安']);
      infoSheet.addRow(['7. 订单状态：选填，默认为pending，可选值：pending、approved、shipped、completed、rejected、cancelled']);
      infoSheet.addRow(['8. 备注：选填，用户备注']);
      infoSheet.addRow(['9. 管理员备注：选填，管理员备注']);
      
      // 添加模板工作表
      const templateSheet = workbook.addWorksheet('导入模板');
      
      // 添加表头
      const headerRow = templateSheet.addRow(templateHeaders);
      headerRow.font = { bold: true };
      
      // 设置列宽
      templateHeaders.forEach((header, index) => {
        templateSheet.getColumn(index + 1).width = 20;
      });
      
      // 添加示例数据
      exampleData.forEach(data => {
        templateSheet.addRow(Object.values(data));
      });
      
      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=order_import_template.xlsx`);
      
      // 写入响应
      await workbook.xlsx.write(res);
      res.end();
    } else {
      // 生成CSV模板
      const csvContent = parse(exampleData, { fields: templateHeaders });
      
      // 设置响应头
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=order_import_template.csv`);
      
      // 发送CSV内容
      res.send(csvContent);
    }
  } catch (error) {
    console.error('生成订单导入模板失败:', error);
    return res.status(500).json({ message: '生成订单导入模板失败', error: error.message });
  }
};

/**
 * 导出为Excel格式
 */
async function exportToExcel(res, exchanges) {
  // 创建工作簿和工作表
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('订单数据');
  
  // 定义列
  worksheet.columns = [
    { header: '订单ID', key: 'id', width: 10 },
    { header: '用户ID', key: 'userId', width: 10 },
    { header: '用户名', key: 'username', width: 15 },
    { header: '邮箱', key: 'email', width: 25 },
    { header: '部门', key: 'department', width: 15 },
    { header: '商品ID', key: 'productId', width: 10 },
    { header: '商品名称', key: 'productName', width: 25 },
    { header: '数量', key: 'quantity', width: 10 },
    { header: '支付方式', key: 'paymentMethod', width: 10 },
    { header: '价格', key: 'price', width: 10 },
    { header: '总金额', key: 'totalAmount', width: 15 },
    { header: '联系方式', key: 'contactInfo', width: 20 },
    { header: '位置', key: 'location', width: 10 },
    { header: '状态', key: 'status', width: 10 },
    { header: '创建时间', key: 'createdAt', width: 20 },
    { header: '更新时间', key: 'updatedAt', width: 20 },
    { header: '用户备注', key: 'remarks', width: 30 },
    { header: '管理员备注', key: 'adminRemarks', width: 30 },
    { header: '物流公司', key: 'trackingCompany', width: 15 },
    { header: '物流单号', key: 'trackingNumber', width: 20 }
  ];
  
  // 添加数据
  exchanges.forEach(exchange => {
    const user = exchange.User || {};
    const product = exchange.Product || {};
    
    // 计算总金额
    let price = 0;
    let totalAmount = 0;
    
    if (exchange.paymentMethod === 'ly') {
      price = product.lyPrice || 0;
      totalAmount = price * exchange.quantity;
    } else {
      price = product.rmbPrice || 0;
      totalAmount = price * exchange.quantity;
    }
    
    // 格式化状态
    let statusText = '';
    switch(exchange.status) {
      case 'pending': statusText = '待处理'; break;
      case 'approved': statusText = '已批准'; break;
      case 'shipped': statusText = '已发货'; break;
      case 'completed': statusText = '已完成'; break;
      case 'rejected': statusText = '已拒绝'; break;
      case 'cancelled': statusText = '已取消'; break;
      default: statusText = exchange.status;
    }
    
    worksheet.addRow({
      id: exchange.id,
      userId: exchange.userId,
      username: user.username || '',
      email: user.email || '',
      department: user.department || '',
      productId: exchange.productId,
      productName: product.name || '',
      quantity: exchange.quantity,
      paymentMethod: exchange.paymentMethod === 'ly' ? '光年币' : '人民币',
      price: price,
      totalAmount: totalAmount,
      contactInfo: exchange.contactInfo || '',
      location: exchange.location || '',
      status: statusText,
      createdAt: new Date(exchange.createdAt).toLocaleString(),
      updatedAt: new Date(exchange.updatedAt).toLocaleString(),
      remarks: exchange.remarks || '',
      adminRemarks: exchange.adminRemarks || '',
      trackingCompany: exchange.trackingCompany || '',
      trackingNumber: exchange.trackingNumber || ''
    });
  });
  
  // 设置响应头
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', `attachment; filename=orders_${Date.now()}.xlsx`);
  
  // 写入响应
  return workbook.xlsx.write(res);
}

/**
 * 导出为CSV格式
 */
async function exportToCsv(res, exchanges) {
  // 格式化数据
  const formattedData = exchanges.map(exchange => {
    const user = exchange.User || {};
    const product = exchange.Product || {};
    
    // 计算总金额
    let price = 0;
    let totalAmount = 0;
    
    if (exchange.paymentMethod === 'ly') {
      price = product.lyPrice || 0;
      totalAmount = price * exchange.quantity;
    } else {
      price = product.rmbPrice || 0;
      totalAmount = price * exchange.quantity;
    }
    
    // 格式化状态
    let statusText = '';
    switch(exchange.status) {
      case 'pending': statusText = '待处理'; break;
      case 'approved': statusText = '已批准'; break;
      case 'shipped': statusText = '已发货'; break;
      case 'completed': statusText = '已完成'; break;
      case 'rejected': statusText = '已拒绝'; break;
      case 'cancelled': statusText = '已取消'; break;
      default: statusText = exchange.status;
    }
    
    return {
      '订单ID': exchange.id,
      '用户ID': exchange.userId,
      '用户名': user.username || '',
      '邮箱': user.email || '',
      '部门': user.department || '',
      '商品ID': exchange.productId,
      '商品名称': product.name || '',
      '数量': exchange.quantity,
      '支付方式': exchange.paymentMethod === 'ly' ? '光年币' : '人民币',
      '价格': price,
      '总金额': totalAmount,
      '联系方式': exchange.contactInfo || '',
      '位置': exchange.location || '',
      '状态': statusText,
      '创建时间': new Date(exchange.createdAt).toLocaleString(),
      '更新时间': new Date(exchange.updatedAt).toLocaleString(),
      '用户备注': exchange.remarks || '',
      '管理员备注': exchange.adminRemarks || '',
      '物流公司': exchange.trackingCompany || '',
      '物流单号': exchange.trackingNumber || ''
    };
  });
  
  // 生成CSV
  const csvContent = parse(formattedData);
  
  // 设置响应头
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', `attachment; filename=orders_${Date.now()}.csv`);
  
  // 发送CSV内容
  res.send(csvContent);
}

/**
 * 解析CSV文件
 */
async function parseCSV(filePath) {
  return new Promise((resolve, reject) => {
    const data = [];
    
    console.log('=== CSV解析调试信息 ===');
    console.log('开始解析CSV文件:', filePath);
    
    fs.createReadStream(filePath)
      .pipe(csv.parse({ 
        headers: true, 
        trim: true,
        skipEmptyLines: true, // 跳过空行
        encoding: 'utf8' // 确保UTF-8编码
      }))
      .on('error', error => {
        console.error('CSV解析错误:', error);
        reject(error);
      })
      .on('headers', (headers) => {
        console.log('CSV表头:', headers);
      })
      .on('data', (row, index) => {
        console.log(`CSV第${index + 2}行数据:`, JSON.stringify(row, null, 2));
        data.push(row);
      })
      .on('end', () => {
        console.log(`=== CSV解析完成，有效数据行数: ${data.length} ===`);
        resolve(data);
      });
  });
}

/**
 * 解析Excel文件
 */
async function parseExcel(filePath) {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile(filePath);
  
  console.log('=== Excel解析调试信息 ===');
  console.log('工作表数量:', workbook.worksheets.length);
  workbook.worksheets.forEach((sheet, index) => {
    console.log(`工作表${index + 1}: ${sheet.name}, 行数: ${sheet.rowCount}`);
  });
  
  // 优先查找名为"导入模板"的工作表，否则使用第一个工作表
  let worksheet = workbook.getWorksheet('导入模板');
  if (!worksheet) {
    // 如果没有找到"导入模板"工作表，查找包含数据的工作表
    worksheet = workbook.worksheets.find(sheet => sheet.rowCount > 1);
    if (!worksheet) {
      worksheet = workbook.getWorksheet(1); // 默认使用第一个工作表
    }
  }
  
  console.log(`使用工作表: ${worksheet.name}, 行数: ${worksheet.rowCount}`);
  
  const data = [];
  
  // 获取表头
  const headers = [];
  const headerRow = worksheet.getRow(1);
  headerRow.eachCell((cell, colNumber) => {
    const headerValue = cell.value;
    headers[colNumber - 1] = headerValue;
    console.log(`表头列${colNumber}: "${headerValue}"`);
  });
  
  console.log('完整表头数组:', headers);
  
  // 遍历数据行
  for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
    const row = worksheet.getRow(rowNumber);
    
    // 检查行是否为空
    let hasData = false;
    const rowData = {};
    
    row.eachCell((cell, colNumber) => {
      const header = headers[colNumber - 1];
      const cellValue = cell.value;
      
      if (header && cellValue !== null && cellValue !== undefined && cellValue !== '') {
        hasData = true;
        rowData[header] = cellValue;
      }
    });
    
    // 只添加有数据的行
    if (hasData) {
      console.log(`第${rowNumber}行数据:`, JSON.stringify(rowData, null, 2));
      data.push(rowData);
    } else {
      console.log(`第${rowNumber}行为空，跳过`);
    }
  }
  
  console.log(`=== Excel解析完成，有效数据行数: ${data.length} ===`);
  return data;
}

/**
 * 处理导入数据
 */
async function processImportData(data, admin) {
  const result = {
    success: 0,
    errors: []
  };
  
  // 遍历每一行数据
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const rowIndex = i + 2; // 加2是因为第一行是表头，Excel是从1开始计数的
    
    try {
      // 验证必填字段
      if (!row['用户邮箱']) {
        result.errors.push({ row: rowIndex, message: '用户邮箱不能为空' });
        continue;
      }
      
      if (!row['商品名称']) {
        result.errors.push({ row: rowIndex, message: '商品名称不能为空' });
        continue;
      }
      
      // 查找用户
      const user = await User.findOne({ where: { email: row['用户邮箱'] } });
      if (!user) {
        result.errors.push({ row: rowIndex, message: `用户邮箱 ${row['用户邮箱']} 不存在` });
        continue;
      }
      
      // 查找商品
      const product = await Product.findOne({ where: { name: row['商品名称'] } });
      if (!product) {
        result.errors.push({ row: rowIndex, message: `商品 ${row['商品名称']} 不存在` });
        continue;
      }
      
      // 验证数量
      const quantity = parseInt(row['数量'], 10) || 1;
      if (isNaN(quantity) || quantity <= 0) {
        result.errors.push({ row: rowIndex, message: '数量必须为正整数' });
        continue;
      }
      
      // 验证库存
      if (product.stock < quantity) {
        result.errors.push({ row: rowIndex, message: `商品 ${row['商品名称']} 库存不足，当前库存: ${product.stock}` });
        continue;
      }
      
      // 验证支付方式
      const paymentMethod = row['支付方式']?.toLowerCase();
      if (!paymentMethod || !['ly', 'rmb'].includes(paymentMethod)) {
        result.errors.push({ row: rowIndex, message: '支付方式必须为 ly 或 rmb' });
        continue;
      }
      
      // 验证位置
      const validLocations = ['北京', '武汉', '长沙', '西安'];
      if (!row['位置'] || !validLocations.includes(row['位置'])) {
        result.errors.push({ row: rowIndex, message: `位置必须为以下之一: ${validLocations.join(', ')}` });
        continue;
      }
      
      // 验证状态
      const status = row['订单状态']?.toLowerCase() || 'pending';
      const validStatuses = ['pending', 'approved', 'shipped', 'completed', 'rejected', 'cancelled'];
      if (!validStatuses.includes(status)) {
        result.errors.push({ row: rowIndex, message: `状态必须为以下之一: ${validStatuses.join(', ')}` });
        continue;
      }
      
      // 创建兑换记录
      await Exchange.create({
        userId: user.id,
        productId: product.id,
        quantity,
        paymentMethod,
        contactInfo: row['联系方式'] || '未提供',
        location: row['位置'],
        remarks: row['备注'] || null,
        adminRemarks: row['管理员备注'] || null,
        status,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // 更新商品库存和兑换数量
      await product.update({
        stock: product.stock - quantity,
        exchangeCount: (product.exchangeCount || 0) + quantity
      });
      
      // 检查是否需要发送库存告警
      await checkProductStock(product);
      
      // 创建通知
      await createNotification({
        type: 'exchange',
        sourceId: product.id,
        title: '新订单通知',
        content: `您的订单 ${row['商品名称']} 已通过导入创建，当前状态为${status === 'pending' ? '待处理' : status}`,
        recipientId: user.id
      });
      
      result.success++;
    } catch (error) {
      console.error(`处理第 ${rowIndex} 行数据时出错:`, error);
      result.errors.push({ row: rowIndex, message: `处理数据时出错: ${error.message}` });
    }
  }
  
  return result;
}

/**
 * 批量删除兑换记录
 */
exports.batchDeleteExchanges = async (req, res) => {
  try {
    // 检查是否为管理员
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: '无权访问管理员功能' });
    }

    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: '请提供有效的订单ID列表' });
    }
    
    // 首先查找所有需要删除的记录
    const exchanges = await Exchange.findAll({
      where: { id: { [Op.in]: ids } },
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'stock', 'exchangeCount'],
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username'],
          required: false
        }
      ]
    });
    
    if (exchanges.length === 0) {
      return res.status(404).json({ message: '未找到任何匹配的订单记录' });
    }
    
    // 获取交易信息用于日志记录
    const exchangeDetails = exchanges.map(exchange => ({
      id: exchange.id,
      userId: exchange.userId,
      productId: exchange.productId,
      productName: exchange.Product?.name || '未知商品',
      username: exchange.User?.username || '未知用户',
      quantity: exchange.quantity,
      status: exchange.status
    }));
    
    // 开启事务处理
    const result = await sequelize.transaction(async (t) => {
      // 处理已批准或已发货但尚未完成的订单，需要恢复库存
      const restoreStockPromises = exchanges
        .filter(exchange => ['pending', 'approved', 'shipped'].includes(exchange.status))
        .map(async exchange => {
          if (exchange.Product) {
            const product = exchange.Product;
            
            // 恢复库存
            await product.update({
              stock: product.stock + exchange.quantity,
              exchangeCount: Math.max(0, product.exchangeCount - exchange.quantity)
            }, { transaction: t });
            
            // 记录库存变更日志
            await createLog({
              action: 'stock_update',
              entityType: 'product',
              entityId: product.id,
              oldValue: JSON.stringify({ stock: product.stock - exchange.quantity }),
              newValue: JSON.stringify({ stock: product.stock }),
              userId: req.user.id,
              username: req.user.username,
              description: `由于管理员删除兑换订单(ID: ${exchange.id})，商品"${product.name}"库存恢复${exchange.quantity}个`
            }, req, t);
          }
        });
      
      // 等待所有库存更新完成
      await Promise.all(restoreStockPromises);
      
      // 删除兑换记录
      const deleted = await Exchange.destroy({
        where: { id: { [Op.in]: ids } },
        transaction: t
      });
      
      // 记录批量删除日志
      await createLog({
        action: 'exchange_batch_delete',
        entityType: 'exchange',
        entityId: null,
        oldValue: JSON.stringify(exchangeDetails),
        newValue: null,
        userId: req.user.id,
        username: req.user.username,
        description: `管理员"${req.user.username}"批量删除了${deleted}个兑换订单`
      }, req, t);
      
      return deleted;
    });
    
    // 检查是否删除了所有订单，如果是则重置自增ID
    // 这里不放在事务中，因为事务可能会阻止 AUTO_INCREMENT 的重置
    const remainingCount = await Exchange.count();
    if (remainingCount === 0) {
      try {
        // 1. 重置自增ID
        await sequelize.query('ALTER TABLE exchanges AUTO_INCREMENT = 1');
        console.log('已删除所有订单，exchanges表自增ID已重置为1');
        
        // 2. 清理数据库会话状态
        await sequelize.query('FLUSH TABLES');
        console.log('已刷新数据库表状态');
        
        // 3. 优化表结构
        await sequelize.query('OPTIMIZE TABLE exchanges');
        console.log('已优化exchanges表结构');
        
        // 4. 记录重置操作日志
        await createLog({
          action: 'AUTO_INCREMENT_RESET',
          entityType: 'EXCHANGE',
          entityId: null,
          oldValue: null,
          newValue: JSON.stringify({ auto_increment: 1 }),
          userId: req.user.id,
          username: req.user.username,
          description: `由于管理员"${req.user.username}"删除了所有订单，系统自动重置了订单表的自增ID为1`
        }, req);
      } catch (resetError) {
        console.error('重置exchanges表自增ID失败:', resetError);
        // 不中断主流程，只记录错误
      }
    }
    
    return res.status(200).json({
      message: `成功删除${result}个订单记录`,
      deleted: result,
      total: ids.length,
      autoIncrementReset: remainingCount === 0
    });
  } catch (error) {
    console.error('批量删除兑换记录失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 检查数据库中是否已经删除了所有订单，如果是则重置自增ID
 * 这个函数会在批量删除订单后被调用
 */
exports.checkAndResetAutoIncrement = async (req, res) => {
  try {
    // 检查是否为管理员
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: '无权访问管理员功能' });
    }
    
    // 查询数据库中的订单总数
    const count = await Exchange.count();
    
    // 如果订单总数为0，重置自增ID
    if (count === 0) {
      // 1. 重置自增ID
      await sequelize.query('ALTER TABLE exchanges AUTO_INCREMENT = 1');
      console.log('已重置exchanges表的自增ID为1');
      
      // 2. 清理数据库会话状态
      await sequelize.query('FLUSH TABLES');
      console.log('已刷新数据库表状态');
      
      // 3. 优化表结构
      await sequelize.query('OPTIMIZE TABLE exchanges');
      console.log('已优化exchanges表结构');
      
      // 4. 记录重置操作日志
      await createLog({
        action: 'AUTO_INCREMENT_RESET',
        entityType: 'EXCHANGE',
        entityId: null,
        oldValue: null,
        newValue: JSON.stringify({ auto_increment: 1 }),
        userId: req.user.id,
        username: req.user.username,
        description: `管理员"${req.user.username}"重置了订单表的自增ID为1`
      }, req);
      
      return res.status(200).json({
        success: true,
        message: '已重置订单表的自增ID为1',
        resetPerformed: true
      });
    }
    
    return res.status(200).json({
      success: true,
      message: '数据库中仍有订单记录，未执行重置操作',
      remainingCount: count,
      resetPerformed: false
    });
  } catch (error) {
    console.error('检查和重置自增ID失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试',
      error: error.message
    });
  }
};

/**
 * 获取库存变化趋势数据
 */
exports.getStockTrend = async (req, res) => {
  try {
    console.log('[StockTrend] 调用库存趋势数据API');
    
    // 检查是否为管理员
    if (!req.user || req.user.role !== 'admin') {
      console.log('[StockTrend] 非管理员尝试访问库存趋势数据');
      return res.status(403).json({ 
        message: '无权访问管理员功能',
        authenticated: !!req.user,
        userRole: req.user ? req.user.role : null
      });
    }
    
    // 获取请求参数
    const period = req.query.period || 'month'; // 默认按月
    const categoryId = req.query.category; // 可选的分类筛选
    
    // 获取日期范围
    let startDate, endDate;
    const now = new Date();
    endDate = now;
    
    // 根据时间段设置起始日期
    switch(period) {
      case 'week':
        // 最近7天
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        // 最近30天
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 30);
        break;
      case 'quarter':
        // 最近90天
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 90);
        break;
      default:
        // 默认30天
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 30);
    }
    
    console.log(`[StockTrend] 查询库存趋势，时间段:${period}，起始日期:${startDate.toISOString()}, 结束日期:${endDate.toISOString()}`);
    
    // 定义时间格式化函数
    const formatDate = (date, periodType) => {
      const d = new Date(date);
      if (periodType === 'week' || periodType === 'month' || periodType === 'quarter') {
        // 格式为 MM-DD
        return `${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
      }
      return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}`;
    };
    
    // 生成时间区间数组
    const timeSlots = [];
    let dayCount;
    
    switch(period) {
      case 'week':
        dayCount = 7;
        break;
      case 'month':
        dayCount = 30;
        break;
      case 'quarter':
        dayCount = 90;
        break;
      default:
        dayCount = 30;
    }
    
    // 按天分组
    for (let i = 0; i < dayCount; i++) {
      const d = new Date(endDate);
      d.setDate(d.getDate() - (dayCount - 1 - i));
      timeSlots.push({
        date: formatDate(d, period),
        timestamp: d
      });
    }
    
    // 获取当前总库存（作为基准）
    let currentStockQuery = {
      attributes: [
        [sequelize.fn('SUM', sequelize.col('stock')), 'totalStock']
      ],
      raw: true
    };
    
    // 如果指定了分类，添加分类筛选
    if (categoryId && categoryId !== 'all') {
      currentStockQuery.where = { categoryId: parseInt(categoryId) };
    }
    
    const currentStockResult = await Product.findOne(currentStockQuery);
    const currentTotalStock = currentStockResult && currentStockResult.totalStock 
      ? parseInt(currentStockResult.totalStock) 
      : 0;
    
    console.log(`[StockTrend] 当前总库存: ${currentTotalStock}`);
    
    // 构建日志查询条件
    const { Log } = require('../models');
    let logWhereClause = {
      action: 'stock_update',
      entityType: 'product',
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    };
    
    // 获取库存变更日志
    const stockLogs = await Log.findAll({
      where: logWhereClause,
      attributes: ['id', 'entityId', 'oldValue', 'newValue', 'createdAt', 'description'],
      order: [['createdAt', 'ASC']]
    });
    
    console.log(`[StockTrend] 查询到 ${stockLogs.length} 条库存变更日志`);
    
    // 如果有分类筛选，需要过滤日志
    let filteredLogs = stockLogs;
    if (categoryId && categoryId !== 'all') {
      // 获取该分类下的所有商品ID
      const categoryProducts = await Product.findAll({
        where: { categoryId: parseInt(categoryId) },
        attributes: ['id']
      });
      const productIds = categoryProducts.map(p => p.id);
      
      // 过滤日志，只保留该分类商品的库存变更
      filteredLogs = stockLogs.filter(log => productIds.includes(log.entityId));
      console.log(`[StockTrend] 分类筛选后剩余 ${filteredLogs.length} 条日志`);
    }
    
    // 初始化数据数组
    const totalStockData = [];
    const stockIncreaseData = [];
    const stockDecreaseData = [];
    const netChangeData = [];
    
    // 计算每天的库存变化
    let runningStock = currentTotalStock;
    
    // 从最新的日期开始，向前推算历史库存
    for (let i = timeSlots.length - 1; i >= 0; i--) {
      const slot = timeSlots[i];
      const slotStart = new Date(slot.timestamp);
      slotStart.setHours(0, 0, 0, 0);
      const slotEnd = new Date(slot.timestamp);
      slotEnd.setHours(23, 59, 59, 999);
      
      // 获取该时间段的库存变更日志
      const dayLogs = filteredLogs.filter(log => {
        const logDate = new Date(log.createdAt);
        return logDate >= slotStart && logDate <= slotEnd;
      });
      
      let dayIncrease = 0;
      let dayDecrease = 0;
      let dayNetChange = 0;
      
      // 分析每条日志的库存变化
      dayLogs.forEach(log => {
        try {
          const oldValue = JSON.parse(log.oldValue || '{}');
          const newValue = JSON.parse(log.newValue || '{}');
          
          const oldStock = parseInt(oldValue.stock || 0);
          const newStock = parseInt(newValue.stock || 0);
          const change = newStock - oldStock;
          
          if (change > 0) {
            dayIncrease += change;
          } else if (change < 0) {
            dayDecrease += Math.abs(change);
          }
          
          dayNetChange += change;
        } catch (error) {
          console.error(`[StockTrend] 解析日志失败:`, log.id, error);
        }
      });
      
      // 如果是最后一天（今天），使用当前库存
      if (i === timeSlots.length - 1) {
        totalStockData.unshift(runningStock);
      } else {
        // 向前推算：当前库存 - 后续所有变化
        runningStock -= dayNetChange;
        totalStockData.unshift(runningStock);
      }
      
      stockIncreaseData.unshift(dayIncrease);
      stockDecreaseData.unshift(dayDecrease);
      netChangeData.unshift(dayNetChange);
    }
    
    // 返回结果
    const result = {
      timeLabels: timeSlots.map(slot => slot.date),
      totalStockData,
      stockIncreaseData,
      stockDecreaseData,
      netChangeData,
      period,
      currentTotalStock,
      categoryId: categoryId || 'all'
    };
    
    console.log('[StockTrend] 库存趋势数据处理完成');
    return res.json(result);
  } catch (error) {
    console.error('[StockTrend] 获取库存趋势数据失败:', error);
    return res.status(500).json({ 
      message: '服务器错误，请稍后重试', 
      error: error.message 
    });
  }
};

// 辅助函数：获取状态文本
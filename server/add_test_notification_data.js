const { NotificationLog } = require('./models');

async function addTestData() {
  try {
    console.log('=== 添加测试通知数据 ===');
    
    // 添加一些测试数据
    const testData = [
      {
        notificationType: 'exchange_notification',
        status: 'success',
        webhookUrl: 'https://open.feishu.cn/open-apis/bot/v2/hook/test',
        requestPayload: { msg_type: 'text', content: { text: '测试消息1' } },
        responseStatus: 200,
        responseBody: { code: 0, msg: 'success' },
        responseTime: 150,
        retryCount: 0,
        maxRetries: 3,
        triggerSource: 'auto',
        sentAt: new Date()
      },
      {
        notificationType: 'stock_alert',
        status: 'failed',
        webhookUrl: 'https://open.feishu.cn/open-apis/bot/v2/hook/test',
        requestPayload: { msg_type: 'text', content: { text: '库存告警' } },
        responseStatus: 500,
        errorMessage: '服务器内部错误',
        responseTime: 5000,
        retryCount: 1,
        maxRetries: 3,
        triggerSource: 'auto',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1天前
      },
      {
        notificationType: 'feedback_notification',
        status: 'pending',
        webhookUrl: 'https://open.feishu.cn/open-apis/bot/v2/hook/test',
        requestPayload: { msg_type: 'text', content: { text: '反馈通知' } },
        retryCount: 0,
        maxRetries: 3,
        triggerSource: 'manual',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
      }
    ];

    for (const data of testData) {
      await NotificationLog.create(data);
    }

    console.log('✅ 测试数据添加成功');
    
    // 验证数据
    const count = await NotificationLog.count();
    console.log(`数据库中现有 ${count} 条通知记录`);
    
  } catch (error) {
    console.error('❌ 添加测试数据失败:', error);
  } finally {
    process.exit(0);
  }
}

addTestData();

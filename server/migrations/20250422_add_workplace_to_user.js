const { Sequelize } = require('sequelize');
const { sequelize } = require('../config/database');

module.exports = {
  up: async () => {
    await sequelize.getQueryInterface().addColumn('users', 'workplace', {
      type: Sequelize.STRING,
      allowNull: true,
      after: 'department'
    });
  },

  down: async () => {
    await sequelize.getQueryInterface().removeColumn('users', 'workplace');
  }
}; 
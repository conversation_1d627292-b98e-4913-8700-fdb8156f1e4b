'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 删除username的唯一约束
      await queryInterface.removeConstraint('users', 'users_username_key');
      console.log('成功移除username唯一约束');

      return Promise.resolve();
    } catch (error) {
      console.error('移除唯一约束失败:', error);
      return Promise.reject(error);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 恢复username的唯一约束
      await queryInterface.addConstraint('users', {
        fields: ['username'],
        type: 'unique',
        name: 'users_username_key'
      });
      console.log('成功恢复username唯一约束');

      return Promise.resolve();
    } catch (error) {
      console.error('恢复唯一约束失败:', error);
      return Promise.reject(error);
    }
  }
}; 
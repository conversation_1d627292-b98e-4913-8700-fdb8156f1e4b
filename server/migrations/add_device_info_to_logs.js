const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'feishu_mall'
};

// 迁移函数
async function migrate() {
  console.log('开始迁移: 向日志表添加设备信息字段');

  let connection;
  try {
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 检查字段是否已存在
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'logs' AND COLUMN_NAME = 'deviceInfo'
    `, [dbConfig.database]);

    if (columns.length > 0) {
      console.log('deviceInfo 字段已存在，跳过迁移');
      return;
    }

    // 添加 deviceInfo 字段
    await connection.execute(`
      ALTER TABLE logs
      ADD COLUMN deviceInfo TEXT NULL COMMENT '设备信息，如浏览器、操作系统等'
      AFTER ipAddress
    `);

    console.log('deviceInfo 字段添加成功');

    // 添加索引
    console.log('正在创建索引...');

    // 检查索引是否存在
    const [indexAction] = await connection.execute(`
      SHOW INDEX FROM logs WHERE Key_name = 'idx_logs_action'
    `);

    if (indexAction.length === 0) {
      await connection.execute(`
        CREATE INDEX idx_logs_action ON logs (action)
      `);
      console.log('idx_logs_action 索引创建成功');
    }

    const [indexEntityType] = await connection.execute(`
      SHOW INDEX FROM logs WHERE Key_name = 'idx_logs_entityType_entityId'
    `);

    if (indexEntityType.length === 0) {
      await connection.execute(`
        CREATE INDEX idx_logs_entityType_entityId ON logs (entityType, entityId)
      `);
      console.log('idx_logs_entityType_entityId 索引创建成功');
    }

    const [indexUserId] = await connection.execute(`
      SHOW INDEX FROM logs WHERE Key_name = 'idx_logs_userId'
    `);

    if (indexUserId.length === 0) {
      await connection.execute(`
        CREATE INDEX idx_logs_userId ON logs (userId)
      `);
      console.log('idx_logs_userId 索引创建成功');
    }

    const [indexCreatedAt] = await connection.execute(`
      SHOW INDEX FROM logs WHERE Key_name = 'idx_logs_createdAt'
    `);

    if (indexCreatedAt.length === 0) {
      await connection.execute(`
        CREATE INDEX idx_logs_createdAt ON logs (createdAt)
      `);
      console.log('idx_logs_createdAt 索引创建成功');
    }

    console.log('迁移完成：日志表结构更新成功');
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行迁移
migrate();
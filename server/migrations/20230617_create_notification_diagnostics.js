'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notification_diagnostics', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      diagnostic_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '诊断类型'
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        comment: '状态'
      },
      details: {
        type: Sequelize.JSON,
        comment: '详细信息'
      },
      error_message: {
        type: Sequelize.TEXT,
        comment: '错误信息'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false
      },
      resolved_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '解决时间'
      },
      resolution: {
        type: Sequelize.TEXT,
        comment: '解决方案'
      }
    }, {
      charset: 'utf8mb4',
      collate: 'utf8mb4_general_ci',
      comment: '通知诊断信息表',
      indexes: [
        {
          name: 'idx_notification_diag_type_status',
          fields: ['diagnostic_type', 'status']
        },
        {
          name: 'idx_notification_diag_created',
          fields: ['created_at']
        }
      ]
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notification_diagnostics');
  }
}; 
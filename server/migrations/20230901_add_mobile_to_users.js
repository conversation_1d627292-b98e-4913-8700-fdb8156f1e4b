const { Sequelize } = require('sequelize');
const { sequelize } = require('../config/database');

module.exports = {
  up: async () => {
    try {
      // 检查列是否已经存在
      const [results] = await sequelize.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'mobile'
      `);
      
      // 如果列不存在，则添加
      if (results.length === 0) {
        console.log('添加mobile字段到users表...');
        await sequelize.getQueryInterface().addColumn('users', 'mobile', {
          type: Sequelize.STRING,
          allowNull: true,
          defaultValue: null,
          comment: '用户手机号码'
        });
        console.log('mobile字段添加成功!');
      } else {
        console.log('mobile字段已存在，无需添加');
      }
    } catch (error) {
      console.error('添加mobile字段出错:', error);
      throw error;
    }
  },

  down: async () => {
    try {
      // 检查列是否存在
      const [results] = await sequelize.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'mobile'
      `);
      
      // 如果列存在，则删除
      if (results.length > 0) {
        console.log('从users表中删除mobile字段...');
        await sequelize.getQueryInterface().removeColumn('users', 'mobile');
        console.log('mobile字段删除成功!');
      } else {
        console.log('mobile字段不存在，无需删除');
      }
    } catch (error) {
      console.error('删除mobile字段出错:', error);
      throw error;
    }
  }
}; 
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('exchanges', 'paymentProofUrl', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: '支付凭证图片URL'
    });
    
    console.log('已添加paymentProofUrl字段到exchanges表');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('exchanges', 'paymentProofUrl');
    
    console.log('已从exchanges表移除paymentProofUrl字段');
  }
}; 
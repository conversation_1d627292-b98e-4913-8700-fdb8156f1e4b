'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. 更新type字段的枚举值
    await queryInterface.changeColumn('Feedbacks', 'type', {
      type: Sequelize.ENUM('product', 'feature', 'bug', 'other'),
      defaultValue: 'other'
    });

    // 2. 更新status字段的枚举值
    await queryInterface.changeColumn('Feedbacks', 'status', {
      type: Sequelize.ENUM('pending', 'processing', 'completed'),
      defaultValue: 'pending'
    });

    // 3. 添加adminReply字段
    await queryInterface.addColumn('Feedbacks', 'adminReply', {
      type: Sequelize.TEXT,
      allowNull: true
    });

    // 4. 删除不再使用的字段
    await queryInterface.removeColumn('Feedbacks', 'response');
    await queryInterface.removeColumn('Feedbacks', 'respondedBy');
    await queryInterface.removeColumn('Feedbacks', 'respondedAt');
  },

  async down(queryInterface, Sequelize) {
    // 1. 还原adminReply字段
    await queryInterface.removeColumn('Feedbacks', 'adminReply');

    // 2. 还原之前删除的字段
    await queryInterface.addColumn('Feedbacks', 'response', {
      type: Sequelize.TEXT,
      allowNull: true
    });
    await queryInterface.addColumn('Feedbacks', 'respondedBy', {
      type: Sequelize.INTEGER,
      allowNull: true
    });
    await queryInterface.addColumn('Feedbacks', 'respondedAt', {
      type: Sequelize.DATE,
      allowNull: true
    });

    // 3. 还原status字段的枚举值
    await queryInterface.changeColumn('Feedbacks', 'status', {
      type: Sequelize.ENUM('pending', 'processing', 'resolved', 'rejected'),
      defaultValue: 'pending'
    });

    // 4. 还原type字段的枚举值
    await queryInterface.changeColumn('Feedbacks', 'type', {
      type: Sequelize.ENUM('general', 'bug', 'feature', 'question'),
      defaultValue: 'general'
    });
  }
}; 
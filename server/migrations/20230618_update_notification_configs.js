'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加新字段（容错处理）
    const addColumnIfNotExists = async (tableName, columnName, columnDefinition) => {
      try {
        // 检查列是否存在
        const columns = await queryInterface.sequelize.query(
          `SHOW COLUMNS FROM ${tableName} LIKE '${columnName}'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );
        
        if (columns.length === 0) {
          console.log(`添加字段 ${columnName} 到表 ${tableName}`);
          await queryInterface.addColumn(tableName, columnName, columnDefinition);
        } else {
          console.log(`字段 ${columnName} 已存在于表 ${tableName}，跳过添加`);
        }
      } catch (error) {
        console.error(`添加字段 ${columnName} 失败:`, error.message);
      }
    };
    
    // 尝试添加新字段
    await addColumnIfNotExists('notification_configs', 'template_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: '关联的模板ID',
      after: 'retry_count'
    });

    await addColumnIfNotExists('notification_configs', 'schedule_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: '关联的调度ID',
      after: 'template_id'
    });

    await addColumnIfNotExists('notification_configs', 'advanced_settings', {
      type: Sequelize.JSON,
      allowNull: true,
      comment: '高级设置',
      after: 'schedule_id'
    });

    // 添加外键约束（容错处理）
    const addConstraintIfNotExists = async (tableName, constraintName, constraintDefinition) => {
      try {
        // 检查约束是否存在
        const constraints = await queryInterface.sequelize.query(
          `SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
           WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '${tableName}' AND CONSTRAINT_NAME = '${constraintName}'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );
        
        if (constraints.length === 0) {
          console.log(`添加约束 ${constraintName} 到表 ${tableName}`);
          await queryInterface.addConstraint(tableName, {
            ...constraintDefinition,
            name: constraintName
          });
        } else {
          console.log(`约束 ${constraintName} 已存在于表 ${tableName}，跳过添加`);
        }
      } catch (error) {
        console.error(`添加约束 ${constraintName} 失败:`, error.message);
      }
    };

    // 尝试添加外键约束
    await addConstraintIfNotExists('notification_configs', 'fk_notification_config_template', {
      fields: ['template_id'],
      type: 'foreign key',
      references: {
        table: 'message_templates',
        field: 'id'
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE'
    });

    await addConstraintIfNotExists('notification_configs', 'fk_notification_config_schedule', {
      fields: ['schedule_id'],
      type: 'foreign key',
      references: {
        table: 'sending_schedules',
        field: 'id'
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE'
    });

    // 更新现有的配置数据
    try {
      const templates = await queryInterface.sequelize.query(
        'SELECT id, template_code, notification_type FROM message_templates',
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      const schedules = await queryInterface.sequelize.query(
        'SELECT id, notification_type FROM sending_schedules',
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      // 获取所有配置
      const configs = await queryInterface.sequelize.query(
        'SELECT id, notification_type FROM notification_configs',
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      // 更新配置
      for (const config of configs) {
        const matchedTemplate = templates.find(t => t.notification_type === config.notification_type);
        const matchedSchedule = schedules.find(s => s.notification_type === config.notification_type);
        
        if (matchedTemplate || matchedSchedule) {
          await queryInterface.sequelize.query(
            `UPDATE notification_configs 
             SET template_id = ${matchedTemplate ? matchedTemplate.id : null},
                 schedule_id = ${matchedSchedule ? matchedSchedule.id : null},
                 advanced_settings = '{"enableSmartScheduling": true, "enableRetry": true, "maxRetryAttempts": 3}'
             WHERE id = ${config.id}`
          );
        }
      }
    } catch (error) {
      console.error('更新配置数据失败:', error.message);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 容错移除外键约束
    const removeConstraintIfExists = async (tableName, constraintName) => {
      try {
        const constraints = await queryInterface.sequelize.query(
          `SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
           WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '${tableName}' AND CONSTRAINT_NAME = '${constraintName}'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );
        
        if (constraints.length > 0) {
          await queryInterface.removeConstraint(tableName, constraintName);
        } else {
          console.log(`约束 ${constraintName} 不存在，跳过移除`);
        }
      } catch (error) {
        console.error(`移除约束 ${constraintName} 失败:`, error.message);
      }
    };
    
    // 尝试移除外键约束
    await removeConstraintIfExists('notification_configs', 'fk_notification_config_template');
    await removeConstraintIfExists('notification_configs', 'fk_notification_config_schedule');
    
    // 容错移除字段
    const removeColumnIfExists = async (tableName, columnName) => {
      try {
        const columns = await queryInterface.sequelize.query(
          `SHOW COLUMNS FROM ${tableName} LIKE '${columnName}'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );
        
        if (columns.length > 0) {
          await queryInterface.removeColumn(tableName, columnName);
        } else {
          console.log(`字段 ${columnName} 不存在，跳过移除`);
        }
      } catch (error) {
        console.error(`移除字段 ${columnName} 失败:`, error.message);
      }
    };
    
    // 尝试移除字段
    await removeColumnIfExists('notification_configs', 'template_id');
    await removeColumnIfExists('notification_configs', 'schedule_id');
    await removeColumnIfExists('notification_configs', 'advanced_settings');
  }
}; 
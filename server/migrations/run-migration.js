// 手动执行的迁移脚本 - 添加contentHtml字段到announcements表
const { sequelize } = require('../config/database');

async function runMigration() {
  console.log('开始执行迁移...');
  
  try {
    // 检查表是否存在
    const [tables] = await sequelize.query(`
      SHOW TABLES LIKE 'announcements'
    `);
    
    if (tables.length === 0) {
      console.log('announcements表不存在，请先创建表');
      return;
    }
    
    // 检查字段是否已存在
    const [columns] = await sequelize.query(`
      SHOW COLUMNS FROM announcements LIKE 'contentHtml'
    `);
    
    if (columns.length > 0) {
      console.log('contentHtml字段已存在，无需迁移');
      return;
    }
    
    // 执行ALTER TABLE语句，添加contentHtml字段
    await sequelize.query(`
      ALTER TABLE announcements 
      ADD COLUMN contentHtml TEXT AFTER content
    `);
    
    console.log('成功添加contentHtml字段到announcements表');
    
    // 更新现有数据，将content字段值复制到contentHtml字段
    await sequelize.query(`
      UPDATE announcements 
      SET contentHtml = content 
      WHERE contentHtml IS NULL
    `);
    
    console.log('成功更新现有数据，迁移完成');
    
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    // 关闭连接
    await sequelize.close();
  }
}

// 运行迁移
runMigration()
  .then(() => {
    console.log('迁移脚本执行完毕');
    process.exit(0);
  })
  .catch(err => {
    console.error('迁移脚本执行出错:', err);
    process.exit(1);
  }); 
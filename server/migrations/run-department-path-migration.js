// 手动执行的迁移脚本 - 添加departmentPath字段到users表
const { sequelize } = require('../config/database');

async function runMigration() {
  console.log('开始执行迁移...');
  
  try {
    // 检查表是否存在
    const [tables] = await sequelize.query(`
      SHOW TABLES LIKE 'users'
    `);
    
    if (tables.length === 0) {
      console.log('users表不存在，请先创建表');
      return;
    }
    
    // 检查字段是否已存在
    const [columns] = await sequelize.query(`
      SHOW COLUMNS FROM users LIKE 'departmentPath'
    `);
    
    if (columns.length > 0) {
      console.log('departmentPath字段已存在，无需迁移');
      return;
    }
    
    // 执行ALTER TABLE语句，添加departmentPath字段
    await sequelize.query(`
      ALTER TABLE users 
      ADD COLUMN departmentPath VARCHAR(255) DEFAULT NULL COMMENT '用户完整部门路径，如"公司/技术部/后端组"'
    `);
    
    console.log('成功添加departmentPath字段到users表');
    
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    // 关闭连接
    await sequelize.close();
  }
}

// 运行迁移
runMigration()
  .then(() => {
    console.log('迁移脚本执行完毕');
    process.exit(0);
  })
  .catch(err => {
    console.error('迁移脚本执行出错:', err);
    process.exit(1);
  }); 
const { Sequelize } = require('sequelize');
const { sequelize } = require('../config/database');

module.exports = {
  up: async () => {
    try {
      // 检查表是否已经存在
      const [tables] = await sequelize.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() AND table_name = 'workplaces'
      `);
      
      if (tables.length === 0) {
        console.log('创建workplaces表...');
        
        await sequelize.getQueryInterface().createTable('workplaces', {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: true
          },
          code: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: true
          },
          description: {
            type: Sequelize.STRING,
            allowNull: true
          },
          isActive: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: true
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false
          }
        });

        console.log('workplaces表创建成功');
        
        // 检查表是否为空
        const [count] = await sequelize.query('SELECT COUNT(*) as count FROM workplaces');
        if (count[0].count === 0) {
          console.log('添加初始职场数据...');
          
          // 添加初始数据
          await sequelize.getQueryInterface().bulkInsert('workplaces', [
            {
              name: '北京',
              code: 'BJ',
              description: '北京职场',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              name: '武汉',
              code: 'WH',
              description: '武汉职场',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              name: '长沙',
              code: 'CS',
              description: '长沙职场',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              name: '西安',
              code: 'XA',
              description: '西安职场',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              name: '深圳',
              code: 'SZ',
              description: '深圳职场',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ]);
          
          console.log('初始职场数据添加成功');
        } else {
          console.log('workplaces表已有数据，跳过初始数据添加');
        }
      } else {
        console.log('workplaces表已存在，跳过创建');
      }
    } catch (error) {
      console.error('创建职场表出错:', error);
      throw error;
    }
  },

  down: async () => {
    try {
      // 检查表是否存在
      const [tables] = await sequelize.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() AND table_name = 'workplaces'
      `);
      
      if (tables.length > 0) {
        console.log('删除workplaces表...');
        await sequelize.getQueryInterface().dropTable('workplaces');
        console.log('workplaces表删除成功');
      } else {
        console.log('workplaces表不存在，无需删除');
      }
    } catch (error) {
      console.error('删除职场表出错:', error);
      throw error;
    }
  }
}; 
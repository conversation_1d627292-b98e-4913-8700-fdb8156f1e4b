'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('logs', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      action: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '操作类型，如：stock_update, exchange_create, exchange_status_update等'
      },
      entityType: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '实体类型，如：product, exchange等'
      },
      entityId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '相关实体ID'
      },
      oldValue: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '操作前的值（JSON格式）'
      },
      newValue: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '操作后的值（JSON格式）'
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '操作用户ID，如果有'
      },
      username: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '操作姓名，方便查询不需要关联'
      },
      ipAddress: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '操作者IP地址'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '操作描述'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // 添加索引，优化查询性能
    await queryInterface.addIndex('logs', ['action']);
    await queryInterface.addIndex('logs', ['entityType']);
    await queryInterface.addIndex('logs', ['entityId']);
    await queryInterface.addIndex('logs', ['userId']);
    await queryInterface.addIndex('logs', ['username']);
    await queryInterface.addIndex('logs', ['createdAt']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('logs');
  }
}; 
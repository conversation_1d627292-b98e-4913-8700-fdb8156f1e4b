'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. 移除积分字段
    await queryInterface.removeColumn('users', 'points');
    
    // 2. 添加部门字段
    await queryInterface.addColumn('users', 'department', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: null
    });
  },

  async down(queryInterface, Sequelize) {
    // 1. 恢复部门字段
    await queryInterface.removeColumn('users', 'department');
    
    // 2. 恢复积分字段
    await queryInterface.addColumn('users', 'points', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0
    });
  }
}; 
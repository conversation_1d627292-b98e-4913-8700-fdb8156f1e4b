'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加光年币字段
    await queryInterface.addColumn('users', 'points', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '用户光年币数量'
    });

    // 添加最后登录时间字段
    await queryInterface.addColumn('users', 'lastLoginAt', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: '最后登录时间'
    });

    // 添加最后登录IP字段
    await queryInterface.addColumn('users', 'lastLoginIp', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: '最后登录IP地址'
    });
  },

  async down(queryInterface, Sequelize) {
    // 删除字段
    await queryInterface.removeColumn('users', 'points');
    await queryInterface.removeColumn('users', 'lastLoginAt');
    await queryInterface.removeColumn('users', 'lastLoginIp');
  }
}; 
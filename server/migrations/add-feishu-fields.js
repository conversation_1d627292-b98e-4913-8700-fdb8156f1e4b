'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加飞书相关字段到users表
    return queryInterface.sequelize.transaction(async (transaction) => {
      try {
        // 1. 先检查表中是否已经有authType字段，如果没有则添加
        await queryInterface.addColumn(
          'users',
          'authType',
          {
            type: Sequelize.ENUM('password', 'feishu'),
            allowNull: false,
            defaultValue: 'password',
            comment: '认证类型：密码或飞书'
          },
          { transaction }
        ).catch(error => {
          // 如果字段已存在，忽略错误
          console.log('authType字段可能已存在:', error.message);
        });

        // 2. 添加飞书OpenID字段
        await queryInterface.addColumn(
          'users',
          'feishuOpenId',
          {
            type: Sequelize.STRING,
            allowNull: true,
            comment: '飞书用户的OpenID'
          },
          { transaction }
        ).catch(error => {
          console.log('feishuOpenId字段可能已存在:', error.message);
        });

        // 3. 添加飞书UnionID字段
        await queryInterface.addColumn(
          'users',
          'feishuUnionId',
          {
            type: Sequelize.STRING,
            allowNull: true,
            comment: '飞书用户的UnionID'
          },
          { transaction }
        ).catch(error => {
          console.log('feishuUnionId字段可能已存在:', error.message);
        });

        // 4. 添加飞书UserID字段
        await queryInterface.addColumn(
          'users',
          'feishuUserId',
          {
            type: Sequelize.STRING,
            allowNull: true,
            comment: '飞书用户ID'
          },
          { transaction }
        ).catch(error => {
          console.log('feishuUserId字段可能已存在:', error.message);
        });

        // 5. 添加飞书头像URL字段
        await queryInterface.addColumn(
          'users',
          'feishuAvatar',
          {
            type: Sequelize.STRING,
            allowNull: true,
            comment: '飞书用户头像URL'
          },
          { transaction }
        ).catch(error => {
          console.log('feishuAvatar字段可能已存在:', error.message);
        });

        // 6. 添加飞书访问令牌字段
        await queryInterface.addColumn(
          'users',
          'feishuAccessToken',
          {
            type: Sequelize.STRING,
            allowNull: true,
            comment: '飞书访问令牌'
          },
          { transaction }
        ).catch(error => {
          console.log('feishuAccessToken字段可能已存在:', error.message);
        });

        // 7. 添加飞书刷新令牌字段
        await queryInterface.addColumn(
          'users',
          'feishuRefreshToken',
          {
            type: Sequelize.STRING,
            allowNull: true,
            comment: '飞书刷新令牌'
          },
          { transaction }
        ).catch(error => {
          console.log('feishuRefreshToken字段可能已存在:', error.message);
        });

        // 8. 添加飞书令牌过期时间字段
        await queryInterface.addColumn(
          'users',
          'feishuTokenExpireTime',
          {
            type: Sequelize.DATE,
            allowNull: true,
            comment: '飞书令牌过期时间'
          },
          { transaction }
        ).catch(error => {
          console.log('feishuTokenExpireTime字段可能已存在:', error.message);
        });

        console.log('所有飞书相关字段已添加成功');
      } catch (error) {
        console.error('迁移失败:', error);
        throw error;
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      try {
        // 移除所有添加的字段（按相反顺序）
        await queryInterface.removeColumn('users', 'feishuTokenExpireTime', { transaction });
        await queryInterface.removeColumn('users', 'feishuRefreshToken', { transaction });
        await queryInterface.removeColumn('users', 'feishuAccessToken', { transaction });
        await queryInterface.removeColumn('users', 'feishuAvatar', { transaction });
        await queryInterface.removeColumn('users', 'feishuUserId', { transaction });
        await queryInterface.removeColumn('users', 'feishuUnionId', { transaction });
        await queryInterface.removeColumn('users', 'feishuOpenId', { transaction });
        
        // 删除authType枚举类型需要先删除字段，然后删除类型
        await queryInterface.removeColumn('users', 'authType', { transaction });
        await queryInterface.sequelize.query('DROP TYPE IF EXISTS enum_users_authType', { transaction });
        
        console.log('所有飞书相关字段已移除成功');
      } catch (error) {
        console.error('回滚迁移失败:', error);
        throw error;
      }
    });
  }
}; 
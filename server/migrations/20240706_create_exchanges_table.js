'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('exchanges', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1
        }
      },
      paymentMethod: {
        type: Sequelize.ENUM('ly', 'rmb'),
        allowNull: false,
        comment: '支付方式：ly(光年币)或rmb(人民币)'
      },
      contactInfo: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '联系信息'
      },
      location: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '用户所在职场位置'
      },
      remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '用户备注'
      },
      status: {
        type: Sequelize.ENUM('pending', 'approved', 'shipped', 'completed', 'rejected', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending',
        comment: '状态：待处理、已批准、已发货、已完成、已拒绝、已取消'
      },
      adminRemarks: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '管理员备注'
      },
      trackingNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '物流单号'
      },
      trackingCompany: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '物流公司'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('exchanges');
  }
}; 
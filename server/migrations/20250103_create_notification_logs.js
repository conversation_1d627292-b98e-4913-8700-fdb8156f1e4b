'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notification_logs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      notification_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '通知类型'
      },
      webhook_url: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: '发送的webhook地址'
      },
      request_payload: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '发送的消息内容(JSON格式)'
      },
      response_status: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'HTTP响应状态码'
      },
      response_body: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '响应内容(JSON格式)'
      },
      status: {
        type: Sequelize.ENUM('pending', 'success', 'failed'),
        defaultValue: 'pending',
        comment: '发送状态'
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '错误信息'
      },
      retry_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '重试次数'
      },
      max_retries: {
        type: Sequelize.INTEGER,
        defaultValue: 3,
        comment: '最大重试次数'
      },
      next_retry_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '下次重试时间'
      },
      sent_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '实际发送时间'
      },
      response_time: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '响应时间(毫秒)'
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '触发者用户ID'
      },
      trigger_source: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '触发源(auto/manual/test)'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 创建索引
    await queryInterface.addIndex('notification_logs', ['notification_type'], {
      name: 'idx_notification_logs_type'
    });
    await queryInterface.addIndex('notification_logs', ['status'], {
      name: 'idx_notification_logs_status'
    });
    await queryInterface.addIndex('notification_logs', ['created_at'], {
      name: 'idx_notification_logs_created_at'
    });
    await queryInterface.addIndex('notification_logs', ['next_retry_at'], {
      name: 'idx_notification_logs_next_retry'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notification_logs');
  }
}; 
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notification_configs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      notification_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '通知类型'
      },
      enabled: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        comment: '是否启用'
      },
      webhook_url: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: 'webhook地址'
      },
      schedule_time: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '定时发送时间'
      },
      retry_count: {
        type: Sequelize.INTEGER,
        defaultValue: 3,
        comment: '重试次数'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 创建索引
    await queryInterface.addIndex('notification_configs', ['notification_type'], {
      unique: true,
      name: 'idx_notification_type'
    });

    // 插入默认配置数据
    await queryInterface.bulkInsert('notification_configs', [
      {
        notification_type: 'exchange_notification',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'stock_alert',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'new_user_welcome',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'new_product_notification',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'daily_report',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        schedule_time: '19:00',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'weekly_report',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        schedule_time: '09:00',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'monthly_report',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        schedule_time: '10:00',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'order_alert',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'maintenance_notification',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        notification_type: 'error_alert',
        enabled: true,
        webhook_url: process.env.FEISHU_BOT_WEBHOOK_URL || null,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notification_configs');
  }
}; 
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加imageUrls字段到announcements表
    await queryInterface.addColumn('announcements', 'imageUrls', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: '公告多图片URL（JSON数组格式）'
    });
    
    // 同步现有单图数据到多图字段
    const announcements = await queryInterface.sequelize.query(
      'SELECT id, imageUrl FROM announcements WHERE imageUrl IS NOT NULL',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    for (const announcement of announcements) {
      if (announcement.imageUrl) {
        // 将单图同步到多图字段
        await queryInterface.sequelize.query(
          'UPDATE announcements SET imageUrls = ? WHERE id = ?',
          {
            replacements: [JSON.stringify([announcement.imageUrl]), announcement.id],
            type: Sequelize.QueryTypes.UPDATE
          }
        );
      }
    }
    
    console.log('已成功添加imageUrls字段到announcements表');
  },

  down: async (queryInterface, Sequelize) => {
    // 删除imageUrls字段
    await queryInterface.removeColumn('announcements', 'imageUrls');
    console.log('已成功移除imageUrls字段');
  }
}; 
'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('exchanges', 'amount', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
      comment: '订单总金额（根据quantity和产品价格计算）'
    });
    
    // 添加字段后计算已有记录的amount值
    console.log('正在计算现有订单的金额...');
    
    // 更新人民币订单
    await queryInterface.sequelize.query(`
      UPDATE exchanges e 
      JOIN products p ON e.productId = p.id
      SET e.amount = e.quantity * p.rmbPrice
      WHERE e.paymentMethod = 'rmb'
    `);
    
    // 更新光年币订单
    await queryInterface.sequelize.query(`
      UPDATE exchanges e 
      JOIN products p ON e.productId = p.id
      SET e.amount = e.quantity * p.lyPrice
      WHERE e.paymentMethod = 'ly'
    `);
    
    console.log('订单金额计算完成');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('exchanges', 'amount');
  }
}; 
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 先修改枚举类型
      await queryInterface.sequelize.query(`
        ALTER TABLE notifications 
        MODIFY COLUMN type 
        ENUM('exchange', 'feedback', 'stock_alert', 'product')
      `);

      return Promise.resolve();
    } catch (error) {
      console.error('迁移失败:', error);
      return Promise.reject(error);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 恢复原有枚举类型（注意：如果已有product类型的通知，此操作可能会失败）
      await queryInterface.sequelize.query(`
        ALTER TABLE notifications 
        MODIFY COLUMN type 
        ENUM('exchange', 'feedback', 'stock_alert')
      `);

      return Promise.resolve();
    } catch (error) {
      console.error('回滚失败:', error);
      return Promise.reject(error);
    }
  }
}; 
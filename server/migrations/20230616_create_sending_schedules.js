'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('sending_schedules', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      schedule_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '调度名称'
      },
      notification_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '关联的通知类型'
      },
      schedule_type: {
        type: Sequelize.ENUM('fixed', 'smart', 'conditional'),
        defaultValue: 'fixed',
        comment: '调度类型'
      },
      cron_expression: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Cron表达式'
      },
      time_windows: {
        type: Sequelize.JSON,
        comment: '时间窗口配置'
      },
      conditions: {
        type: Sequelize.JSON,
        comment: '触发条件'
      },
      priority: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: '优先级'
      },
      enabled: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        comment: '是否启用'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        allowNull: false
      }
    }, {
      charset: 'utf8mb4',
      collate: 'utf8mb4_general_ci',
      comment: '发送时间调度表'
    });

    // 索引配置
    const indexes = [
      {
        name: 'idx_notification_schedule_type',
        unique: true,
        fields: ['notification_type', 'schedule_type']
      },
      {
        name: 'idx_schedule_enabled',
        fields: ['enabled']
      }
    ];
    
    // 添加索引
    for (const index of indexes) {
      try {
        await queryInterface.addIndex('sending_schedules', index.fields, {
          name: index.name,
          unique: index.unique || false
        });
      } catch (error) {
        // 如果索引已存在，则跳过
        if (error.code === 'ER_DUP_KEYNAME') {
          console.log(`索引 ${index.name} 已存在，跳过创建`);
        } else {
          throw error;
        }
      }
    }

    // 添加一些基础调度配置
    await queryInterface.bulkInsert('sending_schedules', [
      {
        schedule_name: '工作日订单通知',
        notification_type: 'exchange',
        schedule_type: 'smart',
        cron_expression: null,
        time_windows: JSON.stringify({
          workDays: true,
          weekends: false,
          holidays: false,
          timeRanges: [
            { start: '09:00', end: '12:00', priority: 3 },
            { start: '14:00', end: '17:30', priority: 2 }
          ]
        }),
        conditions: JSON.stringify({
          systemLoad: { maxCpuPercent: 70, maxMemoryPercent: 80 },
          messageCount: { maxBatchSize: 20, delayBetweenBatches: 300 }
        }),
        priority: 10,
        enabled: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        schedule_name: '每日运营报告',
        notification_type: 'daily_report',
        schedule_type: 'fixed',
        cron_expression: '0 19 * * 1-5',
        time_windows: null,
        conditions: null,
        priority: 5,
        enabled: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        schedule_name: '系统通知智能发送',
        notification_type: 'system',
        schedule_type: 'conditional',
        cron_expression: null,
        time_windows: JSON.stringify({
          workDays: true,
          weekends: true,
          holidays: false,
          timeRanges: [
            { start: '10:00', end: '16:00', priority: 1 }
          ]
        }),
        conditions: JSON.stringify({
          userActivity: { minActiveUsers: 5, considerTimeWindow: true },
          urgency: { highPriority: 'immediate', normalPriority: 'next_window', lowPriority: 'next_day' }
        }),
        priority: 20,
        enabled: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('sending_schedules');
  }
}; 
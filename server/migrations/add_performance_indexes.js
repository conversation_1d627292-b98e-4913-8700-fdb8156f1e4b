'use strict';

/**
 * 性能优化索引迁移
 * 这些索引是安全的，不会影响现有功能，只会提升查询性能
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('开始添加性能优化索引...');
    
    try {
      // 商品表性能索引
      await queryInterface.addIndex('products', ['categoryId', 'status'], {
        name: 'idx_products_category_status',
        concurrently: true // MySQL不支持，但PostgreSQL支持并发创建
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      await queryInterface.addIndex('products', ['status', 'createdAt'], {
        name: 'idx_products_status_created'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      await queryInterface.addIndex('products', ['lyPrice'], {
        name: 'idx_products_ly_price'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      await queryInterface.addIndex('products', ['rmbPrice'], {
        name: 'idx_products_rmb_price'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      // 兑换记录表性能索引
      await queryInterface.addIndex('exchanges', ['userId', 'status'], {
        name: 'idx_exchanges_user_status'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      await queryInterface.addIndex('exchanges', ['productId', 'status'], {
        name: 'idx_exchanges_product_status'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      await queryInterface.addIndex('exchanges', ['status', 'createdAt'], {
        name: 'idx_exchanges_status_created'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      // 用户表性能索引
      await queryInterface.addIndex('users', ['isActive', 'lastLoginAt'], {
        name: 'idx_users_active_login'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      // 商品图片表性能索引
      await queryInterface.addIndex('product_images', ['productId', 'sortOrder'], {
        name: 'idx_product_images_product_sort'
      }).catch(err => console.log('索引可能已存在:', err.message));
      
      console.log('性能优化索引添加完成');
    } catch (error) {
      console.error('添加索引时出错:', error);
      // 不抛出错误，确保迁移不会失败
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('开始移除性能优化索引...');
    
    const indexesToRemove = [
      'idx_products_category_status',
      'idx_products_status_created',
      'idx_products_ly_price',
      'idx_products_rmb_price',
      'idx_exchanges_user_status',
      'idx_exchanges_product_status',
      'idx_exchanges_status_created',
      'idx_users_active_login',
      'idx_product_images_product_sort'
    ];
    
    for (const indexName of indexesToRemove) {
      try {
        await queryInterface.removeIndex('products', indexName);
      } catch (error) {
        console.log(`移除索引 ${indexName} 失败:`, error.message);
      }
    }
    
    console.log('性能优化索引移除完成');
  }
}; 
'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // 获取当前列定义
    const tableInfo = await queryInterface.describeTable('notifications');
    const typeColumn = tableInfo.type;
    
    if (typeColumn) {
      // 修改type列的ENUM类型，增加stock_alert选项
      await queryInterface.changeColumn('notifications', 'type', {
        type: Sequelize.ENUM('exchange', 'feedback', 'stock_alert'),
        allowNull: false
      });
      
      console.log('通知类型字段已更新，增加了stock_alert类型');
    }
  },

  async down (queryInterface, Sequelize) {
    // 恢复原来的类型定义
    const tableInfo = await queryInterface.describeTable('notifications');
    const typeColumn = tableInfo.type;
    
    if (typeColumn) {
      // 先修改回原始的ENUM类型
      await queryInterface.changeColumn('notifications', 'type', {
        type: Sequelize.ENUM('exchange', 'feedback'),
        allowNull: false
      });
      
      console.log('通知类型字段已恢复为原始设置');
    }
  }
};

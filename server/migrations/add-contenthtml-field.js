require('dotenv').config();
const mysql = require('mysql2/promise');

// 从环境变量读取数据库配置，如果没有则使用默认值
const DB_NAME = process.env.DB_NAME || 'feishu_mall';
const DB_USER = process.env.DB_USER || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || '';
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || 3306;

async function addContentHtmlField() {
  console.log('尝试连接数据库...');
  console.log(`数据库: ${DB_NAME}, 用户: ${DB_USER}, 主机: ${DB_HOST}`);

  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: DB_HOST,
    port: DB_PORT,
    user: DB_USER,
    password: DB_PASSWORD,
    database: DB_NAME
  });

  try {
    console.log('数据库连接成功!');

    // 检查announcements表是否存在
    const [tables] = await connection.query(
      `SELECT TABLE_NAME FROM information_schema.TABLES
       WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'announcements'`,
      [DB_NAME]
    );

    if (tables.length === 0) {
      console.log('announcements表不存在，无需添加字段');
      return;
    }

    // 检查contentHtml字段是否已存在
    const [columns] = await connection.query(
      `SELECT COLUMN_NAME FROM information_schema.COLUMNS
       WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'announcements' AND COLUMN_NAME = 'contentHtml'`,
      [DB_NAME]
    );

    if (columns.length > 0) {
      console.log('contentHtml字段已存在，无需添加');
      return;
    }

    // 添加contentHtml字段
    console.log('正在添加contentHtml字段...');
    await connection.query(
      `ALTER TABLE announcements
       ADD COLUMN contentHtml TEXT AFTER content`
    );
    console.log('contentHtml字段添加成功');

    // 将现有content字段的值复制到新的contentHtml字段
    console.log('正在更新现有记录...');
    await connection.query(
      `UPDATE announcements SET contentHtml = content
       WHERE contentHtml IS NULL`
    );
    console.log('现有记录更新完成');

  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    // 关闭数据库连接
    await connection.end();
    console.log('数据库连接已关闭');
  }
}

// 执行迁移
addContentHtmlField()
  .then(() => {
    console.log('迁移脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('迁移脚本执行失败:', error);
    process.exit(1);
  });
const { Sequelize } = require('sequelize');
const { sequelize } = require('../config/database');

module.exports = {
  up: async () => {
    await sequelize.getQueryInterface().addColumn('users', 'workplaceId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'workplaces',
        key: 'id'
      },
      after: 'workplace'
    });

    // 迁移现有数据：将workplace字符串字段映射到workplaceId
    const [users] = await sequelize.query('SELECT id, workplace FROM users WHERE workplace IS NOT NULL');
    const [workplaces] = await sequelize.query('SELECT id, name FROM workplaces');
    
    // 创建职场名称到ID的映射
    const workplaceMap = {};
    workplaces.forEach(workplace => {
      workplaceMap[workplace.name] = workplace.id;
    });

    // 更新用户的workplaceId
    for (const user of users) {
      if (user.workplace && workplaceMap[user.workplace]) {
        await sequelize.query(
          'UPDATE users SET workplaceId = ? WHERE id = ?',
          {
            replacements: [workplaceMap[user.workplace], user.id],
            type: sequelize.QueryTypes.UPDATE
          }
        );
      }
    }
  },

  down: async () => {
    await sequelize.getQueryInterface().removeColumn('users', 'workplaceId');
  }
}; 
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('exchanges', 'orderNumber', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: '格式化的订单编号'
    });
    
    // 创建索引以提高查询性能
    await queryInterface.addIndex('exchanges', ['orderNumber'], {
      name: 'idx_exchange_order_number'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('exchanges', 'idx_exchange_order_number');
    await queryInterface.removeColumn('exchanges', 'orderNumber');
  }
}; 
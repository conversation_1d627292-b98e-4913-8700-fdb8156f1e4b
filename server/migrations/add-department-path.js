'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('users', 'departmentPath', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: null,
      comment: '用户完整部门路径，如"公司/技术部/后端组"'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('users', 'departmentPath');
  }
}; 
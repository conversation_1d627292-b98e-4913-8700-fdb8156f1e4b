'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('message_templates', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      template_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '模板名称'
      },
      template_code: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '模板代码，用于系统识别',
        unique: true
      },
      notification_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '通知类型'
      },
      template_content: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: '模板内容'
      },
      variables: {
        type: Sequelize.JSON,
        comment: '可用变量列表'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        allowNull: false
      }
    }, {
      charset: 'utf8mb4',
      collate: 'utf8mb4_general_ci',
      comment: '消息模板表'
    });

    // 添加一些基础模板数据
    await queryInterface.bulkInsert('message_templates', [
      {
        template_name: '订单确认通知',
        template_code: 'order_confirmed',
        notification_type: 'exchange',
        template_content: JSON.stringify({
          "msg_type": "interactive",
          "card": {
            "elements": [
              {
                "tag": "div",
                "text": {
                  "content": "🎉 **订单确认通知**\n\n📋 **订单编号**：{{orderNumber}}\n🛒 **商品名称**：{{productName}}\n📦 **数量**：{{quantity}}\n💰 **总金额**：{{totalAmount}}{{currency}}\n🕒 **下单时间**：{{orderTime}}\n📍 **兑换地点**：{{location}}\n\n{{#if remarks}}📝 **备注**：{{remarks}}{{/if}}\n\n感谢您的订单！我们会尽快处理。",
                  "tag": "lark_md"
                }
              }
            ],
            "header": {
              "title": {
                "content": "📦 订单确认 - {{productName}}",
                "tag": "plain_text"
              },
              "template": "blue"
            }
          }
        }),
        variables: JSON.stringify([
          "orderNumber", "productName", "quantity", "totalAmount", "currency", "orderTime", "location", "remarks"
        ]),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        template_name: '新用户欢迎',
        template_code: 'new_user_welcome',
        notification_type: 'user',
        template_content: JSON.stringify({
          "msg_type": "interactive",
          "card": {
            "elements": [
              {
                "tag": "div",
                "text": {
                  "content": "👋 **欢迎加入光年小卖部**\n\n🙋 **用户名**：{{username}}\n🏢 **部门**：{{department}}\n📱 **联系方式**：{{contactInfo}}\n📅 **注册时间**：{{registerTime}}\n\n🎁 感谢您注册光年小卖部！\n\n快去查看有哪些好物可以兑换吧~",
                  "tag": "lark_md"
                }
              }
            ],
            "header": {
              "title": {
                "content": "🎉 欢迎新用户 - {{username}}",
                "tag": "plain_text"
              },
              "template": "green"
            }
          }
        }),
        variables: JSON.stringify([
          "username", "department", "contactInfo", "registerTime"
        ]),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        template_name: '系统维护通知',
        template_code: 'system_maintenance',
        notification_type: 'system',
        template_content: JSON.stringify({
          "msg_type": "interactive",
          "card": {
            "elements": [
              {
                "tag": "div",
                "text": {
                  "content": "🔧 **系统维护通知**\n\n📋 **维护主题**：{{title}}\n⏰ **维护时间**：{{startTime}} - {{endTime}}\n⏱️ **预计耗时**：{{duration}}\n🔍 **维护原因**：{{reason}}\n\n⚠️ **影响范围**\n{{impactText}}\n\n📋 **准备事项**\n{{preparationsText}}\n\n📞 **技术支持**：{{contactInfo}}\n\n💡 **温馨提示**：请提前做好相关准备，维护期间可能影响系统正常使用，感谢您的理解与配合！",
                  "tag": "lark_md"
                }
              }
            ],
            "header": {
              "title": {
                "content": "🔧 光年小卖部 - 系统维护通知",
                "tag": "plain_text"
              },
              "template": "{{templateColor}}"
            }
          }
        }),
        variables: JSON.stringify([
          "title", "startTime", "endTime", "duration", "reason", "impactText", "preparationsText", "contactInfo", "templateColor"
        ]),
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('message_templates');
  }
}; 
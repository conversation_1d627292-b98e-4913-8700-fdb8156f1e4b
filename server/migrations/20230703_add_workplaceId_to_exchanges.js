const { Sequelize } = require('sequelize');
const { sequelize } = require('../config/database');

module.exports = {
  up: async () => {
    await sequelize.getQueryInterface().addColumn('exchanges', 'workplaceId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'workplaces',
        key: 'id'
      },
      after: 'location'
    });

    // 迁移现有数据：将location字符串字段映射到workplaceId
    const [exchanges] = await sequelize.query('SELECT id, location FROM exchanges WHERE location IS NOT NULL');
    const [workplaces] = await sequelize.query('SELECT id, name FROM workplaces');
    
    // 创建职场名称到ID的映射
    const workplaceMap = {};
    workplaces.forEach(workplace => {
      workplaceMap[workplace.name] = workplace.id;
    });

    // 更新订单的workplaceId
    for (const exchange of exchanges) {
      if (exchange.location && workplaceMap[exchange.location]) {
        await sequelize.query(
          'UPDATE exchanges SET workplaceId = ? WHERE id = ?',
          {
            replacements: [workplaceMap[exchange.location], exchange.id],
            type: sequelize.QueryTypes.UPDATE
          }
        );
      }
    }
  },

  down: async () => {
    await sequelize.getQueryInterface().removeColumn('exchanges', 'workplaceId');
  }
}; 
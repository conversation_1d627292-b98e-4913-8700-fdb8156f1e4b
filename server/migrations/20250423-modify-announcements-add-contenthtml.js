'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加contentHtml字段
    await queryInterface.addColumn('announcements', 'contentHtml', {
      type: Sequelize.TEXT('long'),
      allowNull: true, // 允许为null以便于迁移
      comment: '公告HTML内容（渲染后的HTML）'
    });

    // 修改content字段类型和注释
    await queryInterface.changeColumn('announcements', 'content', {
      type: Sequelize.TEXT('long'),
      allowNull: false,
      comment: '公告原始内容（JSON格式保存，支持图文混排）'
    });

    // 更新现有数据，将content内容复制到contentHtml中
    const [announcements] = await queryInterface.sequelize.query(
      'SELECT id, content FROM announcements;'
    );

    for (const announcement of announcements) {
      await queryInterface.sequelize.query(
        `UPDATE announcements SET contentHtml = ? WHERE id = ?;`,
        {
          replacements: [announcement.content, announcement.id]
        }
      );
    }

    // 将contentHtml字段设为非空
    await queryInterface.changeColumn('announcements', 'contentHtml', {
      type: Sequelize.TEXT('long'),
      allowNull: false,
      comment: '公告HTML内容（渲染后的HTML）'
    });
  },

  async down(queryInterface, Sequelize) {
    // 移除contentHtml字段
    await queryInterface.removeColumn('announcements', 'contentHtml');

    // 还原content字段
    await queryInterface.changeColumn('announcements', 'content', {
      type: Sequelize.TEXT,
      allowNull: false
    });
  }
}; 
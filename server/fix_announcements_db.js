// 这个脚本用于直接通过SQL添加contentHtml字段
// 执行命令: node server/fix_announcements_db.js

// 导入必要的模块
const { sequelize } = require('./config/database');

async function fixAnnouncementsTable() {
  try {
    console.log('开始修复announcements表...');
    
    // 首先检查contentHtml字段是否已存在
    const [checkResults] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE 
        TABLE_SCHEMA = DATABASE() AND 
        TABLE_NAME = 'announcements' AND 
        COLUMN_NAME = 'contentHtml'
    `);
    
    const exists = checkResults[0].count > 0;
    
    if (exists) {
      console.log('contentHtml字段已存在，无需添加');
      return;
    }
    
    // 使用Sequelize实例执行原始SQL查询添加contentHtml字段
    await sequelize.query(`
      ALTER TABLE announcements 
      ADD COLUMN contentHtml TEXT 
      AFTER content
    `);
    
    console.log('已成功添加contentHtml字段');
    
    // 更新现有记录，将content的值复制到contentHtml
    await sequelize.query(`
      UPDATE announcements 
      SET contentHtml = content 
      WHERE contentHtml IS NULL
    `);
    
    console.log('已更新现有记录的contentHtml字段');
    
  } catch (error) {
    console.error('修复announcements表时出错:', error);
  } finally {
    // 关闭连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 执行函数
fixAnnouncementsTable()
  .then(() => {
    console.log('修复过程完成');
    process.exit(0);
  })
  .catch(err => {
    console.error('修复过程失败:', err);
    process.exit(1);
  }); 
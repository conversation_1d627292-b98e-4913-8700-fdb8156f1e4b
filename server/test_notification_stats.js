const { NotificationLog } = require('./models');
const notificationLogService = require('./services/notificationLogService');

async function testNotificationStats() {
  try {
    console.log('=== 开始测试通知统计功能 ===');
    
    // 1. 测试数据库连接
    console.log('1. 测试数据库连接...');
    const { sequelize } = require('./config/database');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 2. 检查NotificationLog模型
    console.log('2. 检查NotificationLog模型...');
    console.log('NotificationLog模型:', NotificationLog.name);
    console.log('表名:', NotificationLog.tableName);
    
    // 3. 检查表结构
    console.log('3. 检查表结构...');
    const tableInfo = await sequelize.query('DESCRIBE notification_logs', { type: sequelize.QueryTypes.SELECT });
    console.log('表结构:', tableInfo);
    
    // 4. 检查表数据
    console.log('4. 检查表数据...');
    const count = await NotificationLog.count();
    console.log('表中记录数:', count);
    
    // 5. 测试基础查询
    console.log('5. 测试基础查询...');
    const basicQuery = await NotificationLog.findAll({
      attributes: [
        'status',
        [NotificationLog.sequelize.fn('COUNT', '*'), 'count']
      ],
      group: ['status'],
      raw: true
    });
    console.log('基础查询结果:', basicQuery);
    
    // 6. 测试getNotificationStats方法
    console.log('6. 测试getNotificationStats方法...');
    const stats = await notificationLogService.getNotificationStats({});
    console.log('统计结果:', JSON.stringify(stats, null, 2));
    
    console.log('✅ 所有测试通过');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    process.exit(0);
  }
}

testNotificationStats();

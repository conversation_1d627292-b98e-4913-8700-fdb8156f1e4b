const fs = require('fs');
const path = require('path');
const { sequelize } = require('../config/database');

// 设置迁移文件目录
const migrationsDir = path.join(__dirname, '../migrations');

// 执行迁移
async function runMigrations() {
  try {
    console.log('开始数据库迁移...');
    
    // 获取所有迁移文件并排序
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js'))
      .sort((a, b) => {
        // 按照文件名中的日期/序号排序
        const numA = parseInt(a.split('_')[0], 10);
        const numB = parseInt(b.split('_')[0], 10);
        return numA - numB;
      });
    
    console.log(`找到 ${migrationFiles.length} 个迁移文件`);
    
    // 依次执行每个迁移
    for (const file of migrationFiles) {
      const migrationPath = path.join(migrationsDir, file);
      const migration = require(migrationPath);
      
      console.log(`执行迁移: ${file}`);
      
      if (typeof migration.up === 'function') {
        await migration.up();
        console.log(`迁移 ${file} 执行成功`);
      } else {
        console.warn(`迁移 ${file} 没有up函数，跳过`);
      }
    }
    
    console.log('所有迁移执行完成');
  } catch (error) {
    console.error('迁移过程中出错:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行迁移
runMigrations(); 
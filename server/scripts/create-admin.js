/**
 * 创建管理员用户的脚本
 * 
 * 运行方法：在server目录下执行 node scripts/create-admin.js
 */

const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const { User } = require('../models');

const createAdminUser = async () => {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 检查admin用户是否已存在
    const existingAdmin = await User.findOne({ where: { username: 'admin' } });
    if (existingAdmin) {
      console.log('管理员用户已存在，无需创建');
      return;
    }

    // 创建密码哈希
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);

    // 创建管理员用户
    const admin = await User.create({
      username: 'admin',
      password: hashedPassword,
      role: 'admin',
      points: 1000
    });

    console.log('管理员用户创建成功:', admin.toJSON());
  } catch (error) {
    console.error('创建管理员用户失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
};

// 执行创建用户
createAdminUser(); 
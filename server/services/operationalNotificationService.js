const { Op } = require('sequelize');
const { Exchange, Product, User, Feedback } = require('../models');
const feishuBotService = require('./feishuBotService');

/**
 * 运营数据推送服务
 */
class OperationalNotificationService {
  constructor() {
    // 里程碑配置
    this.milestones = {
      orders: [10, 50, 100, 200, 500, 1000, 2000, 5000], // 订单数量里程碑
      revenue_ly: [1000, 5000, 10000, 20000, 50000, 100000], // 光年币收入里程碑
      revenue_rmb: [500, 1000, 2000, 5000, 10000, 20000], // 人民币收入里程碑
      users: [10, 25, 50, 100, 200, 500] // 活跃用户里程碑
    };

    // 异常订单预警配置
    this.alertConfig = {
      highQuantityThreshold: 20, // 单次兑换数量超过20个为高风险
      highValueThreshold: {
        ly: 5000, // 光年币单笔订单超过5000为高风险
        rmb: 1000  // 人民币单笔订单超过1000为高风险
      },
      userDailyOrderLimit: 10, // 用户单日订单超过10单为异常
      timeWindowMinutes: 5 // 5分钟内多次下单为异常
    };
  }

  /**
   * 发送新用户欢迎通知
   * @param {Object} user - 新注册的用户信息
   */
  async sendNewUserWelcome(user) {
    try {
      // 仅对飞书登录用户发送欢迎通知
      if (!user.feishuUserId && !user.feishuOpenId) {
        console.log('跳过非飞书用户的欢迎通知:', user.username);
        return;
      }

      console.log('🎉 发送新用户欢迎通知:', user.username);
      await feishuBotService.sendNewUserWelcome(user);
      console.log('✅ 新用户欢迎通知发送成功');
    } catch (error) {
      console.error('❌ 发送新用户欢迎通知失败:', error);
      // 不抛出错误，避免影响用户注册流程
    }
  }

  /**
   * 检查并发送销售里程碑通知
   * @param {Object} exchange - 新创建的兑换记录
   */
  async checkSalesMilestone(exchange) {
    try {
      console.log('📊 检查销售里程碑...');

      // 获取总体统计数据
      const stats = await this.getOverallStats();

      // 检查各种里程碑
      await this.checkOrderMilestone(stats);
      await this.checkRevenueMilestone(stats);
      await this.checkUserMilestone(stats);

    } catch (error) {
      console.error('❌ 检查销售里程碑失败:', error);
    }
  }

  /**
   * 检查订单数量里程碑
   */
  async checkOrderMilestone(stats) {
    const currentOrders = stats.totalOrders;
    const milestone = this.findNearestMilestone('orders', currentOrders);

    if (milestone && this.isJustReached(milestone, currentOrders)) {
      const milestoneData = {
        milestoneType: 'orders',
        currentValue: currentOrders,
        milestoneValue: milestone,
        nextMilestone: this.getNextMilestone('orders', milestone),
        totalOrders: stats.totalOrders,
        totalRevenue: `光年币: ${stats.totalLyRevenue}, 人民币: ¥${stats.totalRmbRevenue}`,
        topProduct: stats.topProduct
      };

      await feishuBotService.sendSalesMilestone(milestoneData);
      console.log('✅ 订单数量里程碑通知发送成功:', milestone);
    }
  }

  /**
   * 检查收入里程碑
   */
  async checkRevenueMilestone(stats) {
    // 检查光年币收入里程碑
    const lyRevenue = stats.totalLyRevenue;
    const lyMilestone = this.findNearestMilestone('revenue_ly', lyRevenue);

    if (lyMilestone && this.isJustReached(lyMilestone, lyRevenue)) {
      const milestoneData = {
        milestoneType: 'revenue_ly',
        currentValue: lyRevenue,
        milestoneValue: lyMilestone,
        nextMilestone: this.getNextMilestone('revenue_ly', lyMilestone),
        totalOrders: stats.totalOrders,
        totalRevenue: `光年币: ${stats.totalLyRevenue}, 人民币: ¥${stats.totalRmbRevenue}`,
        topProduct: stats.topProduct
      };

      await feishuBotService.sendSalesMilestone(milestoneData);
      console.log('✅ 光年币收入里程碑通知发送成功:', lyMilestone);
    }

    // 检查人民币收入里程碑
    const rmbRevenue = stats.totalRmbRevenue;
    const rmbMilestone = this.findNearestMilestone('revenue_rmb', rmbRevenue);

    if (rmbMilestone && this.isJustReached(rmbMilestone, rmbRevenue)) {
      const milestoneData = {
        milestoneType: 'revenue_rmb',
        currentValue: rmbRevenue,
        milestoneValue: rmbMilestone,
        nextMilestone: this.getNextMilestone('revenue_rmb', rmbMilestone),
        totalOrders: stats.totalOrders,
        totalRevenue: `光年币: ${stats.totalLyRevenue}, 人民币: ¥${stats.totalRmbRevenue}`,
        topProduct: stats.topProduct
      };

      await feishuBotService.sendSalesMilestone(milestoneData);
      console.log('✅ 人民币收入里程碑通知发送成功:', rmbMilestone);
    }
  }

  /**
   * 检查用户数量里程碑
   */
  async checkUserMilestone(stats) {
    const activeUsers = stats.activeUsers;
    const milestone = this.findNearestMilestone('users', activeUsers);

    if (milestone && this.isJustReached(milestone, activeUsers)) {
      const milestoneData = {
        milestoneType: 'users',
        currentValue: activeUsers,
        milestoneValue: milestone,
        nextMilestone: this.getNextMilestone('users', milestone),
        totalOrders: stats.totalOrders,
        totalRevenue: `光年币: ${stats.totalLyRevenue}, 人民币: ¥${stats.totalRmbRevenue}`,
        topProduct: stats.topProduct
      };

      await feishuBotService.sendSalesMilestone(milestoneData);
      console.log('✅ 活跃用户里程碑通知发送成功:', milestone);
    }
  }

  /**
   * 检查异常订单并发送预警
   * @param {Object} exchange - 兑换记录
   * @param {Object} product - 商品信息
   * @param {Object} user - 用户信息
   */
  async checkOrderAlert(exchange, product, user) {
    try {
      console.log('🚨 检查异常订单预警...');

      const alerts = [];

      // 检查大额订单
      await this.checkHighValueOrder(exchange, product, user, alerts);

      // 检查大数量订单
      await this.checkHighQuantityOrder(exchange, product, user, alerts);

      // 检查用户行为异常
      await this.checkUserBehaviorAlert(exchange, product, user, alerts);

      // 发送所有预警
      for (const alert of alerts) {
        await feishuBotService.sendOrderAlert(alert);
        console.log('✅ 异常订单预警发送成功:', alert.alertReason);
      }

    } catch (error) {
      console.error('❌ 检查异常订单预警失败:', error);
    }
  }

  /**
   * 检查大额订单
   */
  async checkHighValueOrder(exchange, product, user, alerts) {
    const totalValue = exchange.paymentMethod === 'ly' 
      ? product.lyPrice * exchange.quantity
      : product.rmbPrice * exchange.quantity;

    const threshold = this.alertConfig.highValueThreshold[exchange.paymentMethod];

    if (totalValue >= threshold) {
      alerts.push({
        alertType: 'high_value',
        exchange,
        product,
        user,
        alertReason: `大额订单：${exchange.paymentMethod === 'ly' ? totalValue + ' 光年币' : '¥' + totalValue}`,
        riskLevel: totalValue >= threshold * 2 ? 'high' : 'medium'
      });
    }
  }

  /**
   * 检查大数量订单
   */
  async checkHighQuantityOrder(exchange, product, user, alerts) {
    if (exchange.quantity >= this.alertConfig.highQuantityThreshold) {
      alerts.push({
        alertType: 'high_quantity',
        exchange,
        product,
        user,
        alertReason: `大数量订单：一次兑换 ${exchange.quantity} 个商品`,
        riskLevel: exchange.quantity >= this.alertConfig.highQuantityThreshold * 2 ? 'high' : 'medium'
      });
    }
  }

  /**
   * 检查用户行为异常
   */
  async checkUserBehaviorAlert(exchange, product, user, alerts) {
    // 检查用户当日订单数量
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayOrders = await Exchange.count({
      where: {
        userId: user.id,
        createdAt: {
          [Op.between]: [today, tomorrow]
        }
      }
    });

    if (todayOrders > this.alertConfig.userDailyOrderLimit) {
      alerts.push({
        alertType: 'frequent_orders',
        exchange,
        product,
        user,
        alertReason: `频繁下单：今日已下单 ${todayOrders} 次`,
        riskLevel: 'medium'
      });
    }

    // 检查短时间内多次下单
    const recentTime = new Date(Date.now() - this.alertConfig.timeWindowMinutes * 60 * 1000);
    const recentOrders = await Exchange.count({
      where: {
        userId: user.id,
        createdAt: {
          [Op.gte]: recentTime
        }
      }
    });

    if (recentOrders > 3) {
      alerts.push({
        alertType: 'rapid_orders',
        exchange,
        product,
        user,
        alertReason: `快速下单：${this.alertConfig.timeWindowMinutes}分钟内下单 ${recentOrders} 次`,
        riskLevel: 'high'
      });
    }
  }

  /**
   * 发送用户反馈通知
   * @param {Object} feedback - 反馈信息
   * @param {Object} user - 用户信息
   */
  async sendFeedbackAlert(feedback, user) {
    try {
      console.log('💬 发送用户反馈通知...');

      // 判断是否为重要反馈
      const isImportant = this.isImportantFeedback(feedback);

      // 获取本月反馈总数
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const feedbackCount = await Feedback.count({
        where: {
          createdAt: {
            [Op.gte]: startOfMonth
          }
        }
      });

      // 获取该用户的反馈次数
      const userFeedbackCount = await Feedback.count({
        where: {
          userId: user.id
        }
      });

      const feedbackData = {
        feedback: {
          ...feedback.toJSON(),
          userFeedbackCount
        },
        user,
        isImportant,
        feedbackCount
      };

      await feishuBotService.sendFeedbackAlert(feedbackData);
      console.log('✅ 用户反馈通知发送成功');

    } catch (error) {
      console.error('❌ 发送用户反馈通知失败:', error);
    }
  }

  /**
   * 判断是否为重要反馈
   */
  isImportantFeedback(feedback) {
    // Bug反馈或投诉类型为重要反馈
    if (feedback.type === 'bug' || feedback.type === 'complaint') {
      return true;
    }

    // 内容包含紧急关键词
    const urgentKeywords = ['紧急', '重要', '严重', '无法使用', '故障', '错误', '异常'];
    const content = feedback.content.toLowerCase();
    
    return urgentKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 获取总体统计数据
   */
  async getOverallStats() {
    // 获取总订单数
    const totalOrders = await Exchange.count({
      where: {
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      }
    });

    // 获取收入统计
    const revenueStats = await Exchange.findAll({
      attributes: [
        'paymentMethod',
        [Exchange.sequelize.fn('SUM', 
          Exchange.sequelize.literal(
            'CASE WHEN paymentMethod = "ly" THEN Product.lyPrice * Exchange.quantity ELSE Product.rmbPrice * Exchange.quantity END'
          )
        ), 'totalRevenue']
      ],
      include: [{
        model: Product,
        attributes: []
      }],
      where: {
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      },
      group: ['paymentMethod'],
      raw: true
    });

    let totalLyRevenue = 0;
    let totalRmbRevenue = 0;

    revenueStats.forEach(stat => {
      if (stat.paymentMethod === 'ly') {
        totalLyRevenue = parseInt(stat.totalRevenue) || 0;
      } else {
        totalRmbRevenue = parseFloat(stat.totalRevenue) || 0;
      }
    });

    // 获取活跃用户数（有过兑换记录的用户）
    const activeUsers = await Exchange.count({
      distinct: true,
      col: 'userId',
      where: {
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      }
    });

    // 获取最热门商品
    const topProductStats = await Exchange.findOne({
      attributes: [
        'productId',
        [Exchange.sequelize.fn('SUM', Exchange.sequelize.col('quantity')), 'totalQuantity']
      ],
      include: [{
        model: Product,
        attributes: ['name']
      }],
      where: {
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      },
      group: ['productId'],
      order: [[Exchange.sequelize.fn('SUM', Exchange.sequelize.col('quantity')), 'DESC']]
    });

    const topProduct = topProductStats ? {
      name: topProductStats.Product.name,
      count: parseInt(topProductStats.dataValues.totalQuantity)
    } : null;

    return {
      totalOrders,
      totalLyRevenue,
      totalRmbRevenue,
      activeUsers,
      topProduct
    };
  }

  /**
   * 查找最近的里程碑
   */
  findNearestMilestone(type, currentValue) {
    const milestones = this.milestones[type];
    return milestones.find(milestone => milestone <= currentValue) || null;
  }

  /**
   * 判断是否刚刚达到里程碑
   */
  isJustReached(milestone, currentValue) {
    // 简单实现：假设刚创建的订单让我们达到了里程碑
    // 实际应用中可以存储上次检查的值进行比较
    return currentValue >= milestone && currentValue <= milestone + 5;
  }

  /**
   * 获取下一个里程碑
   */
  getNextMilestone(type, currentMilestone) {
    const milestones = this.milestones[type];
    const currentIndex = milestones.indexOf(currentMilestone);
    
    if (currentIndex >= 0 && currentIndex < milestones.length - 1) {
      return milestones[currentIndex + 1];
    }
    
    return null;
  }

  /**
   * 发送新品上架通知
   * @param {Object} product - 新商品信息
   * @param {Object} admin - 创建商品的管理员信息
   */
  async sendNewProductNotification(product, admin) {
    try {
      console.log('🎁 发送新品上架通知:', product.name);

      const productData = {
        product,
        admin
      };

      await feishuBotService.sendNewProductNotification(productData);
      console.log('✅ 新品上架通知发送成功');

    } catch (error) {
      console.error('❌ 发送新品上架通知失败:', error);
    }
  }
}

module.exports = new OperationalNotificationService(); 
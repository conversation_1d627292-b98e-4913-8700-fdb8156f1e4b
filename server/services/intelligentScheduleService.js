const cron = require('node-cron');
const SendingSchedule = require('../models/sendingSchedule');
const NotificationConfig = require('../models/notificationConfig');
const holidayService = require('./holidayService');
const { Op } = require('sequelize');
const os = require('os');
const schedule = require('node-schedule');

/**
 * 智能发送时间调度服务
 * 用于管理和控制消息发送时间
 */
class IntelligentScheduleService {
  constructor() {
    this.scheduledJobs = new Map();
    this.messageQueue = new Map();
    this.systemMetrics = {
      cpu: 0,
      memory: 0,
      lastUpdated: null
    };
    
    // 每分钟更新系统指标
    this.metricsJob = null;
  }

  /**
   * 初始化服务
   */
  async init() {
    console.log('初始化智能调度服务...');
    
    // 启动系统指标监控
    this.startMetricsMonitoring();
    
    // 加载所有调度配置
    await this.loadAllSchedules();
    
    console.log('智能调度服务初始化完成');
  }

  /**
   * 启动系统指标监控
   */
  startMetricsMonitoring() {
    if (this.metricsJob) {
      this.metricsJob.cancel();
    }
    
    this.metricsJob = schedule.scheduleJob('*/1 * * * *', () => {
      this.updateSystemMetrics();
    });
    
    // 立即更新一次
    this.updateSystemMetrics();
    
    console.log('系统指标监控已启动');
  }

  /**
   * 更新系统指标
   */
  updateSystemMetrics() {
    try {
      // 计算CPU使用率
      const cpus = os.cpus();
      let totalIdle = 0;
      let totalTick = 0;
      
      for (const cpu of cpus) {
        for (const type in cpu.times) {
          totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
      }
      
      // 与上次比较计算使用率
      if (this.systemMetrics.lastUpdated) {
        const idleDiff = totalIdle - this.systemMetrics.lastIdleTime;
        const tickDiff = totalTick - this.systemMetrics.lastTickTime;
        this.systemMetrics.cpu = 100 - Math.floor(100 * idleDiff / tickDiff);
      }
      
      // 更新内存使用率
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      this.systemMetrics.memory = Math.floor(100 * (totalMem - freeMem) / totalMem);
      
      // 保存当前值用于下次计算
      this.systemMetrics.lastIdleTime = totalIdle;
      this.systemMetrics.lastTickTime = totalTick;
      this.systemMetrics.lastUpdated = new Date();
    } catch (error) {
      console.error('更新系统指标失败:', error);
    }
  }

  /**
   * 加载所有调度配置
   */
  async loadAllSchedules() {
    try {
      // 清除现有的调度
      for (const [id, job] of this.scheduledJobs.entries()) {
        job.cancel();
        console.log(`调度 ${id} 已停止`);
      }
      this.scheduledJobs.clear();
      
      // 加载所有启用的调度
      const schedules = await SendingSchedule.findAll({
        where: { enabled: true }
      });
      
      console.log(`找到 ${schedules.length} 个启用的调度配置`);
      
      // 注册每个调度
      for (const schedule of schedules) {
        await this.registerSchedule(schedule);
      }
    } catch (error) {
      console.error('加载调度配置失败:', error);
    }
  }

  /**
   * 注册调度配置
   * @param {Object} scheduleData - 调度配置
   */
  async registerSchedule(scheduleData) {
    try {
      const { id, scheduleType, cronExpression, notificationType } = scheduleData;
      
      // 固定时间调度使用cron表达式
      if (scheduleType === 'fixed' && cronExpression) {
        if (!cron.validate(cronExpression)) {
          console.error(`调度 ${id}: 无效的cron表达式: ${cronExpression}`);
          return;
        }
        
        const job = schedule.scheduleJob(cronExpression, async () => {
          console.log(`调度 ${id} (${notificationType}) 触发执行`);
          await this.processScheduledNotification(scheduleData);
        });
        
        this.scheduledJobs.set(id, job);
        console.log(`调度 ${id} (${notificationType}) 已注册，使用cron表达式: ${cronExpression}`);
      }
      // 智能调度和条件调度在其他地方处理
      else if (scheduleType === 'smart' || scheduleType === 'conditional') {
        // 这些类型需要在请求发送通知时进行处理
        console.log(`调度 ${id} (${notificationType}) 已注册为${scheduleType}模式`);
      }
    } catch (error) {
      console.error(`注册调度 ${scheduleData.id} 失败:`, error);
    }
  }

  /**
   * 处理定时通知
   * @param {Object} schedule - 调度配置
   */
  async processScheduledNotification(schedule) {
    try {
      const { notificationType } = schedule;
      
      // 获取关联的通知配置
      const configs = await NotificationConfig.findAll({
        where: {
          notificationType,
          enabled: true
        }
      });
      
      for (const config of configs) {
        // 处理通知发送...
        // 这里需要集成实际的发送机制
        console.log(`处理通知: ${notificationType} (配置ID: ${config.id})`);
      }
    } catch (error) {
      console.error(`处理调度通知失败:`, error);
    }
  }

  /**
   * 获取调度列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 调度列表分页数据
   */
  async getSchedules(options = {}) {
    const {
      page = 1,
      pageSize = 20,
      notificationType,
      scheduleType,
      enabled,
      search
    } = options;

    const where = {};
    
    if (notificationType) {
      where.notificationType = notificationType;
    }
    
    if (scheduleType) {
      where.scheduleType = scheduleType;
    }
    
    if (enabled !== undefined) {
      where.enabled = enabled;
    }
    
    if (search) {
      where.scheduleName = { [Op.like]: `%${search}%` };
    }

    const { count, rows } = await SendingSchedule.findAndCountAll({
      where,
      order: [['priority', 'DESC'], ['id', 'ASC']],
      limit: pageSize,
      offset: (page - 1) * pageSize
    });

    return {
      total: count,
      page,
      pageSize,
      data: rows
    };
  }

  /**
   * 根据ID获取调度
   * @param {number} id - 调度ID
   * @returns {Promise<Object>} - 调度对象
   */
  async getScheduleById(id) {
    const schedule = await SendingSchedule.findByPk(id);
    if (!schedule) {
      throw new Error('调度配置不存在');
    }
    return schedule;
  }

  /**
   * 创建调度
   * @param {Object} scheduleData - 调度数据
   * @returns {Promise<Object>} - 创建的调度对象
   */
  async createSchedule(scheduleData) {
    // 校验调度类型和配置
    this._validateScheduleData(scheduleData);
    
    // 创建调度配置
    const schedule = await SendingSchedule.create(scheduleData);
    
    // 如果启用了，注册调度
    if (schedule.enabled) {
      await this.registerSchedule(schedule);
    }
    
    return schedule;
  }

  /**
   * 更新调度
   * @param {number} id - 调度ID
   * @param {Object} scheduleData - 更新的调度数据
   * @returns {Promise<Object>} - 更新后的调度对象
   */
  async updateSchedule(id, scheduleData) {
    const schedule = await this.getScheduleById(id);
    
    // 校验调度类型和配置
    this._validateScheduleData({
      ...schedule.toJSON(),
      ...scheduleData
    });
    
    // 更新前取消现有调度
    if (this.scheduledJobs.has(id)) {
      this.scheduledJobs.get(id).cancel();
      this.scheduledJobs.delete(id);
    }
    
    // 更新调度配置
    await schedule.update(scheduleData);
    
    // 如果启用了，重新注册调度
    if (schedule.enabled) {
      await this.registerSchedule(schedule);
    }
    
    return schedule;
  }

  /**
   * 删除调度
   * @param {number} id - 调度ID
   * @returns {Promise<boolean>} - 是否删除成功
   */
  async deleteSchedule(id) {
    const schedule = await this.getScheduleById(id);
    
    // 检查是否有配置关联此调度
    const relatedConfigs = await NotificationConfig.count({
      where: { scheduleId: id }
    });
    
    if (relatedConfigs > 0) {
      throw new Error(`有${relatedConfigs}个通知配置正在使用此调度，无法删除`);
    }
    
    // 取消调度
    if (this.scheduledJobs.has(id)) {
      this.scheduledJobs.get(id).cancel();
      this.scheduledJobs.delete(id);
    }
    
    // 删除调度配置
    await schedule.destroy();
    return true;
  }

  /**
   * 启用/禁用调度
   * @param {number} id - 调度ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise<Object>} - 更新后的调度对象
   */
  async toggleSchedule(id, enabled) {
    const schedule = await this.getScheduleById(id);
    
    // 取消现有调度
    if (this.scheduledJobs.has(id)) {
      this.scheduledJobs.get(id).cancel();
      this.scheduledJobs.delete(id);
    }
    
    // 更新状态
    await schedule.update({ enabled });
    
    // 如果启用了，重新注册调度
    if (enabled) {
      await this.registerSchedule(schedule);
    }
    
    return schedule;
  }

  /**
   * 计算下一次发送时间
   * @param {number} scheduleId - 调度ID
   * @param {string} messageType - 消息类型
   * @param {Object} options - 附加选项
   * @returns {Promise<Date>} - 下一次发送时间
   */
  async calculateNextSendTime(scheduleId, messageType, options = {}) {
    const schedule = await this.getScheduleById(scheduleId);
    const now = new Date();
    
    // 根据调度类型计算
    switch (schedule.scheduleType) {
      case 'fixed':
        // 使用cron表达式计算下一次执行时间
        if (schedule.cronExpression) {
          return schedule.nextInvocation();
        }
        break;
        
      case 'smart':
        // 智能调度
        return this._calculateSmartSendTime(schedule, now, options);
        
      case 'conditional':
        // 条件调度
        return this._calculateConditionalSendTime(schedule, messageType, options);
    }
    
    // 默认返回当前时间
    return now;
  }

  /**
   * 计算智能发送时间
   * @param {Object} schedule - 调度配置
   * @param {Date} now - 当前时间
   * @param {Object} options - 附加选项
   * @returns {Date} - 发送时间
   * @private
   */
  _calculateSmartSendTime(schedule, now, options = {}) {
    const timeWindows = schedule.timeWindows || {};
    const currentDay = now.getDay(); // 0-6, 0是周日
    const isWeekend = currentDay === 0 || currentDay === 6;
    
    // 检查今天是否是节假日
    const isHoliday = holidayService.isHoliday(now);
    
    // 检查是否允许在当前日期类型发送
    if (isHoliday && !timeWindows.holidays) {
      // 不允许在节假日发送，寻找下一个工作日
      return holidayService.getNextWorkingDay(now);
    }
    
    if (isWeekend && !timeWindows.weekends) {
      // 不允许在周末发送，寻找下一个工作日
      return holidayService.getNextWorkingDay(now);
    }
    
    if (!isWeekend && !isHoliday && !timeWindows.workDays) {
      // 不允许在工作日发送，寻找下一个非工作日
      return holidayService.getNextNonWorkingDay(now);
    }
    
    // 如果当前日期允许发送，检查时间窗口
    if (timeWindows.timeRanges && timeWindows.timeRanges.length > 0) {
      // 按优先级排序
      const sortedRanges = [...timeWindows.timeRanges].sort((a, b) => 
        (b.priority || 0) - (a.priority || 0)
      );
      
      // 获取当前时间
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const currentTimeMinutes = currentHour * 60 + currentMinute;
      
      // 检查每个时间窗口
      for (const range of sortedRanges) {
        const [startHour, startMinute] = range.start.split(':').map(Number);
        const [endHour, endMinute] = range.end.split(':').map(Number);
        
        const startTimeMinutes = startHour * 60 + startMinute;
        const endTimeMinutes = endHour * 60 + endMinute;
        
        // 如果当前时间在窗口内，立即发送
        if (currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes) {
          return now;
        }
        
        // 如果当前时间在窗口之前，安排在窗口开始时发送
        if (currentTimeMinutes < startTimeMinutes) {
          const targetTime = new Date(now);
          targetTime.setHours(startHour, startMinute, 0, 0);
          return targetTime;
        }
      }
      
      // 如果所有窗口都已经过去，安排在明天的第一个窗口
      const firstWindow = sortedRanges[0];
      const [startHour, startMinute] = firstWindow.start.split(':').map(Number);
      
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(startHour, startMinute, 0, 0);
      
      // 递归检查明天是否符合发送条件
      return this._calculateSmartSendTime(schedule, tomorrow, options);
    }
    
    // 默认立即发送
    return now;
  }

  /**
   * 计算条件发送时间
   * @param {Object} schedule - 调度配置
   * @param {string} messageType - 消息类型
   * @param {Object} options - 附加选项
   * @returns {Date} - 发送时间
   * @private
   */
  _calculateConditionalSendTime(schedule, messageType, options = {}) {
    const conditions = schedule.conditions || {};
    const now = new Date();
    
    // 检查系统负载条件
    if (conditions.systemLoad) {
      const { maxCpuPercent, maxMemoryPercent } = conditions.systemLoad;
      
      // 如果当前系统负载超过阈值，延迟发送
      if (this.systemMetrics.cpu > maxCpuPercent || this.systemMetrics.memory > maxMemoryPercent) {
        // 延迟10分钟后重试
        const delayedTime = new Date(now);
        delayedTime.setMinutes(delayedTime.getMinutes() + 10);
        return delayedTime;
      }
    }
    
    // 检查消息队列条件
    if (conditions.messageCount) {
      const { maxBatchSize } = conditions.messageCount;
      
      // 如果队列中消息数超过阈值，延迟发送
      const queueSize = this.getQueueSize(messageType);
      if (queueSize >= maxBatchSize) {
        // 延迟一段时间
        const delayedTime = new Date(now);
        delayedTime.setMinutes(delayedTime.getMinutes() + 5);
        return delayedTime;
      }
    }
    
    // 检查紧急程度条件
    if (conditions.urgency && options.priority) {
      const { highPriority, normalPriority, lowPriority } = conditions.urgency;
      
      // 根据优先级决定发送策略
      if (options.priority === 'high' && highPriority === 'immediate') {
        // 高优先级立即发送
        return now;
      }
      
      if (options.priority === 'low' && lowPriority === 'next_day') {
        // 低优先级安排在第二天发送
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(10, 0, 0, 0); // 默认早上10点
        return tomorrow;
      }
      
      // 其他情况使用智能时间计算
      return this._calculateSmartSendTime(schedule, now, options);
    }
    
    // 默认立即发送
    return now;
  }

  /**
   * 获取队列大小
   * @param {string} messageType - 消息类型
   * @returns {number} - 队列大小
   */
  getQueueSize(messageType) {
    if (!this.messageQueue.has(messageType)) {
      return 0;
    }
    return this.messageQueue.get(messageType).length;
  }

  /**
   * 添加消息到队列
   * @param {string} messageType - 消息类型
   * @param {Object} message - 消息数据
   */
  addToQueue(messageType, message) {
    if (!this.messageQueue.has(messageType)) {
      this.messageQueue.set(messageType, []);
    }
    this.messageQueue.get(messageType).push({
      message,
      addedAt: new Date()
    });
  }

  /**
   * 验证调度数据
   * @param {Object} scheduleData - 调度数据
   * @private
   */
  _validateScheduleData(scheduleData) {
    const { scheduleType, cronExpression, timeWindows } = scheduleData;
    
    // 固定调度必须有cron表达式
    if (scheduleType === 'fixed') {
      if (!cronExpression) {
        throw new Error('固定调度必须提供cron表达式');
      }
      
      if (!cron.validate(cronExpression)) {
        throw new Error(`无效的cron表达式: ${cronExpression}`);
      }
    }
    
    // 智能调度必须有时间窗口
    if (scheduleType === 'smart' && (!timeWindows || typeof timeWindows !== 'object')) {
      throw new Error('智能调度必须提供时间窗口配置');
    }
  }
}

module.exports = new IntelligentScheduleService(); 
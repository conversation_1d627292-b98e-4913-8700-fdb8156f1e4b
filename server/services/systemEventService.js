const feishuBotService = require('./feishuBotService');

/**
 * 系统事件通知服务
 * 用于系统维护通知和错误告警推送
 */
class SystemEventService {
  constructor() {
    // 错误告警配置
    this.alertConfig = {
      // 错误频率阈值配置
      errorThresholds: {
        critical: {
          count: 1,      // 1次即发送
          timeWindow: 300 // 5分钟内
        },
        high: {
          count: 3,      // 3次发送
          timeWindow: 600 // 10分钟内
        },
        medium: {
          count: 5,      // 5次发送
          timeWindow: 900 // 15分钟内
        },
        low: {
          count: 10,     // 10次发送
          timeWindow: 1800 // 30分钟内
        }
      },
      
      // 错误记录存储（内存中，实际应用中应该用数据库）
      errorHistory: new Map(),
      
      // 最大历史记录数
      maxHistorySize: 1000
    };

    // 系统维护计划存储
    this.maintenanceSchedule = [];
  }

  /**
   * 发送系统维护通知
   * @param {Object} maintenanceInfo - 维护信息
   */
  async sendMaintenanceNotification(maintenanceInfo) {
    try {
      const {
        type = 'scheduled',
        title,
        startTime,
        endTime,
        reason,
        impact = [],
        preparations = [],
        contactInfo = '系统管理员'
      } = maintenanceInfo;

      // 计算维护时长
      const start = new Date(startTime);
      const end = new Date(endTime);
      const durationMs = end - start;
      const hours = Math.floor(durationMs / (1000 * 60 * 60));
      const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      const duration = hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`;

      const maintenanceData = {
        type,
        title,
        startTime: start.toLocaleString('zh-CN'),
        endTime: end.toLocaleString('zh-CN'),
        duration,
        reason,
        impact,
        preparations,
        contactInfo
      };

      console.log('🔧 发送系统维护通知:', title);
      await feishuBotService.sendMaintenanceNotification(maintenanceData);
      
      // 记录维护计划
      this.maintenanceSchedule.push({
        ...maintenanceData,
        notifiedAt: new Date(),
        status: 'scheduled'
      });

      console.log('✅ 系统维护通知发送成功');
      return { success: true, message: '维护通知发送成功' };

    } catch (error) {
      console.error('❌ 发送系统维护通知失败:', error);
      throw error;
    }
  }

  /**
   * 发送错误告警推送
   * @param {Object} errorInfo - 错误信息
   */
  async sendErrorAlert(errorInfo) {
    try {
      const {
        severity = 'medium',
        errorType,
        errorMessage,
        systemComponent = '未知组件',
        stackTrace,
        resolution,
        affectedUsers,
        monitorUrl
      } = errorInfo;

      // 检查是否需要发送告警（基于频率限制）
      const shouldSendAlert = this.shouldSendErrorAlert(errorType, severity);
      
      if (!shouldSendAlert) {
        console.log(`🔇 错误告警被频率限制跳过: ${errorType}`);
        return { success: false, message: '告警被频率限制跳过' };
      }

      // 记录错误发生
      this.recordError(errorType, severity);

      const alertData = {
        severity,
        errorType,
        errorMessage,
        timestamp: new Date().toLocaleString('zh-CN'),
        affectedUsers,
        systemComponent,
        stackTrace,
        resolution,
        monitorUrl
      };

      console.log(`🚨 发送${severity}级错误告警:`, errorType);
      await feishuBotService.sendErrorAlert(alertData);
      console.log('✅ 错误告警发送成功');

      return { success: true, message: '错误告警发送成功' };

    } catch (error) {
      console.error('❌ 发送错误告警失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否应该发送错误告警（基于频率限制）
   * @param {string} errorType - 错误类型
   * @param {string} severity - 严重程度
   * @returns {boolean} - 是否应该发送
   */
  shouldSendErrorAlert(errorType, severity) {
    const config = this.alertConfig.errorThresholds[severity];
    if (!config) {
      return true; // 如果没有配置，默认发送
    }

    const now = Date.now();
    const timeWindow = config.timeWindow * 1000; // 转换为毫秒
    const errorKey = `${errorType}_${severity}`;

    // 获取历史记录
    if (!this.alertConfig.errorHistory.has(errorKey)) {
      this.alertConfig.errorHistory.set(errorKey, []);
    }

    const history = this.alertConfig.errorHistory.get(errorKey);
    
    // 清理过期记录
    const validHistory = history.filter(timestamp => now - timestamp < timeWindow);
    this.alertConfig.errorHistory.set(errorKey, validHistory);

    // 检查是否达到阈值
    if (validHistory.length >= config.count) {
      return false; // 频率太高，跳过发送
    }

    return true;
  }

  /**
   * 记录错误发生
   * @param {string} errorType - 错误类型
   * @param {string} severity - 严重程度
   */
  recordError(errorType, severity) {
    const errorKey = `${errorType}_${severity}`;
    const now = Date.now();

    if (!this.alertConfig.errorHistory.has(errorKey)) {
      this.alertConfig.errorHistory.set(errorKey, []);
    }

    const history = this.alertConfig.errorHistory.get(errorKey);
    history.push(now);

    // 限制历史记录大小
    if (history.length > this.alertConfig.maxHistorySize) {
      history.shift(); // 移除最旧的记录
    }
  }

  /**
   * 预定义错误类型的快速发送方法
   */

  /**
   * 发送数据库连接错误告警
   * @param {Object} error - 错误对象
   */
  async sendDatabaseError(error) {
    return this.sendErrorAlert({
      severity: 'critical',
      errorType: '数据库连接错误',
      errorMessage: error.message,
      systemComponent: '数据库',
      stackTrace: error.stack,
      resolution: '检查数据库连接配置和网络状态',
      affectedUsers: '所有用户'
    });
  }

  /**
   * 发送API接口错误告警
   * @param {Object} error - 错误对象
   * @param {string} endpoint - 接口路径
   */
  async sendAPIError(error, endpoint) {
    return this.sendErrorAlert({
      severity: 'high',
      errorType: 'API接口错误',
      errorMessage: `${endpoint}: ${error.message}`,
      systemComponent: 'API服务',
      stackTrace: error.stack,
      resolution: '检查接口逻辑和依赖服务状态'
    });
  }

  /**
   * 发送飞书服务错误告警
   * @param {Object} error - 错误对象
   */
  async sendFeishuServiceError(error) {
    return this.sendErrorAlert({
      severity: 'medium',
      errorType: '飞书服务错误',
      errorMessage: error.message,
      systemComponent: '飞书集成',
      stackTrace: error.stack,
      resolution: '检查飞书webhook配置和网络连接'
    });
  }

  /**
   * 发送文件上传错误告警
   * @param {Object} error - 错误对象
   * @param {string} fileName - 文件名
   */
  async sendFileUploadError(error, fileName) {
    return this.sendErrorAlert({
      severity: 'low',
      errorType: '文件上传错误',
      errorMessage: `文件 ${fileName}: ${error.message}`,
      systemComponent: '文件服务',
      stackTrace: error.stack,
      resolution: '检查文件大小、格式和存储空间'
    });
  }

  /**
   * 获取系统维护计划
   * @returns {Array} - 维护计划列表
   */
  getMaintenanceSchedule() {
    return this.maintenanceSchedule;
  }

  /**
   * 获取错误统计
   * @returns {Object} - 错误统计信息
   */
  getErrorStatistics() {
    const stats = {
      totalErrorTypes: this.alertConfig.errorHistory.size,
      recentErrors: {},
      errorsByComponent: {}
    };

    const now = Date.now();
    const last24Hours = 24 * 60 * 60 * 1000;

    for (const [errorKey, timestamps] of this.alertConfig.errorHistory.entries()) {
      const recentCount = timestamps.filter(timestamp => now - timestamp < last24Hours).length;
      if (recentCount > 0) {
        stats.recentErrors[errorKey] = recentCount;
      }
    }

    return stats;
  }

  /**
   * 清理过期的错误历史记录
   */
  cleanupErrorHistory() {
    const now = Date.now();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天

    for (const [errorKey, timestamps] of this.alertConfig.errorHistory.entries()) {
      const validTimestamps = timestamps.filter(timestamp => now - timestamp < maxAge);
      
      if (validTimestamps.length === 0) {
        this.alertConfig.errorHistory.delete(errorKey);
      } else {
        this.alertConfig.errorHistory.set(errorKey, validTimestamps);
      }
    }

    console.log(`🧹 清理了过期的错误历史记录，当前记录类型数: ${this.alertConfig.errorHistory.size}`);
  }
}

module.exports = new SystemEventService(); 
/**
 * 节假日服务
 * 用于智能调度时间控制中的节假日判断
 */
class HolidayService {
  constructor() {
    // 中国法定节假日（示例数据，实际应从API获取或数据库存储）
    this.holidays = {
      // 2023年法定节假日
      '2023-01-01': { name: '元旦', isHoliday: true },
      '2023-01-02': { name: '元旦', isHoliday: true },
      '2023-01-21': { name: '除夕', isHoliday: true },
      '2023-01-22': { name: '春节', isHoliday: true },
      '2023-01-23': { name: '春节', isHoliday: true },
      '2023-01-24': { name: '春节', isHoliday: true },
      '2023-01-25': { name: '春节', isHoliday: true },
      '2023-01-26': { name: '春节', isHoliday: true },
      '2023-01-27': { name: '春节', isHoliday: true },
      '2023-04-05': { name: '清明节', isHoliday: true },
      '2023-05-01': { name: '劳动节', isHoliday: true },
      '2023-05-02': { name: '劳动节', isHoliday: true },
      '2023-05-03': { name: '劳动节', isHoliday: true },
      '2023-06-22': { name: '端午节', isHoliday: true },
      '2023-06-23': { name: '端午节', isHoliday: true },
      '2023-06-24': { name: '端午节', isHoliday: true },
      '2023-09-29': { name: '中秋节', isHoliday: true },
      '2023-09-30': { name: '中秋节/国庆节', isHoliday: true },
      '2023-10-01': { name: '国庆节', isHoliday: true },
      '2023-10-02': { name: '国庆节', isHoliday: true },
      '2023-10-03': { name: '国庆节', isHoliday: true },
      '2023-10-04': { name: '国庆节', isHoliday: true },
      '2023-10-05': { name: '国庆节', isHoliday: true },
      '2023-10-06': { name: '国庆节', isHoliday: true },
      
      // 2023年调休工作日
      '2023-01-28': { name: '春节调休', isHoliday: false, isWorkday: true },
      '2023-01-29': { name: '春节调休', isHoliday: false, isWorkday: true },
      '2023-04-23': { name: '劳动节调休', isHoliday: false, isWorkday: true },
      '2023-05-06': { name: '劳动节调休', isHoliday: false, isWorkday: true },
      '2023-06-25': { name: '端午节调休', isHoliday: false, isWorkday: true },
      '2023-10-07': { name: '国庆节调休', isHoliday: false, isWorkday: true },
      '2023-10-08': { name: '国庆节调休', isHoliday: false, isWorkday: true },
      
      // 2024年法定节假日
      '2024-01-01': { name: '元旦', isHoliday: true },
      '2024-02-10': { name: '除夕', isHoliday: true },
      '2024-02-11': { name: '春节', isHoliday: true },
      '2024-02-12': { name: '春节', isHoliday: true },
      '2024-02-13': { name: '春节', isHoliday: true },
      '2024-02-14': { name: '春节', isHoliday: true },
      '2024-02-15': { name: '春节', isHoliday: true },
      '2024-02-16': { name: '春节', isHoliday: true },
      '2024-02-17': { name: '春节', isHoliday: true },
      '2024-04-04': { name: '清明节', isHoliday: true },
      '2024-04-05': { name: '清明节', isHoliday: true },
      '2024-04-06': { name: '清明节', isHoliday: true },
      '2024-05-01': { name: '劳动节', isHoliday: true },
      '2024-05-02': { name: '劳动节', isHoliday: true },
      '2024-05-03': { name: '劳动节', isHoliday: true },
      '2024-05-04': { name: '劳动节', isHoliday: true },
      '2024-05-05': { name: '劳动节', isHoliday: true },
      '2024-06-08': { name: '端午节', isHoliday: true },
      '2024-06-09': { name: '端午节', isHoliday: true },
      '2024-06-10': { name: '端午节', isHoliday: true },
      '2024-09-15': { name: '中秋节', isHoliday: true },
      '2024-09-16': { name: '中秋节', isHoliday: true },
      '2024-09-17': { name: '中秋节', isHoliday: true },
      '2024-10-01': { name: '国庆节', isHoliday: true },
      '2024-10-02': { name: '国庆节', isHoliday: true },
      '2024-10-03': { name: '国庆节', isHoliday: true },
      '2024-10-04': { name: '国庆节', isHoliday: true },
      '2024-10-05': { name: '国庆节', isHoliday: true },
      '2024-10-06': { name: '国庆节', isHoliday: true },
      '2024-10-07': { name: '国庆节', isHoliday: true },
      
      // 2024年调休工作日
      '2024-02-04': { name: '春节调休', isHoliday: false, isWorkday: true },
      '2024-02-18': { name: '春节调休', isHoliday: false, isWorkday: true },
      '2024-04-07': { name: '清明节调休', isHoliday: false, isWorkday: true },
      '2024-04-28': { name: '劳动节调休', isHoliday: false, isWorkday: true },
      '2024-05-11': { name: '劳动节调休', isHoliday: false, isWorkday: true },
      '2024-09-14': { name: '中秋节调休', isHoliday: false, isWorkday: true },
      '2024-09-29': { name: '国庆节调休', isHoliday: false, isWorkday: true },
      '2024-10-12': { name: '国庆节调休', isHoliday: false, isWorkday: true }
    };
  }

  /**
   * 格式化日期为YYYY-MM-DD格式
   * @param {Date} date - 日期对象
   * @returns {string} - 格式化后的日期字符串
   * @private
   */
  _formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 检查指定日期是否为节假日
   * @param {Date|string} date - 日期对象或日期字符串
   * @returns {boolean} - 是否为节假日
   */
  isHoliday(date) {
    const dateStr = typeof date === 'string' ? date : this._formatDate(date);
    const holiday = this.holidays[dateStr];
    
    if (holiday && holiday.isHoliday) {
      return true;
    }
    
    if (holiday && holiday.isWorkday) {
      return false;
    }
    
    // 如果不是特殊日期，则判断是否是周末
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    const day = date.getDay();
    return day === 0 || day === 6; // 0是周日，6是周六
  }

  /**
   * 检查指定日期是否为工作日
   * @param {Date|string} date - 日期对象或日期字符串
   * @returns {boolean} - 是否为工作日
   */
  isWorkday(date) {
    return !this.isHoliday(date);
  }

  /**
   * 获取下一个工作日
   * @param {Date|string} date - 日期对象或日期字符串
   * @returns {Date} - 下一个工作日
   */
  getNextWorkingDay(date) {
    let nextDay;
    
    if (typeof date === 'string') {
      nextDay = new Date(date);
    } else {
      nextDay = new Date(date);
    }
    
    // 从下一天开始查找
    nextDay.setDate(nextDay.getDate() + 1);
    
    // 一直查找直到找到工作日
    while (this.isHoliday(nextDay)) {
      nextDay.setDate(nextDay.getDate() + 1);
    }
    
    return nextDay;
  }

  /**
   * 获取下一个非工作日
   * @param {Date|string} date - 日期对象或日期字符串
   * @returns {Date} - 下一个非工作日
   */
  getNextNonWorkingDay(date) {
    let nextDay;
    
    if (typeof date === 'string') {
      nextDay = new Date(date);
    } else {
      nextDay = new Date(date);
    }
    
    // 从下一天开始查找
    nextDay.setDate(nextDay.getDate() + 1);
    
    // 一直查找直到找到非工作日
    while (!this.isHoliday(nextDay)) {
      nextDay.setDate(nextDay.getDate() + 1);
    }
    
    return nextDay;
  }

  /**
   * 获取指定时间范围内的工作日数量
   * @param {Date|string} startDate - 开始日期
   * @param {Date|string} endDate - 结束日期
   * @returns {number} - 工作日数量
   */
  getWorkdaysCount(startDate, endDate) {
    let start, end;
    
    if (typeof startDate === 'string') {
      start = new Date(startDate);
    } else {
      start = new Date(startDate);
    }
    
    if (typeof endDate === 'string') {
      end = new Date(endDate);
    } else {
      end = new Date(endDate);
    }
    
    // 确保开始日期不晚于结束日期
    if (start > end) {
      throw new Error('开始日期不能晚于结束日期');
    }
    
    let count = 0;
    let current = new Date(start);
    
    while (current <= end) {
      if (this.isWorkday(current)) {
        count++;
      }
      
      current.setDate(current.getDate() + 1);
    }
    
    return count;
  }

  /**
   * 获取指定月份的所有节假日
   * @param {number} year - 年份
   * @param {number} month - 月份（1-12）
   * @returns {Array} - 节假日数组
   */
  getHolidaysInMonth(year, month) {
    const results = [];
    
    // 确保月份格式正确
    const monthStr = String(month).padStart(2, '0');
    const prefix = `${year}-${monthStr}-`;
    
    // 遍历该月所有日期
    for (let day = 1; day <= 31; day++) {
      const dateStr = `${prefix}${String(day).padStart(2, '0')}`;
      const holiday = this.holidays[dateStr];
      
      // 检查日期是否有效
      const date = new Date(dateStr);
      if (date.getMonth() + 1 !== parseInt(monthStr)) {
        continue; // 跳过无效日期
      }
      
      if (holiday && holiday.isHoliday) {
        results.push({
          date: dateStr,
          name: holiday.name
        });
      }
    }
    
    return results;
  }

  /**
   * 更新节假日数据
   * @param {Object} holidays - 节假日数据
   */
  updateHolidays(holidays) {
    this.holidays = {
      ...this.holidays,
      ...holidays
    };
  }
}

module.exports = new HolidayService(); 
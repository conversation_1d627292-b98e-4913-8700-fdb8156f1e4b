const cron = require('node-cron');
const { Op } = require('sequelize');
const { Exchange, Product, User } = require('../models');
const feishuBotService = require('./feishuBotService');
const holidayService = require('./holidayService');

/**
 * 定时报告服务
 * 负责每日、每周和月度销售数据汇总报告
 */
class ScheduledReportService {
  constructor() {
    this.jobs = new Map(); // 存储定时任务
    this.isInitialized = false;
  }

  /**
   * 初始化定时任务
   */
  init() {
    if (this.isInitialized) {
      console.log('定时报告服务已经初始化');
      return;
    }

    console.log('初始化定时报告服务...');

    // 每日报告 - 工作日19:00
    const dailyJob = cron.schedule('0 19 * * 1-5', async () => {
      try {
        console.log('🕐 定时任务触发: 准备发送每日报告...');
        
        // 检查今天是否是工作日
        const today = new Date();
        const dateStatus = holidayService.getDateStatus(today);
        
        if (!dateStatus.isWorkingDay) {
          console.log(`⏭️ 今天是${dateStatus.description}，跳过每日报告`);
          return;
        }
        
        await this.sendDailyReport();
      } catch (error) {
        console.error('❌ 每日报告发送失败:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });

    // 每周报告 - 周一上午9:00
    const weeklyJob = cron.schedule('0 9 * * 1', async () => {
      try {
        console.log('🕐 定时任务触发: 准备发送每周报告...');
        
        // 检查今天是否是工作日
        const today = new Date();
        const dateStatus = holidayService.getDateStatus(today);
        
        if (!dateStatus.isWorkingDay) {
          console.log(`⏭️ 今天是${dateStatus.description}，推迟每周报告到下一个工作日`);
          
          // 找到下一个工作日发送
          const nextWorkingDay = holidayService.getNextWorkingDay(today);
          console.log(`📅 计划在${nextWorkingDay.toLocaleDateString()}发送每周报告`);
          return;
        }
        
        await this.sendWeeklyReport();
      } catch (error) {
        console.error('❌ 每周报告发送失败:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });

    // 每月报告 - 每月第1个工作日上午10:00
    const monthlyJob = cron.schedule('0 10 1 * *', async () => {
      try {
        console.log('🕐 定时任务触发: 准备发送月度报告...');
        
        const today = new Date();
        
        // 如果今天不是工作日，推迟到下一个工作日
        if (!holidayService.isWorkingDay(today)) {
          const nextWorkingDay = holidayService.getNextWorkingDay(today);
          console.log(`⏭️ 今天不是工作日，推迟月度报告到${nextWorkingDay.toLocaleDateString()}`);
          return;
        }
        
        await this.sendMonthlyReport();
      } catch (error) {
        console.error('❌ 月度报告发送失败:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });

    // 存储任务引用
    this.jobs.set('daily', dailyJob);
    this.jobs.set('weekly', weeklyJob);
    this.jobs.set('monthly', monthlyJob);

    this.isInitialized = true;
    console.log('✅ 定时报告服务初始化完成');
  }

  /**
   * 启动定时任务
   */
  start() {
    if (!this.isInitialized) {
      this.init();
    }

    this.jobs.forEach((job, name) => {
      job.start();
      console.log(`✅ ${name} 定时任务已启动`);
    });

    console.log('🚀 定时报告服务已启动');
  }

  /**
   * 停止定时任务
   */
  stop() {
    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(`⏹️ ${name} 定时任务已停止`);
    });

    console.log('⏹️ 定时报告服务已停止');
  }

  /**
   * 发送每日报告
   */
  async sendDailyReport() {
    try {
      console.log('📊 开始生成每日报告...');
      
      const today = new Date();
      
      // 设置查询时间范围（当天从0点到当前时间）
      const startOfToday = new Date(today);
      startOfToday.setHours(0, 0, 0, 0);
      
      const currentTime = new Date();
      
      console.log(`📅 生成报告时间范围: ${startOfToday.toLocaleString()} 至 ${currentTime.toLocaleString()}`);
      
      const reportData = await this.generateDailyReportData(startOfToday, currentTime);
      
      await feishuBotService.sendDailyReport(reportData);
      console.log('✅ 每日报告发送成功');
      
      return reportData;
    } catch (error) {
      console.error('❌ 生成每日报告失败:', error);
      throw error;
    }
  }

  /**
   * 发送每周报告
   */
  async sendWeeklyReport() {
    try {
      console.log('📊 开始生成每周报告...');
      
      const today = new Date();
      
      // 计算上周的开始和结束日期
      const lastWeekEnd = new Date(today);
      lastWeekEnd.setDate(today.getDate() - today.getDay()); // 上周日
      lastWeekEnd.setHours(23, 59, 59, 999);
      
      const lastWeekStart = new Date(lastWeekEnd);
      lastWeekStart.setDate(lastWeekEnd.getDate() - 6); // 上周一
      lastWeekStart.setHours(0, 0, 0, 0);
      
      const reportData = await this.generateWeeklyReportData(lastWeekStart, lastWeekEnd);
      
      await feishuBotService.sendWeeklyReport(reportData);
      console.log('✅ 每周报告发送成功');
      
      return reportData;
    } catch (error) {
      console.error('❌ 生成每周报告失败:', error);
      throw error;
    }
  }

  /**
   * 发送月度报告
   */
  async sendMonthlyReport() {
    try {
      console.log('📊 开始生成月度报告...');
      
      const today = new Date();
      
      // 计算上个月的开始和结束日期
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      lastMonthEnd.setHours(23, 59, 59, 999);
      
      const reportData = await this.generateMonthlyReportData(lastMonth, lastMonthEnd);
      
      await feishuBotService.sendMonthlyReport(reportData);
      console.log('✅ 月度报告发送成功');
      
      return reportData;
    } catch (error) {
      console.error('❌ 生成月度报告失败:', error);
      throw error;
    }
  }

  /**
   * 生成每日报告数据
   */
  async generateDailyReportData(startDate, endDate) {
    // 获取当天的日期信息
    const dateStatus = holidayService.getDateStatus(startDate);
    
    // 查询当日订单
    const exchanges = await Exchange.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        },
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      },
      include: [
        {
          model: Product,
          attributes: ['name', 'lyPrice', 'rmbPrice']
        },
        {
          model: User,
          attributes: ['department', 'departmentPath']
        }
      ]
    });

    // 统计数据
    const totalOrders = exchanges.length;
    const lyOrders = exchanges.filter(e => e.paymentMethod === 'ly').length;
    const rmbOrders = exchanges.filter(e => e.paymentMethod === 'rmb').length;
    
    let lyAmount = 0;
    let rmbAmount = 0;
    let totalQuantity = 0;
    
    const productStats = {};
    const departmentStats = {};
    const uniqueUserIds = new Set();
    
    exchanges.forEach(exchange => {
      uniqueUserIds.add(exchange.userId);
      totalQuantity += exchange.quantity;
      
      // 计算收入
      if (exchange.paymentMethod === 'ly') {
        lyAmount += exchange.Product.lyPrice * exchange.quantity;
      } else {
        rmbAmount += exchange.Product.rmbPrice * exchange.quantity;
      }
      
      // 统计商品
      const productName = exchange.Product.name;
      if (!productStats[productName]) {
        productStats[productName] = 0;
      }
      productStats[productName] += exchange.quantity;
      
      // 统计部门
      const department = exchange.User.departmentPath || exchange.User.department || '未知部门';
      if (!departmentStats[department]) {
        departmentStats[department] = 0;
      }
      departmentStats[department] += exchange.quantity;
    });
    
    // 转换为排序数组
    const topProductsList = Object.entries(productStats)
      .map(([name, quantity]) => ({ name, quantity }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 3);
    
    const topDepartments = Object.entries(departmentStats)
      .map(([name, quantity]) => ({ name, quantity }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 3);

    return {
      date: startDate.toLocaleDateString('zh-CN'),
      weekday: dateStatus.weekday,
      totalOrders,
      lyOrders,
      rmbOrders,
      lyAmount,
      rmbAmount,
      totalQuantity,
      uniqueUsers: uniqueUserIds.size,
      topProductsList,
      topDepartments
    };
  }

  /**
   * 生成每周报告数据
   */
  async generateWeeklyReportData(startDate, endDate) {
    // 查询周期内订单
    const exchanges = await Exchange.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        },
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      },
      include: [
        {
          model: Product,
          attributes: ['name', 'lyPrice', 'rmbPrice']
        },
        {
          model: User,
          attributes: ['department', 'departmentPath']
        }
      ]
    });

    // 计算工作日数量
    const workDays = holidayService.getWorkingDaysInRange(startDate, endDate);

    // 基础统计
    const totalOrders = exchanges.length;
    const lyOrders = exchanges.filter(e => e.paymentMethod === 'ly').length;
    const rmbOrders = exchanges.filter(e => e.paymentMethod === 'rmb').length;
    
    let lyAmount = 0;
    let rmbAmount = 0;
    let totalQuantity = 0;
    
    const productStats = {};
    const departmentStats = {};
    const uniqueUserIds = new Set();
    
    exchanges.forEach(exchange => {
      uniqueUserIds.add(exchange.userId);
      totalQuantity += exchange.quantity;
      
      if (exchange.paymentMethod === 'ly') {
        lyAmount += exchange.Product.lyPrice * exchange.quantity;
      } else {
        rmbAmount += exchange.Product.rmbPrice * exchange.quantity;
      }
      
      const productName = exchange.Product.name;
      if (!productStats[productName]) {
        productStats[productName] = 0;
      }
      productStats[productName] += exchange.quantity;
      
      const department = exchange.User.departmentPath || exchange.User.department || '未知部门';
      if (!departmentStats[department]) {
        departmentStats[department] = 0;
      }
      departmentStats[department] += exchange.quantity;
    });

    // 计算环比增长率（与前一周比较）
    const prevWeekStart = new Date(startDate);
    prevWeekStart.setDate(startDate.getDate() - 7);
    const prevWeekEnd = new Date(endDate);
    prevWeekEnd.setDate(endDate.getDate() - 7);
    
    const prevWeekOrders = await Exchange.count({
      where: {
        createdAt: {
          [Op.between]: [prevWeekStart, prevWeekEnd]
        },
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      }
    });

    const growthRate = prevWeekOrders > 0 
      ? Math.round(((totalOrders - prevWeekOrders) / prevWeekOrders) * 100)
      : 0;

    // 转换排序数组
    const topProductsList = Object.entries(productStats)
      .map(([name, quantity]) => ({ name, quantity }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
    
    const topDepartments = Object.entries(departmentStats)
      .map(([name, quantity]) => ({ name, quantity }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);

    // 计算周数
    const weekNumber = Math.ceil((endDate.getTime() - new Date(endDate.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000));

    return {
      weekStart: startDate.toLocaleDateString('zh-CN'),
      weekEnd: endDate.toLocaleDateString('zh-CN'),
      weekNumber,
      totalOrders,
      lyOrders,
      rmbOrders,
      lyAmount,
      rmbAmount,
      totalQuantity,
      uniqueUsers: uniqueUserIds.size,
      dailyAverage: workDays > 0 ? Math.round(totalOrders / workDays * 10) / 10 : 0,
      workDays,
      topProductsList,
      topDepartments,
      growthRate
    };
  }

  /**
   * 生成月度报告数据
   */
  async generateMonthlyReportData(startDate, endDate) {
    const year = startDate.getFullYear();
    const month = startDate.getMonth() + 1;

    // 查询月度订单
    const exchanges = await Exchange.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        },
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      },
      include: [
        {
          model: Product,
          attributes: ['name', 'lyPrice', 'rmbPrice']
        },
        {
          model: User,
          attributes: ['username', 'department', 'departmentPath']
        }
      ]
    });

    // 计算工作日数量
    const workDays = holidayService.getWorkingDaysInMonth(year, month);

    // 基础统计
    const totalOrders = exchanges.length;
    const lyOrders = exchanges.filter(e => e.paymentMethod === 'ly').length;
    const rmbOrders = exchanges.filter(e => e.paymentMethod === 'rmb').length;
    
    let lyAmount = 0;
    let rmbAmount = 0;
    let totalQuantity = 0;
    
    const productStats = {};
    const departmentStats = {};
    const userStats = {};
    const uniqueUserIds = new Set();
    const dailyStats = {};
    
    exchanges.forEach(exchange => {
      uniqueUserIds.add(exchange.userId);
      totalQuantity += exchange.quantity;
      
      if (exchange.paymentMethod === 'ly') {
        lyAmount += exchange.Product.lyPrice * exchange.quantity;
      } else {
        rmbAmount += exchange.Product.rmbPrice * exchange.quantity;
      }
      
      // 商品统计
      const productName = exchange.Product.name;
      if (!productStats[productName]) {
        productStats[productName] = 0;
      }
      productStats[productName] += exchange.quantity;
      
      // 部门统计
      const department = exchange.User.departmentPath || exchange.User.department || '未知部门';
      if (!departmentStats[department]) {
        departmentStats[department] = 0;
      }
      departmentStats[department] += exchange.quantity;
      
      // 用户统计
      const userName = exchange.User.username;
      if (!userStats[userName]) {
        userStats[userName] = 0;
      }
      userStats[userName] += 1;
      
      // 每日统计
      const orderDate = exchange.createdAt.toLocaleDateString('zh-CN');
      if (!dailyStats[orderDate]) {
        dailyStats[orderDate] = 0;
      }
      dailyStats[orderDate] += 1;
    });

    // 计算环比增长率（与上个月比较）
    const prevMonthStart = new Date(year, month - 2, 1);
    const prevMonthEnd = new Date(year, month - 1, 0);
    
    const prevMonthOrders = await Exchange.count({
      where: {
        createdAt: {
          [Op.between]: [prevMonthStart, prevMonthEnd]
        },
        status: {
          [Op.in]: ['pending', 'approved', 'shipped', 'completed']
        }
      }
    });

    const growthRate = prevMonthOrders > 0 
      ? Math.round(((totalOrders - prevMonthOrders) / prevMonthOrders) * 100)
      : 0;

    // 计算转化率（使用总用户数作为基数）
    const totalUsers = await User.count();
    const conversionRate = totalUsers > 0 
      ? Math.round((uniqueUserIds.size / totalUsers) * 100)
      : 0;

    // 找出峰值日期
    const peakDay = Object.entries(dailyStats)
      .reduce((max, [date, orders]) => 
        orders > max.orders ? { date, orders } : max, 
        { date: '', orders: 0 }
      );

    // 生成数据洞察
    const insights = this.generateMonthlyInsights({
      totalOrders,
      avgDailyOrders: workDays > 0 ? Math.round(totalOrders / workDays * 10) / 10 : 0,
      conversionRate,
      growthRate,
      lyPercentage: totalOrders > 0 ? Math.round((lyOrders / totalOrders) * 100) : 0,
      peakDay
    });

    // 转换排序数组
    const topProductsList = Object.entries(productStats)
      .map(([name, quantity]) => ({ name, quantity }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
    
    const topDepartments = Object.entries(departmentStats)
      .map(([name, quantity]) => ({ name, quantity }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);

    const topUsers = Object.entries(userStats)
      .map(([name, orders]) => ({ name, orders }))
      .sort((a, b) => b.orders - a.orders)
      .slice(0, 5);

    return {
      month,
      year,
      totalOrders,
      lyOrders,
      rmbOrders,
      lyAmount,
      rmbAmount,
      totalQuantity,
      uniqueUsers: uniqueUserIds.size,
      avgDailyOrders: workDays > 0 ? Math.round(totalOrders / workDays * 10) / 10 : 0,
      workDays,
      topProductsList,
      topDepartments,
      topUsers,
      growthRate,
      conversionRate,
      peakDay,
      insights
    };
  }

  /**
   * 生成月度数据洞察
   */
  generateMonthlyInsights(data) {
    const insights = [];
    
    if (data.growthRate > 20) {
      insights.push(`订单量环比增长${data.growthRate}%，增长势头强劲`);
    } else if (data.growthRate < -10) {
      insights.push(`订单量环比下降${Math.abs(data.growthRate)}%，需要关注`);
    }
    
    if (data.conversionRate > 50) {
      insights.push(`用户转化率达到${data.conversionRate}%，用户活跃度很高`);
    } else if (data.conversionRate < 20) {
      insights.push(`用户转化率仅${data.conversionRate}%，可优化推广策略`);
    }
    
    if (data.lyPercentage > 80) {
      insights.push(`光年币支付占比${data.lyPercentage}%，内部积分使用积极`);
    } else if (data.lyPercentage < 30) {
      insights.push(`人民币支付占比较高，可推广光年币使用`);
    }
    
    if (data.avgDailyOrders > 10) {
      insights.push(`日均订单${data.avgDailyOrders}单，业务活跃度良好`);
    }
    
    return insights;
  }

  /**
   * 手动触发每日报告（用于测试）
   */
  async triggerDailyReport() {
    console.log('🧪 手动触发今日销售汇总报告...');
    return await this.sendDailyReport();
  }

  /**
   * 手动触发每周报告（用于测试）
   */
  async triggerWeeklyReport() {
    console.log('🧪 手动触发每周报告...');
    return await this.sendWeeklyReport();
  }

  /**
   * 手动触发月度报告（用于测试）
   */
  async triggerMonthlyReport() {
    console.log('🧪 手动触发月度报告...');
    return await this.sendMonthlyReport();
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    const status = {
      isInitialized: this.isInitialized,
      jobs: {}
    };

    this.jobs.forEach((job, name) => {
      status.jobs[name] = {
        running: job.running
      };
    });

    return status;
  }
}

module.exports = new ScheduledReportService(); 
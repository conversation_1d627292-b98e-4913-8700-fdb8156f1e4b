const NotificationDiagnostic = require('../models/notificationDiagnostic');
const axios = require('axios');
const { Op } = require('sequelize');
const os = require('os');

/**
 * 通知诊断服务
 * 用于诊断和监控飞书通知发送状态
 */
class NotificationDiagnosticService {
  /**
   * 创建诊断记录
   * @param {Object} diagnosticData - 诊断数据
   * @returns {Promise<Object>} - 创建的诊断记录
   * @private
   */
  async _createDiagnostic(diagnosticData) {
    return await NotificationDiagnostic.create({
      ...diagnosticData,
      status: diagnosticData.status || 'pending'
    });
  }

  /**
   * 更新诊断记录
   * @param {number} id - 诊断ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} - 更新后的诊断记录
   * @private
   */
  async _updateDiagnostic(id, updateData) {
    const diagnostic = await NotificationDiagnostic.findByPk(id);
    if (!diagnostic) {
      throw new Error('诊断记录不存在');
    }
    
    await diagnostic.update(updateData);
    return diagnostic;
  }

  /**
   * 获取诊断记录列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 诊断记录列表
   */
  async getDiagnosticResults(options = {}) {
    const {
      page = 1,
      pageSize = 20,
      diagnosticType,
      status,
      startDate,
      endDate,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = options;

    const where = {};
    
    if (diagnosticType) {
      where.diagnosticType = diagnosticType;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (startDate || endDate) {
      where.createdAt = {};
      
      if (startDate) {
        where.createdAt[Op.gte] = new Date(startDate);
      }
      
      if (endDate) {
        where.createdAt[Op.lte] = new Date(endDate);
      }
    }

    const { count, rows } = await NotificationDiagnostic.findAndCountAll({
      where,
      order: [[sortBy, sortOrder]],
      limit: pageSize,
      offset: (page - 1) * pageSize
    });

    return {
      total: count,
      page,
      pageSize,
      data: rows
    };
  }

  /**
   * 解决诊断问题
   * @param {number} id - 诊断ID
   * @param {string} resolution - 解决方案
   * @returns {Promise<Object>} - 更新后的诊断记录
   */
  async resolveIssue(id, resolution) {
    return await this._updateDiagnostic(id, {
      status: 'resolved',
      resolvedAt: new Date(),
      resolution
    });
  }

  /**
   * 测试Webhook连接
   * @param {string} webhookUrl - Webhook地址
   * @returns {Promise<Object>} - 测试结果
   */
  async testWebhookConnection(webhookUrl) {
    try {
      // 创建诊断记录
      const diagnostic = await this._createDiagnostic({
        diagnosticType: 'webhook_connection',
        status: 'pending',
        details: { webhookUrl }
      });
      
      // 构建测试消息
      const testMessage = {
        msg_type: "text",
        content: {
          text: "这是一条测试消息，请忽略。"
        }
      };
      
      // 发送测试请求
      let success = true;
      let errorMessage = null;
      
      try {
        const response = await axios.post(webhookUrl, testMessage, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 5000 // 5秒超时
        });
        
        if (response.status !== 200) {
          success = false;
          errorMessage = `HTTP状态码: ${response.status}`;
        }
      } catch (error) {
        success = false;
        errorMessage = error.message;
        
        if (error.response) {
          errorMessage += ` (HTTP状态码: ${error.response.status})`;
        }
      }
      
      // 更新诊断结果
      await this._updateDiagnostic(diagnostic.id, {
        status: success ? 'success' : 'error',
        details: {
          ...diagnostic.details,
          timestamp: new Date().toISOString()
        },
        errorMessage: errorMessage
      });
      
      // 返回诊断结果
      return {
        success,
        message: success ? '连接测试成功' : '连接测试失败',
        error: errorMessage,
        timestamp: new Date().toISOString(),
        diagnosticId: diagnostic.id
      };
    } catch (error) {
      console.error('Webhook连接测试失败:', error);
      throw error;
    }
  }

  /**
   * 获取系统健康状态
   * @returns {Promise<Object>} - 系统健康状态
   */
  async getSystemHealth() {
    try {
      // 创建诊断记录
      const diagnostic = await this._createDiagnostic({
        diagnosticType: 'system_health',
        status: 'pending'
      });
      
      // 收集系统信息
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const memoryUsage = Math.floor(100 * (totalMemory - freeMemory) / totalMemory);
      
      const cpus = os.cpus();
      const loadAvg = os.loadavg();
      
      const uptime = os.uptime();
      const uptimeHours = Math.floor(uptime / 3600);
      const uptimeMinutes = Math.floor((uptime % 3600) / 60);
      
      // 确定健康状态
      let status = 'normal';
      let issues = [];
      
      if (memoryUsage > 90) {
        status = 'warning';
        issues.push('内存使用率超过90%');
      }
      
      if (loadAvg[0] > cpus.length * 0.8) {
        status = 'warning';
        issues.push('CPU负载过高');
      }
      
      // 收集诊断数据
      const healthData = {
        memory: {
          total: totalMemory,
          free: freeMemory,
          usage: memoryUsage
        },
        cpu: {
          cores: cpus.length,
          loadAvg: loadAvg
        },
        uptime: {
          seconds: uptime,
          formatted: `${uptimeHours}小时${uptimeMinutes}分钟`
        },
        status,
        issues,
        timestamp: new Date().toISOString()
      };
      
      // 更新诊断记录
      await this._updateDiagnostic(diagnostic.id, {
        status: issues.length > 0 ? 'warning' : 'success',
        details: healthData
      });
      
      // 返回健康状态
      return {
        ...healthData,
        diagnosticId: diagnostic.id
      };
    } catch (error) {
      console.error('获取系统健康状态失败:', error);
      throw error;
    }
  }

  /**
   * 分析消息发送统计
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} - 分析结果
   */
  async analyzeMessageDelivery(options = {}) {
    try {
      const {
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 默认7天前
        endDate = new Date(),
        notificationType
      } = options;
      
      // 创建诊断记录
      const diagnostic = await this._createDiagnostic({
        diagnosticType: 'message_delivery',
        status: 'pending',
        details: { startDate, endDate, notificationType }
      });
      
      // 这里应该从消息发送日志表中查询数据
      // 由于目前没有消息发送日志表，以下为模拟数据
      const mockStats = {
        total: 150,
        success: 142,
        failed: 8,
        successRate: 94.67,
        avgResponseTime: 382, // ms
        byType: {
          system: { total: 25, success: 24, failed: 1 },
          exchange: { total: 85, success: 80, failed: 5 },
          user: { total: 40, success: 38, failed: 2 }
        },
        failureReasons: {
          'network_error': 3,
          'timeout': 2,
          'rate_limit': 2,
          'invalid_content': 1
        }
      };
      
      // 更新诊断记录
      await this._updateDiagnostic(diagnostic.id, {
        status: 'success',
        details: {
          ...diagnostic.details,
          stats: mockStats,
          timestamp: new Date().toISOString()
        }
      });
      
      // 返回分析结果
      return {
        ...mockStats,
        period: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        diagnosticId: diagnostic.id
      };
    } catch (error) {
      console.error('分析消息发送统计失败:', error);
      throw error;
    }
  }

  /**
   * 运行全面诊断
   * @returns {Promise<Object>} - 诊断结果
   */
  async runComprehensiveDiagnostic() {
    try {
      // 创建诊断记录
      const diagnostic = await this._createDiagnostic({
        diagnosticType: 'comprehensive',
        status: 'pending'
      });
      
      // 收集各项诊断结果
      const results = {
        system: await this.getSystemHealth(),
        // webhook连接测试需要webhook地址，这里暂不执行
        messageStats: await this.analyzeMessageDelivery(),
        timestamp: new Date().toISOString()
      };
      
      // 综合评估系统状态
      let overallStatus = 'normal';
      let recommendations = [];
      
      if (results.system.status === 'warning') {
        overallStatus = 'warning';
        recommendations.push('系统资源紧张，建议优化服务器配置');
      }
      
      if (results.messageStats.successRate < 95) {
        overallStatus = 'warning';
        recommendations.push('消息发送成功率低于95%，建议排查原因');
      }
      
      // 更新诊断记录
      await this._updateDiagnostic(diagnostic.id, {
        status: 'success',
        details: {
          results,
          overallStatus,
          recommendations
        }
      });
      
      // 返回诊断结果
      return {
        diagnosticId: diagnostic.id,
        timestamp: new Date().toISOString(),
        overallStatus,
        recommendations,
        details: results
      };
    } catch (error) {
      console.error('运行全面诊断失败:', error);
      throw error;
    }
  }
}

module.exports = new NotificationDiagnosticService(); 
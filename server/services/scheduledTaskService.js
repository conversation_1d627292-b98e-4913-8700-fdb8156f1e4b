const cron = require('node-cron');
const notificationLogService = require('./notificationLogService');

/**
 * 定时任务服务
 */
class ScheduledTaskService {
  constructor() {
    this.tasks = new Map();
  }

  /**
   * 启动所有定时任务
   */
  startAllTasks() {
    this.startRetryFailedNotifications();
    console.log('✅ 所有定时任务已启动');
  }

  /**
   * 启动失败通知重试任务
   * 每5分钟执行一次
   */
  startRetryFailedNotifications() {
    const task = cron.schedule('*/5 * * * *', async () => {
      try {
        console.log('🔄 开始执行失败通知重试任务...');
        const processedCount = await notificationLogService.processFailedRetries();
        if (processedCount > 0) {
          console.log(`✅ 失败通知重试任务完成，处理了 ${processedCount} 条通知`);
        }
      } catch (error) {
        console.error('❌ 失败通知重试任务执行失败:', error);
      }
    }, {
      scheduled: false
    });

    task.start();
    this.tasks.set('retryFailedNotifications', task);
    console.log('✅ 失败通知重试任务已启动 (每5分钟执行一次)');
  }

  /**
   * 停止所有定时任务
   */
  stopAllTasks() {
    this.tasks.forEach((task, name) => {
      task.destroy();
      console.log(`✅ 定时任务 ${name} 已停止`);
    });
    this.tasks.clear();
    console.log('✅ 所有定时任务已停止');
  }

  /**
   * 获取任务状态
   */
  getTaskStatus() {
    const status = {};
    this.tasks.forEach((task, name) => {
      status[name] = {
        running: task.running,
        scheduled: task.scheduled
      };
    });
    return status;
  }
}

module.exports = new ScheduledTaskService(); 
const { NotificationLog, NotificationConfig } = require('../models');
const { Op } = require('sequelize');
const axios = require('axios');

/**
 * 通知日志服务
 */
class NotificationLogService {
  constructor() {
    // 重试策略配置
    this.retryConfig = {
      baseDelay: 1000 * 60, // 基础延迟 1分钟
      maxDelay: 1000 * 60 * 30, // 最大延迟 30分钟
      backoffMultiplier: 2 // 指数退避倍数
    };
  }

  /**
   * 记录通知发送日志
   * @param {Object} logData - 日志数据
   * @returns {Promise<Object>} - 创建的日志记录
   */
  async createLog(logData) {
    try {
      const log = await NotificationLog.create({
        notificationType: logData.notificationType,
        webhookUrl: logData.webhookUrl,
        requestPayload: logData.requestPayload,
        triggerSource: logData.triggerSource || 'auto',
        createdBy: logData.createdBy,
        maxRetries: logData.maxRetries || 3
      });

      return log;
    } catch (error) {
      console.error('创建通知日志失败:', error);
      throw new Error('创建通知日志失败');
    }
  }

  /**
   * 更新日志发送结果
   * @param {number} logId - 日志ID
   * @param {Object} resultData - 结果数据
   */
  async updateLogResult(logId, resultData) {
    try {
      const updateData = {
        status: resultData.success ? 'success' : 'failed',
        responseStatus: resultData.responseStatus,
        responseBody: resultData.responseBody,
        errorMessage: resultData.errorMessage,
        sentAt: new Date(),
        responseTime: resultData.responseTime
      };

      // 如果发送失败，计算下次重试时间
      if (!resultData.success) {
        const log = await NotificationLog.findByPk(logId);
        if (log && log.retryCount < log.maxRetries) {
          const delay = this.calculateRetryDelay(log.retryCount);
          updateData.nextRetryAt = new Date(Date.now() + delay);
          updateData.retryCount = log.retryCount + 1;
          updateData.status = 'pending'; // 保持待重试状态
        }
      }

      await NotificationLog.update(updateData, {
        where: { id: logId }
      });

      console.log(`通知日志更新成功: ${logId}, 状态: ${updateData.status}`);
    } catch (error) {
      console.error('更新通知日志失败:', error);
    }
  }

  /**
   * 计算重试延迟时间
   * @param {number} retryCount - 当前重试次数
   * @returns {number} - 延迟时间(毫秒)
   */
  calculateRetryDelay(retryCount) {
    const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, retryCount);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  /**
   * 执行通知发送（带日志记录）
   * @param {Object} notificationData - 通知数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendWithLog(notificationData) {
    const { notificationType, webhookUrl, payload, triggerSource, createdBy, maxRetries } = notificationData;

    // 创建日志记录
    const log = await this.createLog({
      notificationType,
      webhookUrl,
      requestPayload: payload,
      triggerSource,
      createdBy,
      maxRetries
    });

    const startTime = Date.now();
    let result = {
      success: false,
      logId: log.id
    };

    try {
      // 发送请求
      const response = await axios.post(webhookUrl, payload, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      const responseTime = Date.now() - startTime;

      result = {
        success: true,
        logId: log.id,
        responseStatus: response.status,
        responseBody: response.data,
        responseTime
      };

      console.log(`通知发送成功: ${notificationType}, 耗时: ${responseTime}ms`);
    } catch (error) {
      const responseTime = Date.now() - startTime;

      result = {
        success: false,
        logId: log.id,
        responseStatus: error.response?.status,
        responseBody: error.response?.data,
        errorMessage: error.message,
        responseTime
      };

      console.error(`通知发送失败: ${notificationType}, 错误: ${error.message}`);
    }

    // 更新日志结果
    await this.updateLogResult(log.id, result);

    return result;
  }

  /**
   * 处理失败重试
   * @returns {Promise<number>} - 处理的重试数量
   */
  async processFailedRetries() {
    try {
      console.log('开始处理失败通知重试...');

      // 查找需要重试的通知
      const retryLogs = await NotificationLog.findAll({
        where: {
          status: 'pending',
          retryCount: {
            [Op.lt]: NotificationLog.sequelize.col('max_retries')
          },
          nextRetryAt: {
            [Op.lte]: new Date()
          }
        },
        limit: 50 // 限制批次处理数量
      });

      console.log(`找到 ${retryLogs.length} 条需要重试的通知`);

      let successCount = 0;
      for (const log of retryLogs) {
        try {
          const result = await this.retryNotification(log);
          if (result.success) {
            successCount++;
          }
        } catch (error) {
          console.error(`重试通知失败: ${log.id}`, error);
        }
      }

      console.log(`重试处理完成，成功: ${successCount}/${retryLogs.length}`);
      return retryLogs.length;
    } catch (error) {
      console.error('处理失败重试时出错:', error);
      return 0;
    }
  }

  /**
   * 重试单个通知
   * @param {Object} log - 日志记录
   * @returns {Promise<Object>} - 重试结果
   */
  async retryNotification(log) {
    const startTime = Date.now();
    let result = {
      success: false,
      logId: log.id
    };

    try {
      // 重新发送请求
      const response = await axios.post(log.webhookUrl, log.requestPayload, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      const responseTime = Date.now() - startTime;

      result = {
        success: true,
        logId: log.id,
        responseStatus: response.status,
        responseBody: response.data,
        responseTime
      };

      console.log(`通知重试成功: ${log.notificationType}, 重试次数: ${log.retryCount + 1}`);
    } catch (error) {
      const responseTime = Date.now() - startTime;

      result = {
        success: false,
        logId: log.id,
        responseStatus: error.response?.status,
        responseBody: error.response?.data,
        errorMessage: error.message,
        responseTime
      };

      console.error(`通知重试失败: ${log.notificationType}, 重试次数: ${log.retryCount + 1}, 错误: ${error.message}`);
    }

    // 更新日志结果
    await this.updateLogResult(log.id, result);

    return result;
  }

  /**
   * 获取发送历史记录
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 查询结果
   */
  async getNotificationHistory(options = {}) {
    try {
      const {
        page = 1,
        pageSize = 20,
        notificationType,
        status,
        triggerSource,
        startDate,
        endDate
      } = options;

      const where = {};

      if (notificationType) {
        where.notificationType = notificationType;
      }

      if (status) {
        where.status = status;
      }

      if (triggerSource) {
        where.triggerSource = triggerSource;
      }

      if (startDate || endDate) {
        where.created_at = {};
        if (startDate) {
          where.created_at[Op.gte] = new Date(startDate);
        }
        if (endDate) {
          where.created_at[Op.lte] = new Date(endDate);
        }
      }

      const { rows: logs, count: total } = await NotificationLog.findAndCountAll({
        where,
        order: [['created_at', 'DESC']],
        limit: pageSize,
        offset: (page - 1) * pageSize
      });

      return {
        logs: logs.map(log => ({
          id: log.id,
          notificationType: log.notificationType,
          status: log.status,
          triggerSource: log.triggerSource,
          retryCount: log.retryCount,
          maxRetries: log.maxRetries,
          responseStatus: log.responseStatus,
          responseTime: log.responseTime,
          errorMessage: log.errorMessage,
          sentAt: log.sentAt,
          createdAt: log.createdAt
        })),
        pagination: {
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      console.error('获取通知历史失败:', error);
      throw new Error('获取通知历史失败');
    }
  }

  /**
   * 获取通知统计数据
   * @param {Object} options - 统计选项
   * @returns {Promise<Object>} - 统计结果
   */
  async getNotificationStats(options = {}) {
    try {
      const { startDate, endDate, period = 'day' } = options;

      const where = {};
      if (startDate || endDate) {
        where.created_at = {};
        if (startDate) {
          where.created_at[Op.gte] = new Date(startDate);
        }
        if (endDate) {
          where.created_at[Op.lte] = new Date(endDate);
        }
      }

      // 总体统计
      const totalStats = await NotificationLog.findAll({
        where,
        attributes: [
          'status',
          [NotificationLog.sequelize.fn('COUNT', '*'), 'count']
        ],
        group: ['status'],
        raw: true
      });

      // 按类型统计
      const typeStats = await NotificationLog.findAll({
        where,
        attributes: [
          'notificationType',
          'status',
          [NotificationLog.sequelize.fn('COUNT', '*'), 'count'],
          [NotificationLog.sequelize.fn('AVG', NotificationLog.sequelize.col('response_time')), 'avgResponseTime']
        ],
        group: ['notificationType', 'status'],
        raw: true
      });

      // 按时间统计（最近7天的每日统计）
      const timeStats = await this.getTimeSeriesStats(where, period);

      // 重试统计
      const retryStats = await NotificationLog.findAll({
        where: {
          ...where,
          retryCount: { [Op.gt]: 0 }
        },
        attributes: [
          [NotificationLog.sequelize.fn('COUNT', '*'), 'totalRetries'],
          [NotificationLog.sequelize.fn('AVG', NotificationLog.sequelize.col('retry_count')), 'avgRetries'],
          [NotificationLog.sequelize.fn('MAX', NotificationLog.sequelize.col('retry_count')), 'maxRetries']
        ],
        raw: true
      });

      return {
        totalStats: this.formatTotalStats(totalStats),
        typeStats: this.formatTypeStats(typeStats),
        timeStats,
        retryStats: retryStats[0] || { totalRetries: 0, avgRetries: 0, maxRetries: 0 }
      };
    } catch (error) {
      console.error('获取通知统计失败:', error);
      throw new Error('获取通知统计失败');
    }
  }

  /**
   * 获取时间序列统计
   */
  async getTimeSeriesStats(where, period) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 7); // 最近7天

    const timeWhere = {
      ...where,
      created_at: {
        [Op.gte]: startDate,
        [Op.lte]: endDate
      }
    };

    const stats = await NotificationLog.findAll({
      where: timeWhere,
      attributes: [
        [NotificationLog.sequelize.fn('DATE', NotificationLog.sequelize.col('created_at')), 'date'],
        'status',
        [NotificationLog.sequelize.fn('COUNT', '*'), 'count']
      ],
      group: [
        NotificationLog.sequelize.fn('DATE', NotificationLog.sequelize.col('created_at')),
        'status'
      ],
      order: [[NotificationLog.sequelize.fn('DATE', NotificationLog.sequelize.col('created_at')), 'ASC']],
      raw: true
    });

    return this.formatTimeStats(stats);
  }

  /**
   * 格式化总体统计数据
   */
  formatTotalStats(stats) {
    const result = {
      total: 0,
      success: 0,
      failed: 0,
      pending: 0,
      successRate: 0
    };

    stats.forEach(stat => {
      const count = parseInt(stat.count);
      result.total += count;
      result[stat.status] = count;
    });

    if (result.total > 0) {
      result.successRate = Math.round((result.success / result.total) * 100);
    }

    return result;
  }

  /**
   * 格式化类型统计数据
   */
  formatTypeStats(stats) {
    const typeMap = {};

    stats.forEach(stat => {
      const type = stat.notificationType;
      if (!typeMap[type]) {
        typeMap[type] = {
          notificationType: type,
          total: 0,
          success: 0,
          failed: 0,
          pending: 0,
          successRate: 0,
          avgResponseTime: 0
        };
      }

      const count = parseInt(stat.count);
      typeMap[type].total += count;
      typeMap[type][stat.status] = count;

      if (stat.avgResponseTime) {
        typeMap[type].avgResponseTime = Math.round(stat.avgResponseTime);
      }
    });

    // 计算成功率
    Object.values(typeMap).forEach(type => {
      if (type.total > 0) {
        type.successRate = Math.round((type.success / type.total) * 100);
      }
    });

    return Object.values(typeMap);
  }

  /**
   * 格式化时间统计数据
   */
  formatTimeStats(stats) {
    const dateMap = {};

    // 初始化最近7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      dateMap[dateStr] = {
        date: dateStr,
        total: 0,
        success: 0,
        failed: 0,
        pending: 0
      };
    }

    // 填充实际数据
    stats.forEach(stat => {
      const date = stat.date;
      if (dateMap[date]) {
        const count = parseInt(stat.count);
        dateMap[date].total += count;
        dateMap[date][stat.status] = count;
      }
    });

    return Object.values(dateMap);
  }

  /**
   * 手动重试失败的通知
   * @param {number} logId - 日志ID
   * @returns {Promise<Object>} - 重试结果
   */
  async manualRetry(logId) {
    try {
      const log = await NotificationLog.findByPk(logId);
      if (!log) {
        throw new Error('通知日志不存在');
      }

      if (log.status === 'success') {
        throw new Error('该通知已发送成功，无需重试');
      }

      // 重置重试计数，强制重试
      await log.update({
        nextRetryAt: new Date(),
        retryCount: Math.max(0, log.retryCount - 1) // 减少重试计数以允许重试
      });

      return await this.retryNotification(log);
    } catch (error) {
      console.error('手动重试失败:', error);
      throw error;
    }
  }
}

module.exports = new NotificationLogService(); 
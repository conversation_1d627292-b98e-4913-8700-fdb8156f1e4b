const { User, Product } = require('../models');
const { createNotification } = require('../controllers/notificationController');
const feishuBotService = require('./feishuBotService');

// 库存阈值常量
const STOCK_THRESHOLD = 5;

/**
 * 检查商品库存并发送告警通知
 * @param {Object} product - 商品对象
 * @returns {Promise<void>}
 */
exports.checkProductStock = async (product) => {
  try {
    // 如果商品库存低于阈值且状态为活跃，发送告警
    if (product.stock < STOCK_THRESHOLD && product.status === 'active') {
      console.log(`检测到商品"${product.name}"(ID:${product.id})库存不足，当前库存: ${product.stock}`);
      
      // 获取所有管理员
      const admins = await User.findAll({
        where: { role: 'admin' }
      });
      
      if (!admins || admins.length === 0) {
        console.log('未找到管理员用户，无法发送库存告警');
        return;
      }
      
      // 为每个管理员创建通知
      const notificationPromises = admins.map(admin => 
        createNotification({
          type: 'stock_alert',
          sourceId: product.id,
          title: '商品库存不足',
          content: `商品"${product.name}"库存不足，当前剩余${product.stock}个，请及时补货。`,
          recipientId: admin.id
        })
      );
      
      await Promise.all(notificationPromises);
      console.log(`已为商品"${product.name}"(ID:${product.id})发送库存告警通知给${admins.length}位管理员`);
      
      // 发送飞书群通知
      try {
        console.log('库存告警: 发送飞书群通知...');
        await feishuBotService.sendStockAlert(product, STOCK_THRESHOLD);
        console.log('库存告警: 飞书群通知发送成功');
      } catch (feishuError) {
        console.error('库存告警: 飞书群通知发送失败，但不影响主流程:', feishuError.message);
        // 不阻止主流程
      }
    }
  } catch (error) {
    console.error('库存告警通知发送失败:', error);
  }
}; 
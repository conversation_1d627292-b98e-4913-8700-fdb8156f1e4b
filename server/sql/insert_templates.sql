-- 插入订单确认通知模板
INSERT INTO feishu_mall.message_templates (
  template_name, 
  template_code,
  notification_type, 
  template_type,
  template_content, 
  variables, 
  description,
  is_default,
  is_active,
  usage_count,
  created_at, 
  updated_at
) VALUES (
  '订单确认通知', 
  'order_confirmed',
  'exchange', 
  'card',
  '{
    "msg_type": "interactive",
    "card": {
      "elements": [
        {
          "tag": "div",
          "text": {
            "content": "🎉 **订单确认通知**\\n\\n📋 **订单编号**：{{orderNumber}}\\n🛒 **商品名称**：{{productName}}\\n📦 **数量**：{{quantity}}\\n💰 **总金额**：{{totalAmount}}{{currency}}\\n🕒 **下单时间**：{{orderTime}}\\n📍 **兑换地点**：{{location}}\\n\\n{{#if remarks}}📝 **备注**：{{remarks}}{{/if}}\\n\\n感谢您的订单！我们会尽快处理。",
            "tag": "lark_md"
          }
        }
      ],
      "header": {
        "title": {
          "content": "📦 订单确认 - {{productName}}",
          "tag": "plain_text"
        },
        "template": "blue"
      }
    }
  }',
  '["orderNumber", "productName", "quantity", "totalAmount", "currency", "orderTime", "location", "remarks"]',
  '订单确认通知，发送给用户确认订单已收到',
  1,
  1,
  0,
  NOW(),
  NOW()
);

-- 插入新用户欢迎模板
INSERT INTO feishu_mall.message_templates (
  template_name, 
  template_code,
  notification_type, 
  template_type,
  template_content, 
  variables, 
  description,
  is_default,
  is_active,
  usage_count,
  created_at, 
  updated_at
) VALUES (
  '新用户欢迎', 
  'new_user_welcome',
  'user', 
  'card',
  '{
    "msg_type": "interactive",
    "card": {
      "elements": [
        {
          "tag": "div",
          "text": {
            "content": "👋 **欢迎加入光年小卖部**\\n\\n🙋 **用户名**：{{username}}\\n🏢 **部门**：{{department}}\\n📱 **联系方式**：{{contactInfo}}\\n📅 **注册时间**：{{registerTime}}\\n\\n🎁 感谢您注册光年小卖部！\\n\\n快去查看有哪些好物可以兑换吧~",
            "tag": "lark_md"
          }
        }
      ],
      "header": {
        "title": {
          "content": "🎉 欢迎新用户 - {{username}}",
          "tag": "plain_text"
        },
        "template": "green"
      }
    }
  }',
  '["username", "department", "contactInfo", "registerTime"]',
  '新用户注册时发送的欢迎消息',
  1,
  1,
  0,
  NOW(),
  NOW()
);

-- 插入系统维护通知模板
INSERT INTO feishu_mall.message_templates (
  template_name, 
  template_code,
  notification_type, 
  template_type,
  template_content, 
  variables, 
  description,
  is_default,
  is_active,
  usage_count,
  created_at, 
  updated_at
) VALUES (
  '系统维护通知', 
  'system_maintenance',
  'system', 
  'card',
  '{
    "msg_type": "interactive",
    "card": {
      "elements": [
        {
          "tag": "div",
          "text": {
            "content": "🔧 **系统维护通知**\\n\\n📋 **维护主题**：{{title}}\\n⏰ **维护时间**：{{startTime}} - {{endTime}}\\n⏱️ **预计耗时**：{{duration}}\\n🔍 **维护原因**：{{reason}}\\n\\n⚠️ **影响范围**\\n{{impactText}}\\n\\n📋 **准备事项**\\n{{preparationsText}}\\n\\n📞 **技术支持**：{{contactInfo}}\\n\\n💡 **温馨提示**：请提前做好相关准备，维护期间可能影响系统正常使用，感谢您的理解与配合！",
            "tag": "lark_md"
          }
        }
      ],
      "header": {
        "title": {
          "content": "🔧 光年小卖部 - 系统维护通知",
          "tag": "plain_text"
        },
        "template": "{{templateColor}}"
      }
    }
  }',
  '["title", "startTime", "endTime", "duration", "reason", "impactText", "preparationsText", "contactInfo", "templateColor"]',
  '系统维护通知，用于发布系统维护计划',
  1,
  1,
  0,
  NOW(),
  NOW()
); 
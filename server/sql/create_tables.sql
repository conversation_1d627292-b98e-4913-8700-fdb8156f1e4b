-- 创建发送时间调度表
CREATE TABLE IF NOT EXISTS feishu_mall.sending_schedules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  schedule_name VARCHAR(100) NOT NULL COMMENT '调度名称',
  notification_type VARCHAR(50) NOT NULL COMMENT '关联的通知类型',
  schedule_type ENUM('fixed', 'smart', 'conditional') DEFAULT 'fixed' COMMENT '调度类型',
  cron_expression VARCHAR(100) NULL COMMENT 'Cron表达式',
  time_windows JSON NULL COMMENT '时间窗口配置',
  conditions JSON NULL COMMENT '触发条件',
  priority INT DEFAULT 0 COMMENT '优先级',
  enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY unique_notification_schedule (notification_type, schedule_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发送时间调度表';

-- 创建通知诊断信息表
CREATE TABLE IF NOT EXISTS feishu_mall.notification_diagnostics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  diagnostic_type VARCHAR(50) NOT NULL COMMENT '诊断类型',
  status VARCHAR(20) NOT NULL COMMENT '状态',
  details JSON NULL COMMENT '详细信息',
  error_message TEXT NULL COMMENT '错误信息',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  resolved_at DATETIME NULL COMMENT '解决时间',
  resolution TEXT NULL COMMENT '解决方案',
  INDEX idx_diagnostic_status (diagnostic_type, status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知诊断信息表';

-- 更新通知配置表，添加新字段
ALTER TABLE feishu_mall.notification_configs 
ADD COLUMN template_id INT NULL COMMENT '关联的模板ID' AFTER retry_count,
ADD COLUMN schedule_id INT NULL COMMENT '关联的调度ID' AFTER template_id,
ADD COLUMN advanced_settings JSON NULL COMMENT '高级设置' AFTER schedule_id; 
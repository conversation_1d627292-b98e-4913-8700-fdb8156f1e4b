-- 插入工作日订单通知调度
INSERT INTO feishu_mall.sending_schedules (
  schedule_name,
  notification_type,
  schedule_type,
  cron_expression,
  time_windows,
  conditions,
  priority,
  enabled,
  created_at,
  updated_at
) VALUES (
  '工作日订单通知',
  'exchange',
  'smart',
  NULL,
  '{
    "workDays": true,
    "weekends": false,
    "holidays": false,
    "timeRanges": [
      { "start": "09:00", "end": "12:00", "priority": 3 },
      { "start": "14:00", "end": "17:30", "priority": 2 }
    ]
  }',
  '{
    "systemLoad": { "maxCpuPercent": 70, "maxMemoryPercent": 80 },
    "messageCount": { "maxBatchSize": 20, "delayBetweenBatches": 300 }
  }',
  10,
  1,
  NOW(),
  NOW()
);

-- 插入每日运营报告调度
INSERT INTO feishu_mall.sending_schedules (
  schedule_name,
  notification_type,
  schedule_type,
  cron_expression,
  time_windows,
  conditions,
  priority,
  enabled,
  created_at,
  updated_at
) VALUES (
  '每日运营报告',
  'daily_report',
  'fixed',
  '0 19 * * 1-5',
  NULL,
  NULL,
  5,
  1,
  NOW(),
  NOW()
);

-- 插入系统通知智能发送调度
INSERT INTO feishu_mall.sending_schedules (
  schedule_name,
  notification_type,
  schedule_type,
  cron_expression,
  time_windows,
  conditions,
  priority,
  enabled,
  created_at,
  updated_at
) VALUES (
  '系统通知智能发送',
  'system',
  'conditional',
  NULL,
  '{
    "workDays": true,
    "weekends": true,
    "holidays": false,
    "timeRanges": [
      { "start": "10:00", "end": "16:00", "priority": 1 }
    ]
  }',
  '{
    "userActivity": { "minActiveUsers": 5, "considerTimeWindow": true },
    "urgency": { "highPriority": "immediate", "normalPriority": "next_window", "lowPriority": "next_day" }
  }',
  20,
  1,
  NOW(),
  NOW()
); 
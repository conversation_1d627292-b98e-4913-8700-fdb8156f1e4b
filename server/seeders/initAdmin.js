/**
 * 初始化管理员账户的种子脚本
 */

const { User } = require('../models');
const { sequelize } = require('../config/database');

async function initAdminUser() {
  try {
    // 确保数据库连接正常
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 检查管理员用户是否已存在
    const existingAdmin = await User.findOne({ where: { username: 'admin' } });
    
    if (existingAdmin) {
      console.log('管理员用户已存在，无需创建');
      return;
    }

    // 创建管理员用户
    const admin = await User.create({
      username: 'admin',
      password: 'admin123',  // 密码将通过模型的hooks自动加密
      role: 'admin'
    });

    console.log('管理员用户创建成功:', admin.username);
  } catch (error) {
    console.error('初始化管理员账户时发生错误:', error);
  } finally {
    // 关闭数据库连接
    console.log('脚本执行完成');
  }
}

// 执行初始化
initAdminUser(); 
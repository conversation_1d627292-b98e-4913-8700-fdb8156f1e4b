# 订单编号格式升级指南

本文档介绍如何将系统升级为使用新的订单编号格式。新的订单编号格式为：

- 光年币支付：`GNB-YYYYMMDD001`（例如：GNB-20250516001）
- 人民币支付：`RMB-YYYYMMDD001`（例如：RMB-20250516001）

其中 YYYYMMDD 是订单创建日期，后面的数字是当天该支付方式的订单序号（从001开始）。

## 升级步骤

### 1. 备份数据库

在进行任何更改之前，请确保备份您的数据库：

```bash
# MySQL
mysqldump -u youruser -p exchange_mall > exchange_mall_backup.sql

# PostgreSQL
pg_dump -U youruser exchange_mall > exchange_mall_backup.sql
```

### 2. 更新代码

确保将所有相关代码文件更新到最新版本。

### 3. 运行数据库迁移

执行以下命令添加新的订单编号字段：

```bash
cd server
npm run migrate
```

### 4. 为历史订单生成订单编号

执行以下命令为已有的订单生成新的订单编号格式：

```bash
cd server
npm run generate-order-numbers
```

### 5. 验证升级

登录管理后台，确认订单管理页面中的订单ID现在使用新的格式显示。

### 6. 常见问题

#### Q: 如果脚本执行过程中出错怎么办？

A: 脚本执行会记录详细日志。如果发生错误，请查看日志确定问题原因，修复后可以重新运行生成脚本，它只会处理尚未有订单编号的记录。

#### Q: 新的订单编号格式是否影响旧的API？

A: 否，系统保留了原始的数字ID字段（在数据库中的`id`列）。API和后端操作依然使用这个字段作为主键，只有在展示给用户时才使用新的格式。

#### Q: 如何回滚更改？

A: 如果需要回滚更改，可以执行以下命令：

```bash
cd server
npm run migrate:undo
```

然后恢复之前备份的数据库。

### 7. 技术支持

如有任何问题，请联系技术支持团队。 
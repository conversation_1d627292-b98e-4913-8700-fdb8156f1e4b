const { Sequelize } = require('sequelize');
require('dotenv').config();

// 从环境变量读取数据库配置，如果没有则使用默认值
const DB_NAME = process.env.DB_NAME || 'feishu_mall';
const DB_USER = process.env.DB_USER || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || 'password';
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || 3306;

// 创建Sequelize实例 - 优化连接池配置
const sequelize = new Sequelize(DB_NAME, DB_USER, DB_PASSWORD, {
  host: DB_HOST,
  port: DB_PORT,
  dialect: 'mysql',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 10,        // 适度增加最大连接数（从5增加到10）
    min: 2,         // 设置最小连接数（从0增加到2）
    acquire: 60000, // 增加获取连接超时时间（从30秒增加到60秒）
    idle: 30000,    // 增加空闲超时时间（从10秒增加到30秒）
    evict: 5000,    // 添加连接回收间隔
    handleDisconnects: true // 自动处理断线重连
  },
  dialectOptions: {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci',
    // 连接超时设置
    connectTimeout: 60000,
    acquireTimeout: 60000,
    timeout: 60000
  },
  // 查询优化
  benchmark: process.env.NODE_ENV === 'development', // 只在开发环境启用基准测试
  // 重试配置
  retry: {
    max: 3
  },
  // 配置时区
  timezone: '+08:00'
});

// 测试数据库连接
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  }
};

module.exports = {
  sequelize,
  testConnection
};
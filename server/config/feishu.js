// 飞书登录相关配置
require('dotenv').config();

// 调试输出
console.log('=== 飞书配置加载 ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('FEISHU_REDIRECT_URI:', process.env.FEISHU_REDIRECT_URI);
console.log('process.env:', {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  FEISHU_APP_ID: process.env.FEISHU_APP_ID,
  FEISHU_APP_SECRET: process.env.FEISHU_APP_SECRET,
  FEISHU_REDIRECT_URI: process.env.FEISHU_REDIRECT_URI
});

// 根据环境确定重定向URI
const redirectUriByEnv = process.env.FEISHU_REDIRECT_URI || 'http://localhost:3000/api/feishu/callback';

console.log('计算得到的redirectUri:', redirectUriByEnv);

module.exports = {
  // 飞书应用凭证
  appId: process.env.FEISHU_APP_ID || 'cli_a66b3b2dcab8d013',
  appSecret: process.env.FEISHU_APP_SECRET || '5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r',
  
  // 飞书OAuth URL配置
  authorizeUrl: 'https://open.feishu.cn/open-apis/authen/v1/index',
  accessTokenUrl: 'https://open.feishu.cn/open-apis/authen/v1/access_token',
  userInfoUrl: 'https://open.feishu.cn/open-apis/authen/v1/user_info',
  
  // 部门和用户详情URL
  departmentInfoUrl: 'https://open.feishu.cn/open-apis/contact/v3/departments',
  userDetailUrl: 'https://open.feishu.cn/open-apis/contact/v3/users',
  
  // 回调地址，需要在飞书开放平台后台配置
  redirectUri: redirectUriByEnv,
  
  // 授权类型 - 网页应用
  grantType: 'authorization_code',

  // 该应用实际需要的权限列表（开发者需要在飞书开放平台申请这些权限）
  // 
  // === 核心权限（必需） ===
  // contact:user.base:readonly - 获取用户基本信息（姓名、头像等）
  // contact:user.email:readonly - 获取用户邮箱信息  
  // contact:user.department:readonly - 获取用户组织架构信息（部门ID）
  // contact:user.employee:readonly - 获取用户受雇信息（工号、职务、城市等）
  // contact:department.base:readonly - 获取部门基础信息（部门名称）
  // 
  // === 可选权限（推荐） ===
  // contact:user.phone:readonly - 获取用户手机号（目前缺失，导致无法获取手机号）
  // contact:department.organize:readonly - 获取部门组织架构信息（父部门等，用于构建完整部门路径）
  //
  // === 不需要的权限 ===
  // contact:user.employee_id:readonly - 获取用户employee_id（项目中未使用此字段）
  //
  // 权限申请后，需要提交版本发布并通过审核
}; 
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
// 确保尽早加载环境变量
require('dotenv').config();
const compressionMiddleware = require('./middlewares/compression'); // 添加压缩中间件

// 添加环境变量调试输出
console.log('====== 服务器启动环境变量 ======');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('SERVER_URL:', process.env.SERVER_URL);
console.log('FEISHU_REDIRECT_URI:', process.env.FEISHU_REDIRECT_URI);
console.log('====== 环境变量输出结束 ======');

const config = require('./config/config');
const { testConnection } = require('./config/database');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const fileUpload = require('express-fileupload');
const os = require('os');

// 初始化数据库
require('./config/database');

// 初始化定时统计报告服务
const scheduledReportService = require('./services/scheduledReportService');

// 创建Express应用
const app = express();

// 配置信任代理，以获取真实的客户端IP地址
app.set('trust proxy', true);

// 测试数据库连接
testConnection();

// 中间件
app.use(compressionMiddleware); // 添加响应压缩（放在最前面）
app.use(cors(config.cors)); // CORS配置
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('dev')); // 日志

// 添加文件上传中间件 - 仅用于支付凭证上传路径
// 注意：商品图片上传使用 multer 中间件，所以这里限制路径
const fileUploadMiddleware = fileUpload({
  createParentPath: true, // 自动创建上传目录
  limits: { 
    fileSize: config.upload.maxSize // 文件大小限制
  },
  abortOnLimit: true, // 超出大小限制时终止
  useTempFiles: true, // 使用临时文件
  tempFileDir: path.join(os.tmpdir(), 'uploads'), // 临时文件目录
  debug: process.env.NODE_ENV === 'development' // 开发模式下开启调试
});

// 仅为特定路径启用 express-fileupload 中间件，避免与 multer 冲突
app.use('/api/uploads', fileUploadMiddleware);
app.use('/api/upload/image', fileUploadMiddleware);
app.use('/api/users/import', fileUploadMiddleware); // 添加用户批量导入路径支持
app.use('/api/exchanges/import', fileUploadMiddleware); // 添加订单批量导入路径支持
// 将来可能需要 express-fileupload 的其他路径也在此添加

// 添加调试日志，记录文件上传请求
app.use((req, res, next) => {
  if (req.path === '/api/users/import' && req.method === 'POST') {
    console.log('收到用户批量导入请求:', {
      路径: req.path,
      请求方法: req.method,
      内容类型: req.get('Content-Type'),
      已处理文件: req.files ? '是' : '否'
    });
  }
  
  if (req.path === '/api/exchanges/import' && req.method === 'POST') {
    console.log('收到订单批量导入请求:', {
      路径: req.path,
      请求方法: req.method,
      内容类型: req.get('Content-Type'),
      已处理文件: req.files ? '是' : '否',
      文件详情: req.files ? Object.keys(req.files) : '无文件'
    });
  }
  
  next();
});

// 确保uploads目录存在
const uploadDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
  console.log('创建上传目录:', uploadDir);
} else {
  console.log('上传目录已存在:', uploadDir);
}

// 确保images子目录存在
const imagesDir = path.join(uploadDir, 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
  console.log('创建图片上传目录:', imagesDir);
} else {
  console.log('图片上传目录已存在:', imagesDir);
}

// 确保payment子目录存在
const paymentDir = path.join(uploadDir, 'payment');
if (!fs.existsSync(paymentDir)) {
  fs.mkdirSync(paymentDir, { recursive: true });
  console.log('创建支付码图片目录:', paymentDir);
} else {
  console.log('支付码图片目录已存在:', paymentDir);
}

// 添加权限检查，确保目录可写
try {
  fs.accessSync(imagesDir, fs.constants.W_OK);
  console.log('图片上传目录权限正常');
} catch (err) {
  console.error('图片上传目录权限错误:', err);
  // 尝试修复权限
  try {
    fs.chmodSync(imagesDir, 0o755);
    console.log('已尝试修复图片上传目录权限');
  } catch (chmodErr) {
    console.error('无法修复图片上传目录权限:', chmodErr);
  }
}

// 静态文件服务 - 确保以正确的方式提供静态文件
// 同时配置多种路径，确保能正确访问
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/api/uploads', express.static(path.join(__dirname, 'uploads')));
// 支付码静态文件路径
app.use('/uploads/payment', express.static(path.join(__dirname, 'uploads', 'payment')));
app.use('/api/uploads/payment', express.static(path.join(__dirname, 'uploads', 'payment')));

// 打印静态文件路径
console.log('静态文件路径1:', path.join(__dirname, 'uploads'));
console.log('静态文件路径2:', path.join(__dirname, 'uploads', 'images'));
console.log('支付码路径:', path.join(__dirname, 'uploads', 'payment'));

// 添加 /api/uploads 处理程序 (用于支付截图上传)

// 处理支付截图上传的路由
app.post('/api/uploads', (req, res) => {
  console.log('收到支付截图上传请求 - /api/uploads');
  console.log('请求头:', JSON.stringify(req.headers));
  console.log('请求体类型:', req.get('Content-Type'));
  console.log('请求来源:', req.get('Origin') || '未知');
  
  // CORS预检响应处理
  if (req.method === 'OPTIONS') {
    console.log('收到OPTIONS预检请求，返回204');
    return res.status(204).end();
  }
  
  // 检查是否有上传的文件
  if (!req.files || Object.keys(req.files).length === 0 || !req.files.file) {
    console.error('未接收到支付截图文件');
    return res.status(400).json({ 
      message: '没有上传文件',
      success: false,
      error: true
    });
  }
  
  try {
    const uploadedFile = req.files.file;
    
    // 检查文件类型
    if (!uploadedFile.mimetype.startsWith('image/')) {
      console.error('上传的文件不是图片:', uploadedFile.mimetype);
      return res.status(400).json({ 
        message: '只允许上传图片文件',
        success: false,
        error: true
      });
    }
    
    // 创建唯一文件名
    const uniqueFilename = `${uuidv4()}${path.extname(uploadedFile.name)}`;
    console.log('支付截图文件名:', uniqueFilename);
    
    // 设置保存路径
    const uploadDir = path.join(__dirname, 'uploads/images');
    const filePath = path.join(uploadDir, uniqueFilename);
    console.log('支付截图保存路径:', filePath);
    
    // 移动文件
    uploadedFile.mv(filePath, function(err) {
      if (err) {
        console.error('保存文件失败:', err);
        return res.status(500).json({
          message: '保存文件失败',
          success: false,
          error: true
        });
      }
      
      // 构建图片完整URL
      const baseUrl = process.env.SERVER_URL || `${req.protocol}://${req.get('host')}`;
      const imagePath = `/uploads/images/${uniqueFilename}`;
      const imageUrl = `${baseUrl}${imagePath}`;
      
      console.log('支付截图上传成功:', imageUrl);
      
      // 返回成功响应，同时支持多种格式以增强兼容性
      return res.status(200).json({ 
        imageUrl: imageUrl,
        url: imageUrl,
        errno: 0,
        success: true,
        data: {
          url: imageUrl,
          alt: uploadedFile.name || '支付凭证',
          href: imageUrl
        }
      });
    });
  } catch (error) {
    console.error('处理上传响应时出错:', error);
    return res.status(500).json({
      message: '服务器处理上传时出错',
      success: false,
      error: true
    });
  }
});

// 处理支付截图上传的路由（备用路径）
app.post('/api/upload/image', (req, res) => {
  console.log('收到支付截图上传请求（备用路径）');
  console.log('请求头:', JSON.stringify(req.headers));
  
  // 检查是否有上传的文件
  if (!req.files || Object.keys(req.files).length === 0 || !req.files.file) {
    console.error('未接收到支付截图文件');
    return res.status(400).json({ 
      message: '没有上传文件',
      success: false
    });
  }
  
  try {
    const uploadedFile = req.files.file;
    
    // 检查文件类型
    if (!uploadedFile.mimetype.startsWith('image/')) {
      console.error('上传的文件不是图片:', uploadedFile.mimetype);
      return res.status(400).json({ 
        message: '只允许上传图片文件',
        success: false
      });
    }
    
    // 创建唯一文件名
    const uniqueFilename = `${uuidv4()}${path.extname(uploadedFile.name)}`;
    console.log('支付截图文件名:', uniqueFilename);
    
    // 设置保存路径
    const uploadDir = path.join(__dirname, 'uploads/images');
    const filePath = path.join(uploadDir, uniqueFilename);
    console.log('支付截图保存路径:', filePath);
    
    // 移动文件
    uploadedFile.mv(filePath, function(err) {
      if (err) {
        console.error('保存文件失败:', err);
        return res.status(500).json({
          message: '保存文件失败',
          success: false
        });
      }
      
      // 构建图片完整URL
      const baseUrl = process.env.SERVER_URL || `${req.protocol}://${req.get('host')}`;
      const imagePath = `/uploads/images/${uniqueFilename}`;
      const imageUrl = `${baseUrl}${imagePath}`;
      
      console.log('支付截图上传成功:', imageUrl);
      
      // 返回成功响应，同时支持多种格式以增强兼容性
      return res.json({ 
        imageUrl: imageUrl,
        url: imageUrl,
        errno: 0,
        data: {
          url: imageUrl,
          alt: uploadedFile.name || '支付凭证',
          href: imageUrl
        }
      });
    });
  } catch (error) {
    console.error('处理上传响应时出错:', error);
    return res.status(500).json({
      message: '服务器处理上传时出错',
      success: false
    });
  }
});

// API根路由
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to Exchange Mall API' });
});

// 健康检查路由
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: '服务器运行正常' });
});

// 路由导入 - 使用统一路由文件
app.use('/api/auth', require('./routes/auth'));
app.use('/api/products', require('./routes/products'));
app.use('/api/product-images', require('./routes/productImages'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/announcements', require('./routes/announcements'));
app.use('/api/users', require('./routes/users'));
// 注册上传路由
app.use('/api/upload', require('./routes/upload'));
// 注册兑换路由
app.use('/api/exchanges', require('./routes/exchanges'));
app.use('/api/feedback', require('./routes/feedback'));
// 注册通知路由
app.use('/api/notifications', require('./routes/notification'));
// 注册日志路由
app.use('/api/logs', require('./routes/logs'));
// 注册导出路由
app.use('/api/exports', require('./routes/exports'));
// 注册系统管理路由
app.use('/api/system', require('./routes/system'));
// 注册飞书登录路由
app.use('/api/feishu', require('./routes/feishu'));
// 注册飞书群管理高级功能路由
app.use('/api', require('./routes/api'));

// 错误处理中间件
app.use((req, res, next) => {
  const error = new Error('Not Found');
  error.status = 404;
  next(error);
});

app.use((error, req, res, next) => {
  console.error(`错误: ${error.message}`, error.stack);
  res.status(error.status || 500);
  res.json({
    error: {
      message: error.message
    }
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000; // 使用默认端口3000
app.listen(PORT, () => {
  const serverUrl = process.env.SERVER_URL || `http://localhost:${PORT}`;
  console.log(`==================================`);
  console.log(`服务器启动成功，端口: ${PORT}`);
  console.log(`API地址: ${serverUrl}/api`);
  console.log(`上传API: ${serverUrl}/api/upload`);
  console.log(`静态文件: ${serverUrl}/uploads`);
  console.log(`==================================`);
  
  // 初始化定时统计报告服务
  try {
    scheduledReportService.init();
    console.log(`📊 定时统计报告服务已启动`);
  } catch (error) {
    console.error('❌ 定时统计报告服务启动失败:', error);
  }
});

module.exports = app; 
const express = require('express');
const router = express.Router();
const productImageController = require('../controllers/productImageController');
const { authenticate } = require('../middlewares/auth');
const { isAdmin } = require('../middlewares/admin');
const upload = require('../middlewares/upload');

// 获取商品的所有图片
router.get('/product/:productId', productImageController.getProductImages);

// 以下路由需要管理员权限
// 添加商品图片（通过URL）
router.post('/product/:productId', authenticate, isAdmin, productImageController.addProductImage);

// 上传商品图片（文件上传）
router.post('/upload/product/:productId', 
  authenticate, 
  isAdmin, 
  upload.single('image'), 
  upload.handleError, 
  productImageController.uploadProductImage
);

// 更新商品图片
router.put('/:imageId', authenticate, isAdmin, productImageController.updateProductImage);

// 删除商品图片
router.delete('/:imageId', authenticate, isAdmin, productImageController.deleteProductImage);

module.exports = router;

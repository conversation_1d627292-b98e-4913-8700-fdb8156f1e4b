const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { authenticate } = require('../middlewares/authMiddleware');
const { isAdmin } = require('../middlewares/admin');
const { User } = require('../models');
const { generateToken } = require('../utils/jwt');

// 管理员创建用户 - 仅管理员可用
router.post('/admin/register', authenticate, isAdmin, authController.register);

// 公开用户注册 - 所有人可用
router.post('/register', authController.registerPublic);

// 用户登录 - 增加错误处理中间件
router.post('/login', (req, res, next) => {
  console.log('====== 登录请求预处理 ======');
  console.log('收到登录请求路径:', req.path);
  console.log('请求方法:', req.method);
  
  // 检查请求体
  if (!req.body || typeof req.body !== 'object') {
    console.error('请求体无效:', req.body);
    return res.status(400).json({ message: '请求格式无效' });
  }
  
  // 输出请求信息
  console.log('姓名:', req.body.username || '未提供');
  console.log('邮箱:', req.body.email || '未提供');
  console.log('密码长度:', req.body.password ? req.body.password.length : 0);
  
  // 移除任何尾随的空格
  if (req.body.username) req.body.username = req.body.username.trim();
  if (req.body.email) req.body.email = req.body.email.trim();
  if (req.body.password) req.body.password = req.body.password.trim();
  
  // 验证普通用户必须提供姓名和邮箱
  if ((!req.body.username || !req.body.email) && req.body.userType !== 'admin') {
    return res.status(400).json({ message: '普通用户登录必须提供姓名和邮箱' });
  }
  
  // 验证管理员可以仅用姓名登录
  if (!req.body.username && (!req.body.email || req.body.userType !== 'admin')) {
    return res.status(400).json({ message: '姓名无效，至少需要2个字符' });
  }
  
  // 验证姓名长度
  if (req.body.username && req.body.username.length < 2) {
    return res.status(400).json({ message: '姓名无效，至少需要2个字符' });
  }
  
  // 验证邮箱格式（如果提供）
  if (req.body.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(req.body.email)) {
      return res.status(400).json({ message: '邮箱格式无效' });
    }
  }
  
  // 验证密码长度
  if (!req.body.password || req.body.password.length < 6) {
    return res.status(400).json({ message: '密码长度必须至少为6个字符' });
  }
  
  console.log('预处理后姓名:', req.body.username);
  console.log('预处理后邮箱:', req.body.email);
  console.log('预处理后密码长度:', req.body.password.length);
  console.log('====== 预处理完成，转交控制器 ======');
  
  next();
}, authController.login);

// 获取用户资料
router.get('/profile', authenticate, authController.getProfile);

// 获取当前登录用户信息
router.get('/current', authenticate, authController.getCurrentUser);

// 添加退出登录路由
router.post('/logout', authenticate, authController.logout);

// 刷新用户令牌 - 用于更新角色信息
router.post('/refresh-token', authenticate, async (req, res) => {
  try {
    console.log('收到刷新令牌请求');
    console.log('用户ID:', req.user.id);
    console.log('用户名:', req.user.username);
    console.log('当前角色:', req.user.role);
    
    // 从数据库获取最新的用户信息
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      console.log('用户不存在，ID:', req.user.id);
      return res.status(404).json({ message: '用户不存在' });
    }
    
    console.log('数据库中的用户角色:', user.role);
    
    // 生成新的令牌
    const userForToken = { ...user.get() };
    delete userForToken.password;
    
    const token = generateToken(userForToken, true);
    
    console.log('令牌刷新成功');
    console.log('新的用户角色:', user.role);
    
    return res.status(200).json({
      message: '令牌刷新成功',
      user: userForToken,
      token
    });
  } catch (error) {
    console.error('刷新令牌失败:', error);
    return res.status(500).json({ message: '刷新令牌失败' });
  }
});

module.exports = router; 
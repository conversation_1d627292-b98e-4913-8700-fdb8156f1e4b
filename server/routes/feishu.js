const express = require('express');
const router = express.Router();
const feishuController = require('../controllers/feishuController');

/**
 * @route GET /api/feishu/login-url
 * @desc 获取飞书登录URL
 * @access 公开
 */
router.get('/login-url', feishuController.getLoginUrl);

/**
 * @route GET /api/feishu/callback
 * @desc 飞书登录回调处理
 * @access 公开
 */
router.get('/callback', feishuController.handleCallback);

/**
 * @route POST /api/feishu/login
 * @desc 飞书登录API（前端获取code后直接登录）
 * @access 公开
 */
router.post('/login', feishuController.feishuLogin);

module.exports = router; 
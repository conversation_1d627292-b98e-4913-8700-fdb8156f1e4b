const express = require('express');
const router = express.Router();
const feedbackController = require('../controllers/feedbackController');
const { authenticate } = require('../middlewares/authMiddleware');
const { adminRoute } = require('../utils/adminRouteHelper');

/**
 * @route POST /api/feedback
 * @desc 提交反馈
 * @access 需要登录
 */
router.post('/', authenticate, feedbackController.submitFeedback);

/**
 * @route GET /api/feedback/user
 * @desc 获取当前用户的反馈列表
 * @access 需要登录
 */
router.get('/user', authenticate, feedbackController.getUserFeedbacks);

/**
 * @route GET /api/feedback/:id
 * @desc 获取反馈详情
 * @access 需要登录（用户只能查看自己的反馈，管理员可查看所有）
 */
router.get('/:id', authenticate, feedbackController.getFeedbackDetail);

/**
 * @route GET /api/feedback
 * @desc 获取所有反馈列表
 * @access 仅管理员
 */
router.get('/', adminRoute, feedbackController.getAllFeedbacks);

/**
 * @route PUT /api/feedback/:id
 * @desc 更新反馈状态和回复
 * @access 仅管理员
 */
router.put('/:id', adminRoute, feedbackController.updateFeedback);

module.exports = router; 
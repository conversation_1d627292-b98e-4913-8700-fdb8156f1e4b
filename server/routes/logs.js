const express = require('express');
const router = express.Router();
const { getLogs, getLogById, getLogStats, exportLogs } = require('../controllers/logController');
const { isAdmin, authenticate } = require('../middlewares/auth');

// 特定路由必须在参数路由之前定义
// 统计数据接口
router.get('/stats', getLogStats);

// 导出日志数据接口 - 需要管理员权限
router.get('/export', authenticate, isAdmin, exportLogs);

// 清理旧日志记录接口 - 已移除
// router.post('/cleanup', authenticate, isAdmin, clearOldLogs);

// 日志列表接口
router.get('/', getLogs);

// 单个日志详情接口 - 必须放在最后
router.get('/:id', getLogById);

module.exports = router; 
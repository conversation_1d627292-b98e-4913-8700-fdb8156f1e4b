const express = require('express');
const router = express.Router();
const { 
  resetSystem, 
  getPaymentQRCode, 
  uploadPaymentQRCode, 
  deletePaymentQRCode,
  getWorkplaces,
  getAllActiveWorkplaces,
  getWorkplace,
  createWorkplace,
  updateWorkplace,
  deleteWorkplace
} = require('../controllers/systemController');
const { authenticate, isAdmin } = require('../middlewares/auth');
const { User, Product, Exchange, Feedback, Category } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');
const scheduledReportService = require('../services/scheduledReportService');
const notificationConfigService = require('../services/notificationConfigService');
const notificationLogService = require('../services/notificationLogService');

// 系统重置接口 - 需要管理员权限
router.post('/reset', authenticate, isAdmin, resetSystem);

// 支付码管理接口
// 获取支付码接口 - 不需要认证，任何人都可以访问
router.get('/payment-qrcode', getPaymentQRCode);
// 上传和删除支付码接口 - 需要管理员权限
router.post('/payment-qrcode', authenticate, isAdmin, uploadPaymentQRCode);
router.delete('/payment-qrcode', authenticate, isAdmin, deletePaymentQRCode);

// 职场管理接口 - 所有接口都需要管理员权限
// 获取职场列表 (分页)
router.get('/workplaces', authenticate, isAdmin, getWorkplaces);
// 获取所有活跃职场 (用于下拉选择)
router.get('/workplaces/active', authenticate, getAllActiveWorkplaces);
// 获取单个职场详情
router.get('/workplaces/:id', authenticate, isAdmin, getWorkplace);
// 创建职场
router.post('/workplaces', authenticate, isAdmin, createWorkplace);
// 更新职场
router.put('/workplaces/:id', authenticate, isAdmin, updateWorkplace);
// 删除职场
router.delete('/workplaces/:id', authenticate, isAdmin, deleteWorkplace);

// 系统信息获取
// router.get('/info', authenticate, isAdmin, getSystemInfo);

// 手动触发每日报告（测试用）
router.post('/trigger-daily-report', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🧪 管理员手动触发今日销售汇总报告...');
    await scheduledReportService.triggerDailyReport();
    res.json({ 
      success: true, 
      message: '今日销售汇总报告已发送到飞书群' 
    });
  } catch (error) {
    console.error('手动触发今日销售汇总报告失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '发送今日销售汇总报告失败',
      error: error.message 
    });
  }
});

// 手动触发每周报告（测试用）
router.post('/trigger-weekly-report', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🧪 管理员手动触发每周报告...');
    await scheduledReportService.triggerWeeklyReport();
    res.json({ 
      success: true, 
      message: '每周报告已发送到飞书群' 
    });
  } catch (error) {
    console.error('手动触发每周报告失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '发送每周报告失败',
      error: error.message 
    });
  }
});

// 手动触发新用户欢迎通知（测试用）
router.post('/trigger-new-user-welcome', authenticate, isAdmin, async (req, res) => {
  try {
    const operationalNotificationService = require('../services/operationalNotificationService');
    
    // 获取最近注册的飞书用户
    const recentUser = await User.findOne({
      where: {
        authType: 'feishu'
      },
      order: [['createdAt', 'DESC']]
    });
    
    if (!recentUser) {
      return res.status(404).json({ 
        success: false, 
        message: '未找到飞书用户进行测试' 
      });
    }
    
    console.log('🧪 管理员手动触发新用户欢迎通知...');
    await operationalNotificationService.sendNewUserWelcome(recentUser);
    
    res.json({ 
      success: true, 
      message: `已为用户"${recentUser.username}"发送新用户欢迎通知` 
    });
  } catch (error) {
    console.error('手动触发新用户欢迎通知失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '发送新用户欢迎通知失败',
      error: error.message 
    });
  }
});

// 手动触发销售里程碑通知（测试用）
router.post('/trigger-sales-milestone', authenticate, isAdmin, async (req, res) => {
  try {
    const operationalNotificationService = require('../services/operationalNotificationService');
    
    // 获取最近的兑换记录
    const recentExchange = await Exchange.findOne({
      order: [['createdAt', 'DESC']]
    });
    
    if (!recentExchange) {
      return res.status(404).json({ 
        success: false, 
        message: '未找到兑换记录进行测试' 
      });
    }
    
    console.log('🧪 管理员手动触发销售里程碑检查...');
    await operationalNotificationService.checkSalesMilestone(recentExchange);
    
    res.json({ 
      success: true, 
      message: '已完成销售里程碑检查，如达到里程碑会发送通知' 
    });
  } catch (error) {
    console.error('手动触发销售里程碑检查失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '销售里程碑检查失败',
      error: error.message 
    });
  }
});

// 手动触发异常订单预警（测试用）
router.post('/trigger-order-alert', authenticate, isAdmin, async (req, res) => {
  try {
    const operationalNotificationService = require('../services/operationalNotificationService');
    
    // 获取最近的兑换记录
    const recentExchange = await Exchange.findOne({
      include: [{
        model: Product,
        attributes: ['name', 'lyPrice', 'rmbPrice']
      }, {
        model: User,
        attributes: ['id', 'username', 'department', 'departmentPath']
      }],
      order: [['createdAt', 'DESC']]
    });
    
    if (!recentExchange) {
      return res.status(404).json({ 
        success: false, 
        message: '未找到兑换记录进行测试' 
      });
    }
    
    console.log('🧪 管理员手动触发异常订单预警检查...');
    await operationalNotificationService.checkOrderAlert(
      recentExchange,
      recentExchange.Product,
      recentExchange.User
    );
    
    res.json({ 
      success: true, 
      message: '已完成异常订单预警检查，如有异常会发送预警' 
    });
  } catch (error) {
    console.error('手动触发异常订单预警失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '异常订单预警失败',
      error: error.message 
    });
  }
});

// 手动触发用户反馈通知（测试用）
router.post('/trigger-feedback-alert', authenticate, isAdmin, async (req, res) => {
  try {
    const operationalNotificationService = require('../services/operationalNotificationService');
    
    // 获取最近的反馈记录
    const recentFeedback = await Feedback.findOne({
      include: [{
        model: User,
        attributes: ['id', 'username', 'department', 'departmentPath']
      }],
      order: [['createdAt', 'DESC']]
    });
    
    if (!recentFeedback) {
      return res.status(404).json({ 
        success: false, 
        message: '未找到反馈记录进行测试' 
      });
    }
    
    console.log('🧪 管理员手动触发用户反馈通知...');
    await operationalNotificationService.sendFeedbackAlert(
      recentFeedback,
      recentFeedback.User
    );
    
    res.json({ 
      success: true, 
      message: `已为反馈"${recentFeedback.title}"发送通知` 
    });
  } catch (error) {
    console.error('手动触发用户反馈通知失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '用户反馈通知失败',
      error: error.message 
    });
  }
});

// 手动触发新品上架通知（测试用）
router.post('/trigger-new-product-notification', authenticate, isAdmin, async (req, res) => {
  try {
    const operationalNotificationService = require('../services/operationalNotificationService');
    const { Product, Category } = require('../models');
    
    // 获取最近创建的商品
    const recentProduct = await Product.findOne({
      include: [{
        model: Category,
        attributes: ['name']
      }],
      order: [['createdAt', 'DESC']]
    });
    
    if (!recentProduct) {
      return res.status(404).json({ 
        success: false, 
        message: '未找到商品记录进行测试' 
      });
    }
    
    console.log('🧪 管理员手动触发新品上架通知...');
    await operationalNotificationService.sendNewProductNotification(
      recentProduct,
      req.user
    );
    
    res.json({ 
      success: true, 
      message: `已为商品"${recentProduct.name}"发送新品上架通知` 
    });
  } catch (error) {
    console.error('手动触发新品上架通知失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '新品上架通知失败',
      error: error.message 
    });
  }
});

// 手动触发月度报告（测试用）
router.post('/trigger-monthly-report', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🧪 管理员手动触发月度报告...');
    await scheduledReportService.triggerMonthlyReport();
    
    res.json({ 
      success: true, 
      message: '月度报告已发送到飞书群' 
    });
  } catch (error) {
    console.error('手动触发月度报告失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '发送月度报告失败',
      error: error.message 
    });
  }
});

// 手动触发系统维护通知（测试用）
router.post('/trigger-maintenance-notification', authenticate, isAdmin, async (req, res) => {
  try {
    const systemEventService = require('../services/systemEventService');
    
    // 模拟维护通知数据
    const maintenanceData = {
      type: 'scheduled',
      title: '光年小卖部系统维护升级',
      startTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2小时后
      endTime: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4小时后
      reason: '系统版本升级，优化用户体验和性能',
      impact: [
        '兑换功能暂时不可用',
        '商品浏览功能正常',
        '用户登录可能受影响'
      ],
      preparations: [
        '请提前完成待处理的兑换申请',
        '建议保存重要数据',
        '维护期间请勿进行重要操作'
      ],
      contactInfo: '技术支持群或管理员'
    };
    
    console.log('🧪 管理员手动触发系统维护通知...');
    await systemEventService.sendMaintenanceNotification(maintenanceData);
    
    res.json({ 
      success: true, 
      message: '系统维护通知已发送到飞书群' 
    });
  } catch (error) {
    console.error('手动触发系统维护通知失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '发送系统维护通知失败',
      error: error.message 
    });
  }
});

// 手动触发错误告警推送（测试用）
router.post('/trigger-error-alert', authenticate, isAdmin, async (req, res) => {
  try {
    const systemEventService = require('../services/systemEventService');
    
    // 模拟错误告警数据
    const errorData = {
      severity: 'high',
      errorType: '数据库连接异常',
      errorMessage: '数据库连接池耗尽，无法建立新的数据库连接',
      systemComponent: '数据库服务',
      stackTrace: 'Error: Connection pool exhausted\n    at Database.connect (database.js:45:12)\n    at async UserController.getUsers (userController.js:23:8)',
      resolution: '重启数据库连接池服务，检查连接泄漏问题',
      affectedUsers: '所有用户',
      monitorUrl: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/logs`
    };
    
    console.log('🧪 管理员手动触发错误告警推送...');
    await systemEventService.sendErrorAlert(errorData);
    
    res.json({ 
      success: true, 
      message: '错误告警已发送到飞书群' 
    });
  } catch (error) {
    console.error('手动触发错误告警推送失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '发送错误告警失败',
      error: error.message 
    });
  }
});

// 节假日识别测试接口
router.get('/holiday-status', authenticate, isAdmin, async (req, res) => {
  try {
    const holidayService = require('../services/holidayService');
    const { date } = req.query;
    
    const targetDate = date ? new Date(date) : new Date();
    const dateStatus = holidayService.getDateStatus(targetDate);
    
    res.json({
      success: true,
      data: dateStatus,
      message: `日期状态查询成功: ${dateStatus.description}`
    });
  } catch (error) {
    console.error('获取节假日状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取节假日状态失败',
      error: error.message
    });
  }
});

// 获取工作日统计接口
router.get('/working-days', authenticate, isAdmin, async (req, res) => {
  try {
    const holidayService = require('../services/holidayService');
    const { year, month, startDate, endDate } = req.query;
    
    let result = {};
    
    if (year && month) {
      // 获取指定月份的工作日数量
      result.workingDaysInMonth = holidayService.getWorkingDaysInMonth(parseInt(year), parseInt(month));
      result.month = `${year}年${month}月`;
    }
    
    if (startDate && endDate) {
      // 获取指定日期范围的工作日数量
      result.workingDaysInRange = holidayService.getWorkingDaysInRange(startDate, endDate);
      result.dateRange = `${startDate} 至 ${endDate}`;
    }
    
    if (!year && !month && !startDate && !endDate) {
      // 默认返回当前月份的工作日数量
      const now = new Date();
      result.workingDaysInMonth = holidayService.getWorkingDaysInMonth(now.getFullYear(), now.getMonth() + 1);
      result.month = `${now.getFullYear()}年${now.getMonth() + 1}月`;
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取工作日统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工作日统计失败',
      error: error.message
    });
  }
});

// ================== 飞书群机器人通知管理 API ==================

// 获取所有通知配置
router.get('/notification-configs', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🔧 管理员获取通知配置列表...');
    const configs = await notificationConfigService.getAllConfigs();
    res.json({
      success: true,
      data: configs,
      message: '获取通知配置成功'
    });
  } catch (error) {
    console.error('获取通知配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知配置失败',
      error: error.message
    });
  }
});

// 获取单个通知配置
router.get('/notification-configs/:type', authenticate, isAdmin, async (req, res) => {
  try {
    const { type } = req.params;
    console.log(`🔧 管理员获取通知配置: ${type}`);
    
    const config = await notificationConfigService.getConfigByType(type);
    if (!config) {
      return res.status(404).json({
        success: false,
        message: '通知配置不存在'
      });
    }
    
    res.json({
      success: true,
      data: config,
      message: '获取通知配置成功'
    });
  } catch (error) {
    console.error('获取通知配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知配置失败',
      error: error.message
    });
  }
});

// 更新通知配置
router.put('/notification-configs/:type', authenticate, isAdmin, async (req, res) => {
  try {
    const { type } = req.params;
    const updateData = req.body;
    
    console.log(`🔧 管理员更新通知配置: ${type}`, JSON.stringify(updateData));
    
    // 验证请求数据
    if (!type) {
      return res.status(400).json({
        success: false,
        message: '通知类型不能为空',
        error: '通知类型不能为空'
      });
    }

    // 确保至少有一个需要更新的字段
    if (!updateData || Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: '更新数据不能为空',
        error: '更新数据不能为空'
      });
    }

    // 如果包含enabled字段，确保它是布尔值
    if ('enabled' in updateData) {
      updateData.enabled = Boolean(updateData.enabled);
      console.log(`🔧 确保enabled为布尔值: ${updateData.enabled}`);
    }
    
    const updatedConfig = await notificationConfigService.updateConfig(type, updateData);
    
    console.log(`✅ 通知配置更新完成: ${type}`, JSON.stringify(updatedConfig));
    
    res.json({
      success: true,
      data: updatedConfig,
      message: '更新通知配置成功'
    });
  } catch (error) {
    console.error('更新通知配置失败:', error);
    const errorMessage = error.message || error.toString() || '未知错误';
    res.status(500).json({
      success: false,
      message: '更新通知配置失败: ' + errorMessage,
      error: errorMessage
    });
  }
});

// 批量更新通知配置
router.put('/notification-configs', authenticate, isAdmin, async (req, res) => {
  try {
    const configsData = req.body;
    console.log('🔧 管理员批量更新通知配置...', configsData);
    
    const updatedConfigs = await notificationConfigService.batchUpdateConfigs(configsData);
    res.json({
      success: true,
      data: updatedConfigs,
      message: '批量更新通知配置成功'
    });
  } catch (error) {
    console.error('批量更新通知配置失败:', error);
    res.status(500).json({
      success: false,
      message: '批量更新通知配置失败',
      error: error.message
    });
  }
});

// 检查webhook配置状态（不发送测试消息）
router.get('/webhook-status', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🔍 检查Webhook配置状态...');

    // 检查环境变量中是否配置了webhook
    const hasWebhookUrl = !!process.env.FEISHU_BOT_WEBHOOK_URL;

    // 检查数据库中是否有配置了webhook的通知类型
    const configs = await notificationConfigService.getAllConfigs();
    const hasConfigWithWebhook = configs.some(config => config.webhookUrl && config.webhookUrl.trim());

    console.log('🔍 Webhook状态检查结果:', {
      hasWebhookUrl,
      hasConfigWithWebhook,
      configCount: configs.length
    });

    res.json({
      success: true,
      data: {
        hasWebhookUrl,
        hasConfigWithWebhook,
        configCount: configs.length
      },
      message: 'Webhook状态检查完成'
    });
  } catch (error) {
    console.error('检查Webhook状态失败:', error);
    res.status(500).json({
      success: false,
      message: '检查Webhook状态失败',
      error: error.message
    });
  }
});

// 测试webhook连接
router.post('/test-webhook', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🧪 管理员测试Webhook连接...');
    // 尝试获取要测试的webhook地址
    const { webhookUrl } = req.body;
    
    // 记录环境变量和提供的webhookUrl
    console.log('🧪 环境变量FEISHU_BOT_WEBHOOK_URL:', process.env.FEISHU_BOT_WEBHOOK_URL ? '已设置' : '未设置');
    console.log('🧪 请求提供的webhookUrl:', webhookUrl ? '已提供' : '未提供');
    
    // 进行连接测试，优先使用提供的URL
    const testResult = await notificationConfigService.testWebhookConnection(webhookUrl);
    
    console.log('🧪 Webhook测试结果:', testResult.success ? '成功' : '失败', testResult);
    
    if (testResult.success) {
      // 如果测试成功，确保至少有一个配置使用这个webhook
      const testedUrl = webhookUrl || process.env.FEISHU_BOT_WEBHOOK_URL;
      if (testedUrl) {
        try {
          // 检查是否有使用该webhook的配置
          const configs = await notificationConfigService.getAllConfigs();
          const hasConfigWithThisWebhook = configs.some(config => config.webhookUrl === testedUrl);
          
          // 如果没有配置使用这个webhook，创建或更新第一个配置
          if (!hasConfigWithThisWebhook) {
            console.log('🧪 未找到使用该webhook的配置，创建或更新第一个配置');
            // 选择第一个业务通知类型进行配置
            await notificationConfigService.updateConfig('exchange_notification', { 
              webhookUrl: testedUrl, 
              enabled: true 
            });
          }
        } catch (configError) {
          console.error('为测试的webhook创建配置失败:', configError);
          // 继续处理，不影响测试结果
        }
      }
    }
    
    res.json({
      success: true,
      data: testResult,
      message: testResult.success ? '连接测试成功' : '连接测试失败'
    });
  } catch (error) {
    console.error('测试webhook连接失败:', error);
    res.status(500).json({
      success: false,
      message: '测试webhook连接失败',
      error: error.message
    });
  }
});

// 发送测试通知
router.post('/test-notification/:type', authenticate, isAdmin, async (req, res) => {
  try {
    const { type } = req.params;
    console.log(`🧪 管理员发送测试通知: ${type}`);
    
    const testResult = await notificationConfigService.sendTestNotification(type);
    res.json({
      success: testResult.success,
      data: testResult,
      message: testResult.message
    });
  } catch (error) {
    console.error('发送测试通知失败:', error);
    res.status(500).json({
      success: false,
      message: '发送测试通知失败',
      error: error.message
    });
  }
});

// 发送自定义消息
router.post('/send-custom-message', authenticate, isAdmin, async (req, res) => {
  try {
    const { content, messageType = 'text' } = req.body;
    
    if (!content || content.trim() === '') {
      return res.status(400).json({
        success: false,
        message: '消息内容不能为空'
      });
    }
    
    console.log('🧪 管理员发送自定义消息...');
    const sendResult = await notificationConfigService.sendCustomMessage(content, messageType);
    
    res.json({
      success: sendResult.success,
      data: sendResult,
      message: sendResult.message
    });
  } catch (error) {
    console.error('发送自定义消息失败:', error);
    res.status(500).json({
      success: false,
      message: '发送自定义消息失败',
      error: error.message
    });
  }
});

// 批量配置webhook地址
router.post('/configure-webhook', authenticate, isAdmin, async (req, res) => {
  try {
    const { webhookUrl, applyToTypes } = req.body;
    console.log('🔧 管理员批量配置Webhook地址...', { webhookUrl, applyToTypes });

    if (!webhookUrl || !webhookUrl.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Webhook地址不能为空'
      });
    }

    // 验证webhook地址格式
    const webhookPattern = /^https:\/\/open\.feishu\.cn\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9-]+$/;
    if (!webhookPattern.test(webhookUrl)) {
      return res.status(400).json({
        success: false,
        message: '请输入有效的飞书机器人Webhook地址'
      });
    }

    const result = await notificationConfigService.configureWebhook(webhookUrl, applyToTypes);
    res.json({
      success: true,
      data: result,
      message: 'Webhook配置成功'
    });
  } catch (error) {
    console.error('配置Webhook失败:', error);
    res.status(500).json({
      success: false,
      message: '配置Webhook失败',
      error: error.message
    });
  }
});

// 获取通知类型列表
router.get('/notification-types', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🔧 管理员获取通知类型列表...');
    const types = notificationConfigService.getNotificationTypes();
    res.json({
      success: true,
      data: types,
      message: '获取通知类型列表成功'
    });
  } catch (error) {
    console.error('获取通知类型列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知类型列表失败',
      error: error.message
    });
  }
});

// ================== 飞书群机器人通知日志管理 API ==================

// 获取通知发送历史
router.get('/notification-history', authenticate, isAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      notificationType,
      status,
      triggerSource,
      startDate,
      endDate
    } = req.query;

    console.log('🔧 管理员获取通知发送历史...');
    
    const result = await notificationLogService.getNotificationHistory({
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      notificationType,
      status,
      triggerSource,
      startDate,
      endDate
    });

    res.json({
      success: true,
      data: result,
      message: '获取通知历史成功'
    });
  } catch (error) {
    console.error('获取通知历史失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知历史失败',
      error: error.message
    });
  }
});

// 获取通知统计数据
router.get('/notification-stats', authenticate, isAdmin, async (req, res) => {
  try {
    const { startDate, endDate, period = 'day' } = req.query;
    console.log('🔧 管理员获取通知统计数据...');
    
    const stats = await notificationLogService.getNotificationStats({
      startDate,
      endDate,
      period
    });

    res.json({
      success: true,
      data: stats,
      message: '获取通知统计成功'
    });
  } catch (error) {
    console.error('获取通知统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知统计失败',
      error: error.message
    });
  }
});

// 手动重试失败的通知
router.post('/retry-notification/:logId', authenticate, isAdmin, async (req, res) => {
  try {
    const { logId } = req.params;
    console.log(`🔧 管理员手动重试通知: ${logId}`);
    
    const result = await notificationLogService.manualRetry(parseInt(logId));
    
    res.json({
      success: result.success,
      data: result,
      message: result.success ? '通知重试成功' : '通知重试失败'
    });
  } catch (error) {
    console.error('手动重试通知失败:', error);
    res.status(500).json({
      success: false,
      message: '手动重试通知失败',
      error: error.message
    });
  }
});

// 处理所有失败重试（定时任务触发）
router.post('/process-failed-retries', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('🔧 管理员触发失败重试处理...');
    
    const processedCount = await notificationLogService.processFailedRetries();
    
    res.json({
      success: true,
      data: { processedCount },
      message: `处理了 ${processedCount} 条待重试通知`
    });
  } catch (error) {
    console.error('处理失败重试失败:', error);
    res.status(500).json({
      success: false,
      message: '处理失败重试失败',
      error: error.message
    });
  }
});

module.exports = router; 
const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { authenticate, isAdmin } = require('../middlewares/auth');
const exportController = require('../controllers/exportController');

/**
 * @route   GET /api/exports/dashboard
 * @desc    导出仪表盘数据为Excel或PDF
 * @access  Private (管理员)
 */
router.get('/dashboard', 
  authenticate, 
  isAdmin,
  [
    check('format').isIn(['excel', 'pdf']).withMessage('格式必须是excel或pdf'),
    check('period').optional().isIn(['week', 'month', 'year']).withMessage('周期必须是week、month或year')
  ],
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
  exportController.exportDashboardData
);

module.exports = router; 
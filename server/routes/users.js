const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const auth = require('../middlewares/auth');
const path = require('path');
const fs = require('fs');

// 确保临时目录存在
const tempDir = path.join(__dirname, '..', 'temp');
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

/**
 * @route GET /
 * @desc 获取用户列表
 * @access 仅管理员
 */
router.get('/', auth.authenticate, auth.isAdmin, userController.getUsers);

/**
 * @route POST /
 * @desc 创建新用户
 * @access 仅管理员
 */
router.post('/', auth.authenticate, auth.isAdmin, userController.createUser);

/**
 * @route DELETE /:id
 * @desc 删除用户
 * @access 仅管理员
 */
router.delete('/:id', auth.authenticate, auth.isAdmin, userController.deleteUser);

/**
 * @route PUT /:id/role
 * @desc 更新用户角色
 * @access 仅管理员
 */
router.put('/:id/role', auth.authenticate, auth.isAdmin, userController.updateUserRole);

/**
 * @route PUT /:id/department
 * @desc 更新用户部门
 * @access 仅管理员
 */
router.put('/:id/department', auth.authenticate, auth.isAdmin, userController.updateUserDepartment);

/**
 * @route PUT /:id/email
 * @desc 更新用户邮箱
 * @access 仅管理员
 */
router.put('/:id/email', auth.authenticate, auth.isAdmin, userController.updateUserEmail);

/**
 * @route PUT /:id/mobile
 * @desc 更新用户手机号码
 * @access 仅管理员
 */
router.put('/:id/mobile', auth.authenticate, auth.isAdmin, userController.updateUserMobile);

/**
 * @route PUT /:id/workplace
 * @desc 更新用户职场
 * @access 仅管理员
 */
router.put('/:id/workplace', auth.authenticate, auth.isAdmin, userController.updateUserWorkplace);

/**
 * @route PUT /:id/reset-password
 * @desc 重置用户密码
 * @access 仅管理员
 */
router.put('/:id/reset-password', auth.authenticate, auth.isAdmin, userController.resetUserPassword);

/**
 * @route POST /import
 * @desc 批量导入用户
 * @access 仅管理员
 */
router.post('/import', auth.authenticate, auth.isAdmin, userController.importUsers);

/**
 * @route GET /export
 * @desc 导出用户列表
 * @access 仅管理员
 */
router.get('/export', auth.authenticate, auth.isAdmin, userController.exportUsers);

/**
 * @route GET /template
 * @desc 下载用户导入模板
 * @access 仅管理员
 */
router.get('/template', auth.authenticate, auth.isAdmin, userController.downloadTemplate);

/**
 * @route POST /batch-delete
 * @desc 批量删除用户
 * @access 仅管理员
 */
router.post('/batch-delete', auth.authenticate, auth.isAdmin, userController.batchDeleteUsers);

/**
 * @route GET /activity
 * @desc 获取用户活跃度数据
 * @access 仅管理员
 */
router.get('/activity', auth.authenticate, auth.isAdmin, userController.getUserActivity);

/**
 * @route PUT /password
 * @desc 修改自己的密码
 * @access 已登录用户
 */
router.put('/password', auth.authenticate, userController.updatePassword);

module.exports = router; 
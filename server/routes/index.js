const express = require('express');
const router = express.Router();

// 引入各个路由
const authRoutes = require('./auth');
const productRoutes = require('./products');
const categoryRoutes = require('./categories');
const userRoutes = require('./users');
const productImageRoutes = require('./productImages');
const logRoutes = require('./logs');
const exportRoutes = require('./exports');
const feishuRoutes = require('./feishu');

// 健康检查接口
router.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: '服务器运行正常' });
});

// 注册各个路由
router.use('/auth', authRoutes);
router.use('/products', productRoutes);
router.use('/categories', categoryRoutes);
router.use('/users', userRoutes);
router.use('/product-images', productImageRoutes);
router.use('/logs', logRoutes);
router.use('/exports', exportRoutes);
router.use('/feishu', feishuRoutes);

module.exports = router; 
const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { authenticate } = require('../middlewares/authMiddleware');
const { isAdmin } = require('../middlewares/admin');
const multer = require('multer');
const path = require('path');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'import-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 限制10MB
  fileFilter: function (req, file, cb) {
    // 只接受csv和excel文件
    const filetypes = /csv|xlsx|xls/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);
    
    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('只支持上传CSV和Excel文件'));
    }
  } 
});

// 公开路由 - 不需要认证
// 获取商品列表（支持筛选、排序和分页）
router.get('/', productController.getProducts);

// 获取商品价格范围（用于滑块初始化）
router.get('/price-ranges', productController.getProductPriceRanges);

// 获取热门商品
router.get('/popular', productController.getPopularProducts);

// 导出商品数据
router.get('/export', authenticate, isAdmin, productController.exportProducts);

// 导入商品数据
router.post('/import', authenticate, isAdmin, upload.single('file'), productController.importProducts);

// 批量删除商品
router.post('/bulk-delete', authenticate, isAdmin, productController.bulkDeleteProducts);

// 获取单个商品详情
router.get('/:id', productController.getProductById);

// 受保护的路由 - 需要管理员权限
// 添加新商品
router.post('/', authenticate, isAdmin, productController.createProduct);

// 更新商品信息
router.put('/:id', authenticate, isAdmin, productController.updateProduct);

// 删除商品
router.delete('/:id', authenticate, isAdmin, productController.deleteProduct);

// 更新商品状态（上/下线）
router.put('/:id/status', authenticate, isAdmin, productController.updateProductStatus);

module.exports = router; 
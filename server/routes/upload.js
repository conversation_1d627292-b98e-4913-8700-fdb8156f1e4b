const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
// 正确引入认证中间件
const auth = require('../middlewares/auth');

// Image upload routes - matching client expectations
router.post('/image', auth.checkAuth, uploadController.uploadImage);
router.post('/paste-image', auth.checkAuth, uploadController.pasteImage);

// 添加公共上传端点 - 支付凭证上传不需要验证
router.post('/payment-proof', uploadController.uploadImage);

// File deletion route 
router.delete('/files/:filename', auth.checkAuth, uploadController.deleteFile);

module.exports = router; 
const express = require('express');
const router = express.Router();
const categoryController = require('../controllers/categoryController');
const { authenticate } = require('../middlewares/authMiddleware');
const { isAdmin } = require('../middlewares/admin');

// 公开路由 - 不需要认证
// 获取所有分类
router.get('/', categoryController.getCategories);

// 获取分类及其商品数量
router.get('/with-product-count', categoryController.getCategoriesWithProductCount);

// 获取单个分类详情
router.get('/:id', categoryController.getCategoryById);

// 受保护的路由 - 需要管理员权限
// 创建新分类
router.post('/', authenticate, isAdmin, categoryController.createCategory);

// 更新分类
router.put('/:id', authenticate, isAdmin, categoryController.updateCategory);

// 删除分类
router.delete('/:id', authenticate, isAdmin, categoryController.deleteCategory);

module.exports = router; 
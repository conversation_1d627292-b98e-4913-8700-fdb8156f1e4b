const express = require('express');
const router = express.Router();

// 导入控制器
// ... existing code ...

// 导入新控制器
const messageTemplateController = require('../controllers/messageTemplateController');
const sendingScheduleController = require('../controllers/sendingScheduleController');
const notificationDiagnosticController = require('../controllers/notificationDiagnosticController');

// ... existing routes ...

// ===== 飞书群管理高级功能路由 =====

// 消息模板路由
router.get('/message-templates', messageTemplateController.getTemplates);
router.get('/message-templates/:id', messageTemplateController.getTemplateById);
router.post('/message-templates', messageTemplateController.createTemplate);
router.put('/message-templates/:id', messageTemplateController.updateTemplate);
router.delete('/message-templates/:id', messageTemplateController.deleteTemplate);
router.post('/message-templates/test-render', messageTemplateController.testRenderTemplate);

// 发送时间调度路由
router.get('/sending-schedules', sendingScheduleController.getSchedules);
router.get('/sending-schedules/:id', sendingScheduleController.getScheduleById);
router.post('/sending-schedules', sendingScheduleController.createSchedule);
router.put('/sending-schedules/:id', sendingScheduleController.updateSchedule);
router.delete('/sending-schedules/:id', sendingScheduleController.deleteSchedule);
router.post('/sending-schedules/:id/toggle', sendingScheduleController.toggleSchedule);
router.post('/sending-schedules/calculate-next-time', sendingScheduleController.calculateNextSendTime);
router.get('/sending-schedules/system-metrics', sendingScheduleController.getSystemMetrics);
router.get('/sending-schedules/holidays', sendingScheduleController.getHolidays);
router.get('/sending-schedules/check-workday', sendingScheduleController.checkWorkday);

// 通知诊断路由
router.get('/notification-diagnostics', notificationDiagnosticController.getDiagnosticResults);
router.post('/notification-diagnostics/:id/resolve', notificationDiagnosticController.resolveIssue);
router.post('/notification-diagnostics/test-webhook', notificationDiagnosticController.testWebhookConnection);
router.get('/notification-diagnostics/system-health', notificationDiagnosticController.getSystemHealth);
router.post('/notification-diagnostics/analyze-message-delivery', notificationDiagnosticController.analyzeMessageDelivery);
router.post('/notification-diagnostics/comprehensive', notificationDiagnosticController.runComprehensiveDiagnostic);

// ... existing code ...

module.exports = router; 
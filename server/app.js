const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const cors = require('cors');

// 创建Express应用
const app = express();

// 配置中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));

// 导入路由模块
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const productRoutes = require('./routes/products');
const categoryRoutes = require('./routes/categories');
const announcementRoutes = require('./routes/announcements');
const feedbackRoutes = require('./routes/feedback');
const uploadRoutes = require('./routes/upload'); // 添加上传路由
const exchangeRoutes = require('./routes/exchanges'); // 添加兑换路由
const systemRoutes = require('./routes/system'); // 添加系统路由
const apiRoutes = require('./routes/api'); // 添加API路由

// 配置静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 使用路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/products', productRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/announcements', announcementRoutes);
app.use('/api/feedback', feedbackRoutes);
app.use('/api/upload', uploadRoutes); // 添加上传路由
app.use('/api/exchanges', exchangeRoutes); // 添加兑换路由
app.use('/api/system', systemRoutes); // 添加系统路由
app.use('/api', apiRoutes); // 添加API路由

// 启动定时报告服务
console.log('🚀 启动定时报告服务...');
const scheduledReportService = require('./services/scheduledReportService');
scheduledReportService.start();

// 启动定时任务服务
console.log('🚀 启动定时任务服务...');
const scheduledTaskService = require('./services/scheduledTaskService');
scheduledTaskService.startAllTasks();

// 设置端口并启动服务器
const PORT = process.env.PORT || 5173;
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
});

module.exports = app; 
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const config = require('../config/config');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '..', config.upload.directory);
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
  console.log('创建上传目录:', uploadDir);
} else {
  console.log('上传目录已存在:', uploadDir);
}

// 确保图片目录存在 
const imagesDir = path.join(uploadDir, 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
  console.log('创建图片上传目录:', imagesDir);
} else {
  console.log('图片上传目录已存在:', imagesDir);
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 根据路径判断是否为商品图片上传
    const isProductImage = req.originalUrl.includes('/product-images/upload/product/');
    // 商品图片存储在 images 子目录
    const targetDir = isProductImage ? imagesDir : uploadDir;
    console.log('设置上传文件目录:', targetDir, '请求路径:', req.originalUrl);
    cb(null, targetDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名，防止文件名冲突
    const uniqueSuffix = crypto.randomBytes(16).toString('hex');
    const extension = path.extname(file.originalname);
    const filename = uniqueSuffix + extension;
    console.log('生成上传文件名:', filename, '原始文件名:', file.originalname);
    cb(null, filename);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型是否允许
  const allowedTypes = config.upload.allowedTypes || ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  console.log('上传文件类型:', file.mimetype, '允许的类型:', allowedTypes);
  
  if (allowedTypes.includes(file.mimetype)) {
    console.log('文件类型验证通过');
    cb(null, true);
  } else {
    console.error('文件类型不允许:', file.mimetype);
    cb(new Error(`只允许以下文件类型: ${allowedTypes.join(', ')}`), false);
  }
};

// 创建 multer 实例
const upload = multer({
  storage: storage,
  limits: {
    fileSize: config.upload.maxSize // 使用配置中的文件大小限制
  },
  fileFilter: fileFilter
});

// 处理上传错误
const handleUploadError = (err, req, res, next) => {
  console.log('文件上传处理中...', err ? '发生错误' : '正常');
  
  if (err) {
    console.error('上传错误详情:', err);
    
    if (err instanceof multer.MulterError) {
      console.error('Multer错误:', err.code, err.message);
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(413).json({ 
          message: `文件大小超过限制，最大允许 ${config.upload.maxSize / (1024 * 1024)} MB`
        });
      }
      return res.status(400).json({ message: `上传错误: ${err.message}` });
    } else {
      console.error('非Multer错误:', err.message);
      return res.status(400).json({ message: err.message });
    }
  }
  
  console.log('文件上传处理完成，继续下一步');
  next();
};

// 添加错误处理函数到 upload 对象，以便导出
upload.handleError = handleUploadError;

module.exports = upload; 
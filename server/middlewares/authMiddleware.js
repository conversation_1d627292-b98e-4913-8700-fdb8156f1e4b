const jwt = require('jsonwebtoken');
const { User } = require('../models');
const config = require('../config/config');

/**
 * 认证中间件 - 验证JWT令牌
 */
exports.authenticate = async (req, res, next) => {
  try {
    // 从请求头获取token
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: '未提供认证令牌' });
    }
    
    // 获取令牌
    const token = authHeader.split(' ')[1];
    
    // 验证令牌
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // 查询用户，确保获取最新的数据库信息
    const user = await User.findByPk(decoded.id);
    
    if (!user) {
      return res.status(401).json({ message: '用户不存在' });
    }
    
    // 将用户添加到请求对象，使用数据库中的最新信息
    req.user = user.toJSON();
    
    // 从数据库获取的最新角色信息覆盖token中的角色信息
    req.user.isAdmin = req.user.role === 'admin';
    
    console.log('用户认证信息(已更新):', {
      id: req.user.id, 
      username: req.user.username,
      role: req.user.role,
      isAdmin: req.user.isAdmin,
      tokenRole: decoded.role,
      tokenIsAdmin: decoded.isAdmin
    });
    
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: '令牌已过期' });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: '无效的令牌' });
    }
    
    console.error('认证错误:', error);
    res.status(500).json({ message: '认证失败' });
  }
};

/**
 * 管理员中间件 - 检查用户是否有管理员权限
 */
exports.adminMiddleware = (req, res, next) => {
  console.log('管理员权限检查开始');
  
  // 检查用户是否存在
  if (!req.user) {
    console.log('权限检查失败: 用户未登录');
    return res.status(401).json({ message: '未授权访问，请先登录' });
  }
  
  console.log('权限检查详情:', { 
    username: req.user.username,
    userId: req.user.id,
    role: req.user.role, 
    isAdmin: req.user.isAdmin,
    roleIsAdmin: req.user.role === 'admin'
  });
  
  // 检查用户角色，优先使用数据库中的角色信息
  if (req.user.role === 'admin') {
    console.log('权限检查通过: 用户具有管理员角色');
    next();
  } else {
    console.log('权限检查失败: 用户不是管理员');
    // 返回更明确的错误信息
    return res.status(403).json({ 
      message: '需要管理员权限',
      details: '您当前账号没有管理员权限，请联系系统管理员授权'
    });
  }
}; 
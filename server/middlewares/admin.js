/**
 * 管理员权限验证中间件
 * 验证用户是否具有管理员权限
 */
const isAdmin = (req, res, next) => {
  try {
    console.log('开始验证管理员权限...');
    // 确保用户已经通过了认证中间件
    if (!req.user) {
      console.log('未找到用户信息，请求未经过认证');
      return res.status(401).json({ 
        message: '未授权访问，请先登录' 
      });
    }

    console.log('用户角色:', req.user.role);
    // 验证用户是否具有管理员角色
    if (req.user.role !== 'admin') {
      console.log('权限不足，用户角色不是admin');
      return res.status(403).json({ 
        message: '权限不足，需要管理员权限' 
      });
    }

    console.log('管理员权限验证成功');
    // 如果用户是管理员，继续下一步
    next();
  } catch (error) {
    console.error('权限验证错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
};

module.exports = { isAdmin }; 
const { body, validationResult } = require('express-validator');

/**
 * 返回验证结果中间件
 */
const validateResult = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

/**
 * 用户注册验证规则
 */
const registerValidation = [
  body('username')
    .notEmpty().withMessage('姓名不能为空')
    .isLength({ min: 2, max: 30 }).withMessage('姓名长度应在2-30个字符之间'),
  body('password')
    .notEmpty().withMessage('密码不能为空')
    .isLength({ min: 6 }).withMessage('密码长度至少为6个字符'),
  body('role')
    .optional()
    .isIn(['admin', 'user']).withMessage('角色必须是admin或user'),
  validateResult
];

/**
 * 用户登录验证规则
 */
const loginValidation = [
  body('username').notEmpty().withMessage('姓名不能为空'),
  body('password').notEmpty().withMessage('密码不能为空'),
  validateResult
];

/**
 * 商品添加验证规则
 */
const productValidation = [
  body('name').notEmpty().withMessage('商品名称不能为空'),
  body('categoryId').notEmpty().withMessage('商品类别不能为空').isInt().withMessage('商品类别ID必须是整数'),
  body('lyPrice').notEmpty().withMessage('光年币价格不能为空').isInt({ min: 0 }).withMessage('光年币价格必须是非负整数'),
  body('rmbPrice').notEmpty().withMessage('人民币价格不能为空').isFloat({ min: 0 }).withMessage('人民币价格必须是非负数'),
  body('stock').optional().isInt({ min: 0 }).withMessage('库存必须是非负整数'),
  body('isHot').optional().isBoolean().withMessage('是否热门必须是布尔值'),
  body('isNew').optional().isBoolean().withMessage('是否新品必须是布尔值'),
  body('status').optional().isIn(['active', 'inactive']).withMessage('状态必须是active或inactive'),
  validateResult
];

/**
 * 分类添加验证规则
 */
const categoryValidation = [
  body('name').notEmpty().withMessage('分类名称不能为空'),
  body('description').optional(),
  body('sortOrder').optional().isInt().withMessage('排序顺序必须是整数'),
  validateResult
];

/**
 * 公告添加验证规则
 */
const announcementValidation = [
  body('title').notEmpty().withMessage('公告标题不能为空'),
  body('content').notEmpty().withMessage('公告内容不能为空'),
  body('type').isIn(['新品', '促销', '系统更新']).withMessage('公告类型必须是新品、促销或系统更新'),
  body('status').optional().isIn(['active', 'inactive']).withMessage('状态必须是active或inactive'),
  validateResult
];

/**
 * 反馈添加验证规则
 */
const feedbackValidation = [
  body('content').notEmpty().withMessage('反馈内容不能为空'),
  validateResult
];

module.exports = {
  registerValidation,
  loginValidation,
  productValidation,
  categoryValidation,
  announcementValidation,
  feedbackValidation
}; 
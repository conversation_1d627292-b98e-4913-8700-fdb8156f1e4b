const compression = require('compression');

/**
 * 安全的响应压缩中间件
 * 只压缩文本类型的响应，不影响二进制文件
 */
const compressionMiddleware = compression({
  // 只压缩大于1KB的响应
  threshold: 1024,
  
  // 压缩级别（1-9，6是平衡点）
  level: 6,
  
  // 只压缩特定类型的内容
  filter: (req, res) => {
    // 如果响应头中包含no-transform，则不压缩
    if (req.headers['x-no-compression']) {
      return false;
    }
    
    // 只压缩文本类型的内容
    const contentType = res.getHeader('content-type');
    if (contentType) {
      return /text|json|javascript|css|xml|svg/.test(contentType);
    }
    
    // 默认使用compression的过滤器
    return compression.filter(req, res);
  },
  
  // 内存级别（1-9，8是默认值）
  memLevel: 8,
  
  // 窗口大小（9-15，15是默认值）
  windowBits: 15
});

module.exports = compressionMiddleware; 
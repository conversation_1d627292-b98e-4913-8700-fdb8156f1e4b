const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'feishu_mall',
  port: process.env.DB_PORT || 3306,
  multipleStatements: true
};

// 迁移文件路径
const migrationFiles = [
  path.join(__dirname, 'migrations', '20230615_create_message_templates.js'),
  path.join(__dirname, 'migrations', '20230616_create_sending_schedules.js'),
  path.join(__dirname, 'migrations', '20230617_create_notification_diagnostics.js'),
  path.join(__dirname, 'migrations', '20230618_update_notification_configs.js')
];

// 执行迁移
async function runMigrations() {
  console.log('开始执行Phase 3功能数据库迁移...');
  
  let connection;
  try {
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接已建立');

    // 依次执行迁移文件
    for (const filePath of migrationFiles) {
      console.log(`执行迁移文件: ${path.basename(filePath)}`);
      
      // 导入迁移文件
      const migration = require(filePath);
      
      // 模拟Sequelize环境
      const queryInterface = {
        createTable: async (tableName, attributes, options) => {
          console.log(`创建表: ${tableName}`);
          
          // 构建CREATE TABLE语句
          let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (`;
          
          // 添加字段
          const columns = [];
          for (const [columnName, columnDef] of Object.entries(attributes)) {
            let columnSql = `${columnName} `;
            
            // 数据类型映射
            if (columnDef.type.key === 'INTEGER') {
              columnSql += 'INT';
            } else if (columnDef.type.key === 'STRING') {
              const length = columnDef.type.options?.length || 255;
              columnSql += `VARCHAR(${length})`;
            } else if (columnDef.type.key === 'TEXT') {
              columnSql += 'TEXT';
            } else if (columnDef.type.key === 'BOOLEAN') {
              columnSql += 'TINYINT(1)';
            } else if (columnDef.type.key === 'DATE') {
              columnSql += 'DATETIME';
            } else if (columnDef.type.key === 'JSON') {
              columnSql += 'JSON';
            } else if (columnDef.type.key === 'ENUM') {
              const values = columnDef.type.values.map(v => `'${v}'`).join(', ');
              columnSql += `ENUM(${values})`;
            } else {
              columnSql += 'VARCHAR(255)';
            }
            
            // 主键
            if (columnDef.primaryKey) {
              columnSql += ' PRIMARY KEY';
            }
            
            // 自增
            if (columnDef.autoIncrement) {
              columnSql += ' AUTO_INCREMENT';
            }
            
            // 是否允许为空
            if (columnDef.allowNull === false) {
              columnSql += ' NOT NULL';
            }
            
            // 默认值
            if (columnDef.defaultValue !== undefined) {
              if (columnDef.defaultValue === null) {
                columnSql += ' DEFAULT NULL';
              } else if (typeof columnDef.defaultValue === 'string') {
                if (columnDef.defaultValue.includes('CURRENT_TIMESTAMP')) {
                  columnSql += ` DEFAULT ${columnDef.defaultValue}`;
                } else {
                  columnSql += ` DEFAULT '${columnDef.defaultValue}'`;
                }
              } else if (typeof columnDef.defaultValue === 'boolean') {
                columnSql += ` DEFAULT ${columnDef.defaultValue ? 1 : 0}`;
              } else {
                columnSql += ` DEFAULT ${columnDef.defaultValue}`;
              }
            }
            
            // 注释
            if (columnDef.comment) {
              columnSql += ` COMMENT '${columnDef.comment}'`;
            }
            
            columns.push(columnSql);
          }
          
          sql += columns.join(', ');
          
          // 表选项
          sql += ') ';
          
          // 字符集
          if (options?.charset) {
            sql += `CHARACTER SET ${options.charset} `;
          }
          
          // 排序规则
          if (options?.collate) {
            sql += `COLLATE ${options.collate} `;
          }
          
          // 表注释
          if (options?.comment) {
            sql += `COMMENT '${options.comment}' `;
          }
          
          // 执行SQL
          await connection.query(sql);
          console.log(`表 ${tableName} 创建成功`);
          
          // 处理索引，增加容错
          if (options?.indexes) {
            for (const index of options.indexes) {
              const indexName = index.name || `idx_${tableName}_${index.fields.join('_')}`;
              const unique = index.unique ? 'UNIQUE' : '';
              const fields = index.fields.join(', ');
              
              try {
                // 检查索引是否存在
                const [indices] = await connection.query(
                  `SHOW INDEX FROM ${tableName} WHERE Key_name = '${indexName}'`
                );
                
                if (indices.length === 0) {
                  const indexSql = `CREATE ${unique} INDEX ${indexName} ON ${tableName} (${fields})`;
                  await connection.query(indexSql);
                  console.log(`索引 ${indexName} 创建成功`);
                } else {
                  console.log(`索引 ${indexName} 已存在，跳过创建`);
                }
              } catch (error) {
                console.error(`创建索引 ${indexName} 时出错:`, error.message);
              }
            }
          }
        },
        
        addColumn: async (tableName, columnName, attributes) => {
          console.log(`向表 ${tableName} 添加字段 ${columnName}`);
          
          // 构建ALTER TABLE语句
          let sql = `ALTER TABLE ${tableName} ADD COLUMN ${columnName} `;
          
          // 数据类型
          if (attributes.type.key === 'INTEGER') {
            sql += 'INT';
          } else if (attributes.type.key === 'STRING') {
            const length = attributes.type.options?.length || 255;
            sql += `VARCHAR(${length})`;
          } else if (attributes.type.key === 'TEXT') {
            sql += 'TEXT';
          } else if (attributes.type.key === 'BOOLEAN') {
            sql += 'TINYINT(1)';
          } else if (attributes.type.key === 'DATE') {
            sql += 'DATETIME';
          } else if (attributes.type.key === 'JSON') {
            sql += 'JSON';
          } else {
            sql += 'VARCHAR(255)';
          }
          
          // 是否允许为空
          if (attributes.allowNull === false) {
            sql += ' NOT NULL';
          }
          
          // 默认值
          if (attributes.defaultValue !== undefined) {
            if (attributes.defaultValue === null) {
              sql += ' DEFAULT NULL';
            } else if (typeof attributes.defaultValue === 'string') {
              sql += ` DEFAULT '${attributes.defaultValue}'`;
            } else if (typeof attributes.defaultValue === 'boolean') {
              sql += ` DEFAULT ${attributes.defaultValue ? 1 : 0}`;
            } else {
              sql += ` DEFAULT ${attributes.defaultValue}`;
            }
          }
          
          // 注释
          if (attributes.comment) {
            sql += ` COMMENT '${attributes.comment}'`;
          }
          
          // 字段位置
          if (attributes.after) {
            sql += ` AFTER ${attributes.after}`;
          }
          
          // 执行SQL
          await connection.query(sql);
          console.log(`字段 ${columnName} 添加成功`);
        },
        
        addConstraint: async (tableName, options) => {
          console.log(`向表 ${tableName} 添加约束 ${options.name}`);
          
          let sql;
          if (options.type === 'foreign key') {
            sql = `ALTER TABLE ${tableName} ADD CONSTRAINT ${options.name} FOREIGN KEY (${options.fields.join(', ')}) REFERENCES ${options.references.table} (${options.references.field})`;
            
            if (options.onDelete) {
              sql += ` ON DELETE ${options.onDelete}`;
            }
            
            if (options.onUpdate) {
              sql += ` ON UPDATE ${options.onUpdate}`;
            }
          }
          
          // 执行SQL
          await connection.query(sql);
          console.log(`约束 ${options.name} 添加成功`);
        },
        
        addIndex: async (tableName, fields, options) => {
          const indexName = options.name || `idx_${tableName}_${fields.join('_')}`;
          const unique = options.unique ? 'UNIQUE' : '';
          
          console.log(`向表 ${tableName} 添加索引 ${indexName}`);
          
          const sql = `CREATE ${unique} INDEX ${indexName} ON ${tableName} (${fields.join(', ')})`;
          await connection.query(sql);
          console.log(`索引 ${indexName} 添加成功`);
        },
        
        bulkInsert: async (tableName, records) => {
          if (records.length === 0) {
            return;
          }
          
          console.log(`向表 ${tableName} 插入 ${records.length} 条记录`);
          
          // 构建INSERT语句
          const columns = Object.keys(records[0]);
          const values = records.map(record => 
            `(${columns.map(col => {
              const value = record[col];
              if (value === null) {
                return 'NULL';
              } else if (typeof value === 'string') {
                return `'${value.replace(/'/g, "''")}'`;
              } else if (typeof value === 'object') {
                if (value instanceof Date) {
                  return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                } else {
                  return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
                }
              } else {
                return value;
              }
            }).join(', ')})`
          ).join(', ');
          
          const sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES ${values}`;
          
          try {
            await connection.query(sql);
            console.log(`${records.length} 条记录插入成功`);
          } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
              console.log(`部分记录存在重复键，可能已经插入过。错误: ${error.message}`);
              
              // 逐条插入，跳过重复项
              let successCount = 0;
              for (const record of records) {
                try {
                  const columns = Object.keys(record);
                  const values = columns.map(col => {
                    const value = record[col];
                    if (value === null) {
                      return 'NULL';
                    } else if (typeof value === 'string') {
                      return `'${value.replace(/'/g, "''")}'`;
                    } else if (typeof value === 'object') {
                      if (value instanceof Date) {
                        return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                      } else {
                        return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
                      }
                    } else {
                      return value;
                    }
                  }).join(', ');
                  
                  const singleSql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values})`;
                  await connection.query(singleSql);
                  successCount++;
                } catch (err) {
                  if (err.code === 'ER_DUP_ENTRY') {
                    console.log(`记录存在重复键，跳过。错误: ${err.message}`);
                  } else {
                    throw err;
                  }
                }
              }
              
              console.log(`成功插入 ${successCount}/${records.length} 条记录`);
            } else {
              throw error;
            }
          }
        },
        
        sequelize: {
          query: async (sql, options = {}) => {
            console.log(`执行SQL: ${sql.slice(0, 100)}${sql.length > 100 ? '...' : ''}`);
            const [rows] = await connection.query(sql);
            return options.type === 'SELECT' ? rows : rows;
          },
          
          QueryTypes: {
            SELECT: 'SELECT'
          }
        },
        
        removeConstraint: async (tableName, constraintName) => {
          console.log(`从表 ${tableName} 移除约束 ${constraintName}`);
          
          const sql = `ALTER TABLE ${tableName} DROP CONSTRAINT ${constraintName}`;
          await connection.query(sql);
          console.log(`约束 ${constraintName} 移除成功`);
        },
        
        removeColumn: async (tableName, columnName) => {
          console.log(`从表 ${tableName} 移除字段 ${columnName}`);
          
          const sql = `ALTER TABLE ${tableName} DROP COLUMN ${columnName}`;
          await connection.query(sql);
          console.log(`字段 ${columnName} 移除成功`);
        },
        
        dropTable: async (tableName) => {
          console.log(`删除表 ${tableName}`);
          
          const sql = `DROP TABLE IF EXISTS ${tableName}`;
          await connection.query(sql);
          console.log(`表 ${tableName} 删除成功`);
        }
      };
      
      // 模拟Sequelize数据类型
      const Sequelize = {
        INTEGER: { key: 'INTEGER' },
        STRING: (length) => ({ key: 'STRING', options: { length } }),
        TEXT: { key: 'TEXT' },
        BOOLEAN: { key: 'BOOLEAN' },
        DATE: { key: 'DATE' },
        JSON: { key: 'JSON' },
        ENUM: (...values) => ({ key: 'ENUM', values }),
        literal: (value) => value
      };
      
      // 执行迁移的up方法
      await migration.up(queryInterface, Sequelize);
      console.log(`迁移文件 ${path.basename(filePath)} 执行成功`);
    }
    
    console.log('所有迁移执行完成');
  } catch (error) {
    console.error('迁移过程中出错:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行迁移
runMigrations(); 
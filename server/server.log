====== 服务器启动环境变量 ======
NODE_ENV: production
SERVER_URL: http://**************
FEISHU_REDIRECT_URI: http://**************/api/feishu/callback
====== 环境变量输出结束 ======
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码图片目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
图片上传目录权限正常
静态文件路径1: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
静态文件路径2: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
上传目录绝对路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
=== 飞书配置加载 ===
NODE_ENV: production
FEISHU_REDIRECT_URI: http://**************/api/feishu/callback
process.env: {
  NODE_ENV: 'production',
  PORT: '3000',
  FEISHU_APP_ID: 'cli_a66b3b2dcab8d013',
  FEISHU_APP_SECRET: '5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r',
  FEISHU_REDIRECT_URI: 'http://**************/api/feishu/callback'
}
计算得到的redirectUri: http://**************/api/feishu/callback
node:events:502
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1937:16)
    at listenInCluster (node:net:1994:12)
    at Server.listen (node:net:2099:7)
    at Function.listen (/Users/<USER>/Desktop/chattywork/workyy/server/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/Users/<USER>/Desktop/chattywork/workyy/server/server.js:359:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1973:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.13.1

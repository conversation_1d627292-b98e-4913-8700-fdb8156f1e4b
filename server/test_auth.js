/**
 * JWT认证测试脚本
 * 用于验证JWT生成和权限检查是否正常工作
 */

const jwt = require('jsonwebtoken');
const config = require('./config/config');
const { User } = require('./models');
const { generateToken } = require('./utils/jwt');

// 模拟请求对象
const req = {
  user: null,
  headers: {
    authorization: null
  }
};

// 模拟响应对象
const res = {
  status(code) {
    console.log(`响应状态码: ${code}`);
    return this;
  },
  json(data) {
    console.log('响应数据:', data);
    return this;
  }
};

// 下一步函数
const next = () => {
  console.log('中间件通过');
};

async function runTest() {
  console.log('===== JWT权限测试开始 =====');
  
  try {
    // 1. 创建测试用户
    const adminUser = {
      id: 999,
      username: 'admin_test',
      role: 'admin'
    };
    
    const normalUser = {
      id: 998,
      username: 'user_test',
      role: 'user'
    };
    
    // 2. 生成JWT令牌
    console.log('\n1. 生成管理员令牌');
    const adminToken = generateToken(adminUser);
    console.log('管理员令牌:', `${adminToken.substring(0, 20)}...`);
    
    console.log('\n2. 生成普通用户令牌');
    const userToken = generateToken(normalUser);
    console.log('普通用户令牌:', `${userToken.substring(0, 20)}...`);
    
    // 3. 解码和验证令牌
    console.log('\n3. 解码管理员令牌');
    const decodedAdmin = jwt.verify(adminToken, config.jwt.secret);
    console.log('解码结果:', {
      id: decodedAdmin.id,
      username: decodedAdmin.username,
      role: decodedAdmin.role,
      isAdmin: decodedAdmin.isAdmin
    });
    
    console.log('\n4. 解码普通用户令牌');
    const decodedUser = jwt.verify(userToken, config.jwt.secret);
    console.log('解码结果:', {
      id: decodedUser.id,
      username: decodedUser.username,
      role: decodedUser.role,
      isAdmin: decodedUser.isAdmin
    });
    
    // 4. 测试中间件
    console.log('\n5. 测试管理员权限中间件');
    const { adminMiddleware } = require('./middlewares/authMiddleware');
    
    // 管理员用户
    req.user = {
      id: adminUser.id,
      username: adminUser.username,
      role: adminUser.role,
      isAdmin: true
    };
    
    console.log('管理员用户权限检查:');
    adminMiddleware(req, res, next);
    
    // 普通用户
    req.user = {
      id: normalUser.id,
      username: normalUser.username,
      role: normalUser.role,
      isAdmin: false
    };
    
    console.log('\n普通用户权限检查:');
    adminMiddleware(req, res, next);
    
    console.log('\n===== JWT权限测试完成 =====');
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 运行测试
runTest().catch(console.error); 
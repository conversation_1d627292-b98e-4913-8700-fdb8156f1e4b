const { sequelize } = require('./config/database');
const fs = require('fs');
const path = require('path');

// 职场迁移文件
const migrations = [
  '20230701_create_workplaces_table.js',
  '20230702_add_workplaceId_to_users.js',
  '20230703_add_workplaceId_to_exchanges.js'
];

async function runMigrations() {
  console.log('开始执行职场相关迁移...');
  
  try {
    // 逐个执行迁移
    for (const migrationName of migrations) {
      console.log(`执行迁移: ${migrationName}`);
      
      // 检查迁移是否已执行
      const [existingMigration] = await sequelize.query(
        'SELECT name FROM SequelizeMeta WHERE name = ?',
        {
          replacements: [migrationName],
          type: sequelize.QueryTypes.SELECT
        }
      );
      
      if (existingMigration) {
        console.log(`迁移 ${migrationName} 已经执行过，跳过`);
        continue;
      }
      
      // 加载迁移文件
      const migrationPath = path.join(__dirname, 'migrations', migrationName);
      const migration = require(migrationPath);
      
      // 执行迁移的up方法
      await migration.up();
      
      // 记录到SequelizeMeta表
      await sequelize.query(
        'INSERT INTO SequelizeMeta (name) VALUES (?)',
        {
          replacements: [migrationName],
          type: sequelize.QueryTypes.INSERT
        }
      );
      
      console.log(`迁移 ${migrationName} 执行成功`);
    }
    
    console.log('所有职场相关迁移已成功执行');
    
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    // 关闭连接
    await sequelize.close();
  }
}

// 运行迁移
runMigrations()
  .then(() => {
    console.log('迁移脚本执行完毕');
    process.exit(0);
  })
  .catch(err => {
    console.error('迁移脚本执行出错:', err);
    process.exit(1);
  }); 
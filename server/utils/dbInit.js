const { sequelize } = require('../config/database');
const { User, Category, Product } = require('../models');

/**
 * 初始化数据库
 * 创建表并添加初始数据
 */
const initDatabase = async () => {
  try {
    // 禁用外键检查以解决外键约束问题
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
    
    // 同步所有模型到数据库
    await sequelize.sync({ force: true });
    console.log('数据库同步完成');
    
    // 重新启用外键检查
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 1');

    // 创建管理员用户
    const adminUser = await User.create({
      username: 'admin',
      password: 'admin123',
      role: 'admin',
      points: 1000
    });

    console.log('管理员用户创建成功:', adminUser.username);

    // 创建初始分类
    const categories = await Category.bulkCreate([
      { name: '解压玩具类', description: '帮助缓解压力的玩具', sortOrder: 1 },
      { name: '盲盒类', description: '神秘的盲盒产品', sortOrder: 2 },
      { name: '定制手工制品类', description: '独特的定制手工产品', sortOrder: 3 },
      { name: '杯具类', description: '各种杯子和饮水工具', sortOrder: 4 },
      { name: '日常用品类', description: '日常生活必需品', sortOrder: 5 },
      { name: '家居用品类', description: '提升家居体验的产品', sortOrder: 6 },
      { name: '多功能用品类', description: '多用途的实用产品', sortOrder: 7 },
      { name: '出行用品类', description: '旅行和出行相关产品', sortOrder: 8 }
    ]);

    console.log('初始分类创建成功:', categories.length);

    // 创建一些初始商品作为示例
    const products = await Product.bulkCreate([
      {
        name: '发财解压按键',
        categoryId: 1, // 解压玩具类
        lyPrice: 5,
        rmbPrice: 5,
        description: '这是一款创意十足的解压神器，外形酷似发财按键。每次按下都会发出清脆的声响，帮助你缓解工作压力，带来好运和好心情。',
        stock: 100,
        isHot: false,
        isNew: true
      },
      {
        name: '植物盲盒',
        categoryId: 2, // 盲盒类
        lyPrice: 10,
        rmbPrice: 9.9,
        description: '神秘的植物盲盒，内含精选绿植。打开盲盒，为你的工位增添一抹绿意，提升工作环境的舒适度。每个盲盒都是一份独特的惊喜！',
        stock: 50,
        isHot: true,
        isNew: false
      }
    ]);

    console.log('初始商品创建成功:', products.length);

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化错误:', error);
  } finally {
    process.exit();
  }
};

// 执行初始化
initDatabase(); 
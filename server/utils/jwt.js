const jwt = require('jsonwebtoken');
const config = require('../config/config');

/**
 * 生成JWT令牌
 * @param {Object} user - 用户对象
 * @param {boolean} rememberMe - 是否记住登录状态
 * @returns {string} - JWT令牌
 */
const generateToken = (user, rememberMe = false) => {
  console.log('生成JWT令牌...');
  console.log('用户ID:', user.id);
  console.log('用户名:', user.username);
  console.log('用户角色:', user.role);
  console.log('记住我:', rememberMe);
  
  // 创建令牌负载
  const payload = { 
    id: user.id,
    username: user.username,
    role: user.role,
    isAdmin: user.role === 'admin'
  };
  
  console.log('JWT负载内容:', JSON.stringify(payload, null, 2));
  
  // 设置过期时间：如果记住我，则使用长期过期时间，否则使用短期过期时间
  const expiresIn = rememberMe ? config.jwt.longExpiresIn : config.jwt.expiresIn;
  
  // 使用密钥签名
  const token = jwt.sign(
    payload,
    config.jwt.secret,
    { expiresIn }
  );
  
  console.log('JWT令牌生成成功，过期时间:', expiresIn);
  return token;
};

/**
 * 验证JWT令牌
 * @param {string} token - JWT令牌
 * @returns {Object} - 解码后的令牌负载
 */
const verifyToken = (token) => {
  return jwt.verify(token, config.jwt.secret);
};

module.exports = {
  generateToken,
  verifyToken
}; 
/**
 * 拼音排序工具
 * 提供基于中文拼音的排序功能
 */
const pinyin = require('pinyin');
const nodejieba = require('nodejieba');

// 加载nodejieba词典
nodejieba.load({
  userDict: null // 可以在这里添加自定义词典路径
});

/**
 * 获取一个字符串的拼音首字母数组
 * @param {String} str 要转成拼音的字符串
 * @param {Boolean} onlyFirst 是否只返回拼音首字母
 * @returns {String} 拼音字符串
 */
function getPinyin(str, onlyFirst = false) {
  if (!str) return '';
  
  try {
    // 适配pinyin 4.x版本API
    const style = onlyFirst ? "first_letter" : "normal";
    
    // 先对字符串进行分词处理
    const words = nodejieba.cut(str);
    
    // 对分词结果分别转为拼音，这样可以更好地处理多音字
    const pinyinResults = words.map(word => {
      const result = pinyin(word, {
        style: style,
        heteronym: false
      });
      // 将二维数组转为字符串
      return result.map(item => item[0]).join('');
    });
    
    // 将所有拼音结果连接起来
    return pinyinResults.join('');
  } catch (error) {
    console.error('拼音转换失败:', error);
    
    // 出错时使用简单方法尝试转换
    try {
      const style = onlyFirst ? "first_letter" : "normal";
      const result = pinyin(str, {
        style: style,
        heteronym: false
      });
      return result.map(item => item[0]).join('');
    } catch (fallbackError) {
      console.error('备用拼音转换也失败:', fallbackError);
      // 实在不行，就返回原字符串
      return str;
    }
  }
}

/**
 * 根据拼音对中文数组进行排序
 * @param {Array} arr 要排序的中文数组
 * @param {Object} options 排序选项
 * @returns {Array} 排序后的数组
 */
function sortByPinyin(arr, options = {}) {
  const { field, onlyFirst = false, descending = false } = options;
  
  // 如果数组为空，直接返回
  if (!arr || !arr.length) {
    return arr;
  }

  // 创建一个排序用的临时数组，包含原始数据和对应的拼音
  const tempArr = arr.map(item => {
    const value = field ? item[field] : item;
    return {
      original: item,
      pinyin: getPinyin(String(value), onlyFirst)
    };
  });

  // 根据拼音进行排序
  tempArr.sort((a, b) => {
    const result = a.pinyin.localeCompare(b.pinyin, 'zh-CN');
    return descending ? -result : result;
  });

  // 返回排序后的原始数据
  return tempArr.map(item => item.original);
}

/**
 * 用于商品名称排序时的比较函数
 * @param {String} a 第一个商品名称
 * @param {String} b 第二个商品名称
 * @returns {Number} 比较结果
 */
function productNameCompare(a, b) {
  return getPinyin(a).localeCompare(getPinyin(b), 'zh-CN');
}

/**
 * 对商品数据按拼音进行排序
 * @param {Array} products 商品数据数组
 * @param {String} direction 排序方向 'asc' 或 'desc'
 * @returns {Array} 排序后的商品数组
 */
function sortProductsByPinyin(products, direction = 'asc') {
  if (!products || !products.length) return products;
  
  const isDescending = direction === 'desc';
  
  return sortByPinyin(products, {
    field: 'name',
    descending: isDescending
  });
}

module.exports = {
  getPinyin,
  sortByPinyin,
  productNameCompare,
  sortProductsByPinyin
}; 
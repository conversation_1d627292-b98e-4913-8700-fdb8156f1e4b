# 光年小卖部 - 后端生产环境配置

# 服务器配置
PORT=3000
NODE_ENV=production
SERVER_URL=http://**************

# 数据库配置
DB_NAME=feishu_mall
DB_USER=root
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=3306

# JWT配置
JWT_SECRET=JO/Ssvef59AR5zFMx5m/MGMin34aMPT0KY6sIcqwowA=
JWT_EXPIRES_IN=1d
JWT_LONG_EXPIRES_IN=30d

# 上传目录配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# CORS配置 - 允许前端80端口访问
CORS_ORIGIN=http://**************

# 飞书应用配置 - 使用当前开发环境的应用ID（已申请手机号权限）
FEISHU_APP_ID=cli_a66b3b2dcab8d013
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
FEISHU_REDIRECT_URI=http://**************/api/feishu/callback

# 飞书群机器人Webhook配置
FEISHU_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc

# 前端URL配置
FRONTEND_URL=http://**************

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// 定义日志操作类型常量
const LOG_ACTIONS = {
  // 用户相关
  USER_LOGIN: 'user_login',
  USER_LOGOUT: 'user_logout',
  USER_REGISTER: 'user_register',
  USER_UPDATE: 'user_update',
  USER_DELETE: 'user_delete',
  USER_PASSWORD_CHANGE: 'user_password_change',
  USER_BULK_DELETE: 'user_bulk_delete',
  
  // 商品相关
  PRODUCT_CREATE: 'product_create',
  PRODUCT_UPDATE: 'product_update',
  PRODUCT_DELETE: 'product_delete',
  PRODUCT_STATUS_CHANGE: 'product_status_change',
  
  // 库存相关
  STOCK_UPDATE: 'stock_update',
  
  // 交易相关
  EXCHANGE_CREATE: 'exchange_create',
  EXCHANGE_STATUS_UPDATE: 'exchange_status_update',
  EXCHANGE_CANCEL: 'exchange_cancel',
  
  // 文件相关
  FILE_UPLOAD: 'file_upload',
  FILE_DELETE: 'file_delete',
  
  // 数据相关
  DATA_IMPORT: 'data_import',
  DATA_EXPORT: 'data_export',
  
  // 系统相关
  SYSTEM_LOGS_CLEANUP: 'system_logs_cleanup',
  
  // 职场相关
  WORKPLACE_CREATE: 'workplace_create',
  WORKPLACE_UPDATE: 'workplace_update',
  WORKPLACE_DELETE: 'workplace_delete'
};

// 定义实体类型常量
const ENTITY_TYPES = {
  USER: 'user',
  PRODUCT: 'product',
  EXCHANGE: 'exchange',
  STOCK: 'stock',
  FILE: 'file',
  SYSTEM: 'system',
  WORKPLACE: 'workplace'
};

const Log = sequelize.define('Log', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  action: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '操作类型，如：user_login, product_create, product_status_change等'
  },
  entityType: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '实体类型，如：user, product, exchange等'
  },
  entityId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '相关实体ID'
  },
  oldValue: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '操作前的值（JSON格式）'
  },
  newValue: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '操作后的值（JSON格式）'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '操作用户ID，如果有'
  },
  username: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '操作姓名，方便查询不需要关联'
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '操作者IP地址'
  },
  deviceInfo: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '设备信息，如浏览器、操作系统等'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '操作描述'
  }
}, {
  tableName: 'logs',
  timestamps: true,
  indexes: [
    {
      name: 'idx_logs_action',
      fields: ['action']
    },
    {
      name: 'idx_logs_entityType_entityId',
      fields: ['entityType', 'entityId']
    },
    {
      name: 'idx_logs_userId',
      fields: ['userId']
    },
    {
      name: 'idx_logs_createdAt',
      fields: ['createdAt']
    }
  ]
});

// 添加日志常量到模型上，方便引用
Log.ACTIONS = LOG_ACTIONS;
Log.ENTITY_TYPES = ENTITY_TYPES;

module.exports = Log; 
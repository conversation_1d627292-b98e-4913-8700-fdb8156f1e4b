'use strict';
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define('Notification', {
  type: {
    type: DataTypes.ENUM('exchange', 'feedback', 'stock_alert', 'product'),
    allowNull: false,
    comment: '通知类型(exchange、feedback、stock_alert或product)'
  },
  sourceId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联的数据ID(兑换、反馈或商品ID)'
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '通知标题'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '通知内容'
  },
  isRead: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已读'
  },
  recipientId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '接收者ID'
  }
}, {
  tableName: 'notifications',
  timestamps: true
});

module.exports = Notification; 
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 30]
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  role: {
    type: DataTypes.ENUM('admin', 'user'),
    allowNull: false,
    defaultValue: 'user'
  },
  department: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: null
  },
  departmentPath: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: null,
    comment: '用户完整部门路径，如"公司/技术部/后端组"'
  },
  mobile: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: null,
    comment: '用户手机号码'
  },
  workplace: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: null,
    comment: '旧职场字段，将被workplaceId替代'
  },
  workplaceId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'workplaces',
      key: 'id'
    },
    comment: '关联到workplaces表的外键'
  },
  feishuOpenId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '飞书用户的OpenID'
  },
  feishuUnionId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '飞书用户的UnionID'
  },
  feishuUserId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '飞书用户ID'
  },
  feishuAvatar: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '飞书用户头像URL'
  },
  feishuAccessToken: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '飞书访问令牌'
  },
  feishuRefreshToken: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '飞书刷新令牌'
  },
  feishuTokenExpireTime: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '飞书令牌过期时间'
  },
  authType: {
    type: DataTypes.ENUM('password', 'feishu'),
    allowNull: false,
    defaultValue: 'password',
    comment: '认证类型：密码或飞书'
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '用户光年币数量'
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后登录时间'
  },
  lastLoginIp: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '最后登录IP地址'
  }
}, {
  tableName: 'users',
  timestamps: true,
  hooks: {
    beforeCreate: async (user) => {
      if (user.password && user.authType === 'password') {
        try {
          console.log(`正在加密密码，长度: ${user.password.length}`);
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
          console.log(`密码加密成功，哈希长度: ${user.password.length}`);
        } catch (error) {
          console.error('密码加密错误:', error);
          throw new Error('密码加密失败');
        }
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password') && user.authType === 'password') {
        try {
          console.log(`正在更新加密密码，长度: ${user.password.length}`);
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
          console.log(`密码更新加密成功，哈希长度: ${user.password.length}`);
        } catch (error) {
          console.error('密码更新加密错误:', error);
          throw new Error('密码更新加密失败');
        }
      }
    }
  }
});

// 实例方法：验证密码
User.prototype.comparePassword = async function(candidatePassword) {
  try {
    console.log('====== 密码比较详情 ======');
    console.log(`用户: ${this.username}`);
    console.log(`提供的密码: ${candidatePassword ? '******' : '空'}`);
    console.log(`提供的密码长度: ${candidatePassword ? candidatePassword.length : 0}`);
    console.log(`存储的哈希: ${this.password ? this.password.substring(0, 20) + '...' : '无'}`);
    
    if (this.authType === 'feishu' && (!this.password || this.password === '')) {
      console.log('飞书登录用户，跳过密码比较');
      return false;
    }
    
    if (!this.password) {
      console.error('错误: 用户记录缺少密码哈希');
      return false;
    }
    
    if (!candidatePassword) {
      console.error('错误: 提供的密码为空');
      return false;
    }
    
    const isMatch = await bcrypt.compare(candidatePassword, this.password);
    
    console.log(`比较结果: ${isMatch ? '匹配' : '不匹配'}`);
    
    if (!isMatch) {
      const testHash = await bcrypt.hash(candidatePassword, 10);
      console.log(`临时哈希: ${testHash}`);
      console.log(`备用比较: ${testHash === this.password ? '匹配' : '不匹配'}`);
    }
    
    console.log('====== 密码比较完成 ======');
    return isMatch;
  } catch (error) {
    console.error('密码比较过程中出现错误:', error);
    throw error;
  }
};

module.exports = User; 
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const NotificationDiagnostic = sequelize.define('NotificationDiagnostic', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  diagnosticType: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'diagnostic_type'
  },
  status: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  details: {
    type: DataTypes.JSON,
    allowNull: true
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message'
  },
  resolvedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'resolved_at'
  },
  resolution: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'notification_diagnostics',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    {
      fields: ['diagnostic_type', 'status'],
      name: 'idx_diagnostic_status'
    },
    {
      fields: ['created_at'],
      name: 'idx_created_at'
    }
  ]
});

module.exports = NotificationDiagnostic; 
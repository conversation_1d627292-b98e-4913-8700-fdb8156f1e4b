const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const NotificationLog = sequelize.define('NotificationLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  notificationType: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'notification_type',
    comment: '通知类型'
  },
  webhookUrl: {
    type: DataTypes.STRING(500),
    allowNull: true,
    field: 'webhook_url',
    comment: '发送的webhook地址'
  },
  requestPayload: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'request_payload',
    comment: '发送的消息内容(JSON格式)',
    get() {
      const value = this.getDataValue('requestPayload');
      return value ? JSON.parse(value) : null;
    },
    set(value) {
      this.setDataValue('requestPayload', value ? JSON.stringify(value) : null);
    }
  },
  responseStatus: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'response_status',
    comment: 'HTTP响应状态码'
  },
  responseBody: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'response_body',
    comment: '响应内容(JSON格式)',
    get() {
      const value = this.getDataValue('responseBody');
      return value ? JSON.parse(value) : null;
    },
    set(value) {
      this.setDataValue('responseBody', value ? JSON.stringify(value) : null);
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'success', 'failed'),
    defaultValue: 'pending',
    comment: '发送状态'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message',
    comment: '错误信息'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'retry_count',
    comment: '重试次数'
  },
  maxRetries: {
    type: DataTypes.INTEGER,
    defaultValue: 3,
    field: 'max_retries',
    comment: '最大重试次数'
  },
  nextRetryAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'next_retry_at',
    comment: '下次重试时间'
  },
  sentAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'sent_at',
    comment: '实际发送时间'
  },
  responseTime: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'response_time',
    comment: '响应时间(毫秒)'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by',
    comment: '触发者用户ID'
  },
  triggerSource: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'trigger_source',
    comment: '触发源(auto/manual/test)'
  }
}, {
  tableName: 'notification_logs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['notification_type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['next_retry_at']
    }
  ]
});

module.exports = NotificationLog; 
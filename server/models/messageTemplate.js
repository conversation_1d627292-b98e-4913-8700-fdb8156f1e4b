const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const MessageTemplate = sequelize.define('MessageTemplate', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  templateName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'template_name'
  },
  templateCode: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'template_code'
  },
  notificationType: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'notification_type'
  },
  templateType: {
    type: DataTypes.ENUM('text', 'card', 'rich'),
    defaultValue: 'card',
    field: 'template_type'
  },
  templateContent: {
    type: DataTypes.TEXT,
    allowNull: false,
    field: 'template_content'
  },
  variables: {
    type: DataTypes.TEXT,
    get() {
      const rawValue = this.getDataValue('variables');
      return rawValue ? JSON.parse(rawValue) : [];
    },
    set(value) {
      this.setDataValue('variables', JSON.stringify(value));
    }
  },
  description: {
    type: DataTypes.STRING(200),
    allowNull: true
  },
  isDefault: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_default'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  usageCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'usage_count'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by'
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'updated_by'
  }
}, {
  tableName: 'message_templates',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = MessageTemplate;
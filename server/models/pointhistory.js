'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class PointHistory extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  PointHistory.init({
    userId: DataTypes.INTEGER,
    amount: DataTypes.INTEGER,
    balance: DataTypes.INTEGER,
    type: DataTypes.ENUM,
    description: DataTypes.STRING,
    relatedId: DataTypes.INTEGER,
    relatedType: DataTypes.STRING
  }, {
    sequelize,
    modelName: 'PointHistory',
  });
  return PointHistory;
};
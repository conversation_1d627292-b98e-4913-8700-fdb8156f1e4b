const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Exchange = sequelize.define('Exchange', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '格式化的订单编号'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1
    }
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '订单总金额'
  },
  paymentMethod: {
    type: DataTypes.ENUM('ly', 'rmb'),
    allowNull: false,
    comment: '支付方式：ly(光年币)或rmb(人民币)'
  },
  contactInfo: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '联系信息'
  },
  location: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '用户所在职场位置（旧字段，将被workplaceId替代）'
  },
  workplaceId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'workplaces',
      key: 'id'
    },
    comment: '关联到workplaces表的外键'
  },
  remarks: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户备注'
  },
  paymentProofUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '支付凭证图片URL'
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'shipped', 'completed', 'rejected', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态：待处理、已批准、已发货、已完成、已拒绝、已取消'
  },
  adminRemarks: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '管理员备注'
  },
  trackingNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '物流单号'
  },
  trackingCompany: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '物流公司'
  },
  totalIncome: {
    type: DataTypes.VIRTUAL,
    get() {
      // 如果已有totalAmount字段，优先使用
      if (this.getDataValue('totalAmount') && this.getDataValue('totalAmount') > 0) {
        return parseFloat(this.getDataValue('totalAmount'));
      }
      
      // 向后兼容的计算逻辑
      const quantity = this.getDataValue('quantity') || 1;
      if (this.Product) {
        if (this.getDataValue('paymentMethod') === 'ly') {
          return this.Product.lyPrice * quantity;
        } else {
          return this.Product.rmbPrice * quantity;
        }
      }
      return 0;
    }
  }
}, {
  tableName: 'exchanges',
  timestamps: true,
  hooks: {
    beforeCreate: async (exchange, options) => {
      try {
        // 自动计算订单总金额
        const Product = sequelize.models.Product;
        if (!Product) {
          console.error('找不到Product模型，无法计算总金额');
          return;
        }
        
        const product = await Product.findByPk(exchange.productId);
        if (product) {
          if (exchange.paymentMethod === 'ly') {
            exchange.totalAmount = product.lyPrice * exchange.quantity;
          } else {
            exchange.totalAmount = parseFloat(product.rmbPrice) * exchange.quantity;
          }
        }

        // 记录创建操作
        console.log(`开始创建订单，支付方式: ${exchange.paymentMethod}`);

        // 不预先生成orderNumber，等待afterCreate钩子获取正确的ID后再生成
        // 这是因为Sequelize的beforeCreate钩子中无法获取到即将分配的ID
      } catch (error) {
        console.error('处理订单创建前钩子失败:', error);
      }
    },
    afterCreate: async (exchange, options) => {
      try {
        // 直接通过数据库实例查询，避免循环依赖
        const Product = sequelize.models.Product;
        if (!Product) {
          console.error('找不到Product模型，无法更新库存');
          return;
        }
        
        const product = await Product.findByPk(exchange.productId);
        if (product) {
          // 减少库存
          await product.update({
            stock: Math.max(0, product.stock - exchange.quantity),
            exchangeCount: (product.exchangeCount || 0) + exchange.quantity
          });
        }
        
        // 生成订单编号
        if (!exchange.orderNumber) {
          console.log(`开始为订单 ID=${exchange.id} 生成订单编号`);
          
          const today = new Date();
          const dateStr = today.getFullYear().toString() + 
                         (today.getMonth() + 1).toString().padStart(2, '0') + 
                         today.getDate().toString().padStart(2, '0');
          
          const prefix = exchange.paymentMethod === 'ly' ? 'GNB-' : 'RMB-';
          
          // 使用数据库查询获取当前最大ID，确保序列号是正确的
          let sequenceNumber = exchange.id;
          
          // 确保序列号与ID一致
          console.log(`使用订单ID作为序列号: ID=${exchange.id}, 序列号=${sequenceNumber}`);
          
          // 生成8位序列号
          const sequenceStr = sequenceNumber.toString().padStart(8, '0');
          const orderNumber = prefix + dateStr + sequenceStr;
          console.log(`生成订单编号: ${orderNumber}`);
          
          try {
            // 使用原始SQL查询直接更新数据库，避免可能的ORM缓存问题
            await sequelize.query(
              'UPDATE exchanges SET orderNumber = ? WHERE id = ?',
              {
                replacements: [orderNumber, exchange.id],
                type: sequelize.QueryTypes.UPDATE
              }
            );
            
            // 同时更新当前实例的orderNumber
            exchange.orderNumber = orderNumber;
            
            console.log(`成功更新订单 ID=${exchange.id} 的订单编号: ${orderNumber}`);
          } catch (updateError) {
            console.error(`更新订单 ID=${exchange.id} 的订单编号时出错:`, updateError);
            
            // 再次尝试使用模型方法更新
            try {
              await exchange.update({ orderNumber }, { hooks: false });
              console.log(`成功使用备用方法更新订单 ID=${exchange.id} 的订单编号: ${orderNumber}`);
            } catch (secondError) {
              console.error(`第二次尝试更新订单 ID=${exchange.id} 的订单编号时出错:`, secondError);
            }
          }
        } else {
          console.log(`订单 ID=${exchange.id} 已有订单编号: ${exchange.orderNumber}`);
        }
      } catch (error) {
        console.error('更新商品库存或订单编号失败:', error);
      }
    }
  }
});

module.exports = Exchange; 
const { sequelize } = require('../config/database');
const User = require('./user');
const Category = require('./category');
const Product = require('./product');
const ProductImage = require('./productImage');
const Announcement = require('./announcement');
const Exchange = require('./exchange');
const Feedback = require('./feedback');
const Log = require('./log');
const Notification = require('./notification');
const Workplace = require('./workplace');
const NotificationConfig = require('./notificationConfig');
const NotificationLog = require('./notificationLog');

// 定义关联关系
// 用户与反馈的关系：一对多（用户提交的反馈）
User.hasMany(Feedback, { foreignKey: 'userId', as: 'userFeedbacks' });
Feedback.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// 注释掉已被删除的字段关联，避免在查询时产生错误
// 用户与反馈的关系：一对多（用户回复的反馈）
// User.hasMany(Feedback, { foreignKey: 'respondedBy', as: 'respondedFeedbacks' });
// Feedback.belongsTo(User, { foreignKey: 'respondedBy', as: 'responder' });

// 用户与公告的关系：一对多（创建者）
User.hasMany(Announcement, { foreignKey: 'createdBy', as: 'createdAnnouncements' });
Announcement.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

// 用户与兑换记录的关系：一对多
User.hasMany(Exchange, { foreignKey: 'userId' });
Exchange.belongsTo(User, { foreignKey: 'userId' });

// 分类与商品的关系：一对多
Category.hasMany(Product, { foreignKey: 'categoryId' });
Product.belongsTo(Category, { foreignKey: 'categoryId' });

// 商品与商品图片的关系：一对多
Product.hasMany(ProductImage, { foreignKey: 'productId' });
ProductImage.belongsTo(Product, { foreignKey: 'productId' });

// 商品与兑换记录的关系：一对多
Product.hasMany(Exchange, { foreignKey: 'productId' });
Exchange.belongsTo(Product, { foreignKey: 'productId' });

// 用户与通知的关系：一对多
User.hasMany(Notification, { foreignKey: 'recipientId' });
Notification.belongsTo(User, { foreignKey: 'recipientId' });

// 职场与用户的关系：一对多
Workplace.hasMany(User, { foreignKey: 'workplaceId' });
User.belongsTo(Workplace, { foreignKey: 'workplaceId' });

// 职场与订单的关系：一对多
Workplace.hasMany(Exchange, { foreignKey: 'workplaceId' });
Exchange.belongsTo(Workplace, { foreignKey: 'workplaceId' });

module.exports = {
  sequelize,
  User,
  Category,
  Product,
  ProductImage,
  Announcement,
  Exchange,
  Feedback,
  Log,
  Notification,
  Workplace,
  NotificationConfig,
  NotificationLog
}; 
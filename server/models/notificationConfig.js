const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const MessageTemplate = require('./messageTemplate');
const SendingSchedule = require('./sendingSchedule');

const NotificationConfig = sequelize.define('NotificationConfig', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  notificationType: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'notification_type',
    comment: '通知类型'
  },
  enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否启用'
  },
  webhookUrl: {
    type: DataTypes.STRING(500),
    allowNull: true,
    field: 'webhook_url',
    comment: 'webhook地址'
  },
  scheduleTime: {
    type: DataTypes.STRING(20),
    allowNull: true,
    field: 'schedule_time',
    comment: '定时发送时间'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    defaultValue: 3,
    field: 'retry_count',
    comment: '重试次数'
  },
  templateId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'template_id',
    comment: '关联的模板ID'
  },
  scheduleId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'schedule_id',
    comment: '关联的调度ID'
  },
  advancedSettings: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'advanced_settings',
    comment: '高级设置'
  }
}, {
  tableName: 'notification_configs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['notification_type']
    }
  ]
});

// 关联模板
NotificationConfig.belongsTo(MessageTemplate, {
  foreignKey: 'template_id',
  as: 'template'
});

// 关联调度
NotificationConfig.belongsTo(SendingSchedule, {
  foreignKey: 'schedule_id',
  as: 'schedule'
});

module.exports = NotificationConfig; 
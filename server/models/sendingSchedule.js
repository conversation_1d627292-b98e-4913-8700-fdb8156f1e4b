const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SendingSchedule = sequelize.define('SendingSchedule', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  scheduleName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'schedule_name'
  },
  notificationType: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'notification_type'
  },
  scheduleType: {
    type: DataTypes.ENUM('fixed', 'smart', 'conditional'),
    defaultValue: 'fixed',
    field: 'schedule_type'
  },
  cronExpression: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'cron_expression'
  },
  timeWindows: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'time_windows'
  },
  conditions: {
    type: DataTypes.JSON,
    allowNull: true
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'sending_schedules',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['notification_type', 'schedule_type'],
      name: 'unique_notification_schedule'
    }
  ]
});

module.exports = SendingSchedule; 
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Announcement = sequelize.define('Announcement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  content: {
    type: DataTypes.TEXT('long'),
    allowNull: false,
    validate: {
      notEmpty: true
    },
    comment: '公告内容（可能是JSON格式或普通文本）'
  },
  contentHtml: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
    comment: '公告HTML内容（渲染后的HTML）',
    get() {
      const contentHtmlVal = this.getDataValue('contentHtml');
      if (contentHtmlVal === null || contentHtmlVal === undefined) {
        return this.getDataValue('content');
      }
      return contentHtmlVal;
    }
  },
  type: {
    type: DataTypes.ENUM('新品', '促销', '系统更新'),
    allowNull: false,
    defaultValue: '系统更新'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active'
  },
  imageUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '公告图片URL（兼容旧版本）'
  },
  imageUrls: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '公告多图片URL（JSON数组格式）',
    get() {
      const rawValue = this.getDataValue('imageUrls');
      if (rawValue) {
        try {
          return JSON.parse(rawValue);
        } catch (e) {
          console.error('解析imageUrls出错:', e);
          return [];
        }
      }
      return null;
    },
    set(value) {
      if (Array.isArray(value)) {
        this.setDataValue('imageUrls', JSON.stringify(value));
      } else if (value === null) {
        this.setDataValue('imageUrls', null);
      } else {
        this.setDataValue('imageUrls', JSON.stringify([value]));
      }
    }
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'announcements',
  timestamps: true
});

module.exports = Announcement; 
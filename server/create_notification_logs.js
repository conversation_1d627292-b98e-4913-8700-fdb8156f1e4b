const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'feishu_mall',
  port: process.env.DB_PORT || 3306,
  multipleStatements: true
};

async function createNotificationLogsTable() {
  let connection;
  try {
    console.log('开始创建notification_logs表...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接已建立');
    
    // 检查表是否已存在
    const [tables] = await connection.query(`SHOW TABLES LIKE 'notification_logs'`);
    if (tables.length > 0) {
      console.log('notification_logs表已存在');
      return;
    }
    
    // 创建表
    const createTableSql = `
      CREATE TABLE notification_logs (
        id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
        notification_type VARCHAR(50) NOT NULL COMMENT '通知类型',
        webhook_url VARCHAR(500) COMMENT '发送的webhook地址',
        request_payload TEXT COMMENT '发送的消息内容(JSON格式)',
        response_status INT COMMENT 'HTTP响应状态码',
        response_body TEXT COMMENT '响应内容(JSON格式)',
        status ENUM('pending', 'success', 'failed') DEFAULT 'pending' COMMENT '发送状态',
        error_message TEXT COMMENT '错误信息',
        retry_count INT DEFAULT 0 COMMENT '重试次数',
        max_retries INT DEFAULT 3 COMMENT '最大重试次数',
        next_retry_at DATETIME COMMENT '下次重试时间',
        sent_at DATETIME COMMENT '实际发送时间',
        response_time INT COMMENT '响应时间(毫秒)',
        created_by INT COMMENT '触发者用户ID',
        trigger_source VARCHAR(50) COMMENT '触发源(auto/manual/test)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知日志表';
    `;
    
    await connection.query(createTableSql);
    console.log('notification_logs表创建成功');
    
    // 创建索引
    const createIndexSql = `
      CREATE INDEX idx_notification_logs_type ON notification_logs (notification_type);
      CREATE INDEX idx_notification_logs_status ON notification_logs (status);
      CREATE INDEX idx_notification_logs_created_at ON notification_logs (created_at);
      CREATE INDEX idx_notification_logs_next_retry ON notification_logs (next_retry_at);
    `;
    
    await connection.query(createIndexSql);
    console.log('notification_logs表索引创建成功');
    
  } catch (error) {
    console.error('创建notification_logs表时出错:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行创建
createNotificationLogsTable(); 
#!/bin/bash

echo "=== 部署修正后的Nginx配置 ==="

# 1. 上传修正后的配置文件
echo "1. 上传修正后的nginx配置文件..."
scp fixed-nginx.conf root@**************:/tmp/nginx-new.conf

# 2. 在服务器上应用配置
echo "2. 在服务器上应用配置..."
ssh root@************** '
# 备份当前配置
echo "📋 备份当前配置..."
cp /www/server/panel/vhost/nginx/**************.conf /www/server/panel/vhost/nginx/**************.conf.backup.$(date +%Y%m%d_%H%M%S)

# 应用新配置
echo "🔄 应用新配置..."
cp /tmp/nginx-new.conf /www/server/panel/vhost/nginx/**************.conf

# 创建并设置上传目录
echo "📁 创建并设置上传目录..."
cd /www/wwwroot/server
mkdir -p uploads/images uploads/documents
chmod 755 uploads
chmod 755 uploads/images  
chmod 755 uploads/documents
chown -R www:www uploads

echo "📋 上传目录结构："
ls -la uploads/

# 测试nginx配置
echo "🔧 测试nginx配置..."
if nginx -t; then
    echo "✅ Nginx配置测试通过"
    
    # 重新加载nginx
    echo "🔄 重新加载nginx..."
    nginx -s reload
    echo "✅ Nginx已重新加载"
    
    # 创建测试文件
    echo "🧪 创建测试文件..."
    echo "Hello World - uploads test" > uploads/test.txt
    
    # 测试静态文件访问
    echo "🌐 测试静态文件访问："
    echo "测试URL: http://**************/uploads/test.txt"
    
    # 本地测试
    LOCAL_TEST=$(curl -s http://127.0.0.1/uploads/test.txt 2>/dev/null)
    if [ "$LOCAL_TEST" = "Hello World - uploads test" ]; then
        echo "✅ 本地访问测试成功"
    else
        echo "❌ 本地访问测试失败"
        echo "返回内容: $LOCAL_TEST"
    fi
    
    # 检查HTTP响应状态
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1/uploads/test.txt)
    echo "HTTP状态码: $HTTP_STATUS"
    
    # 清理测试文件
    rm -f uploads/test.txt
    
else
    echo "❌ Nginx配置测试失败"
    echo "配置文件内容："
    cat /www/server/panel/vhost/nginx/**************.conf
    exit 1
fi
'

echo ""
echo "3. 检查PM2应用状态..."
ssh root@************** "pm2 list | grep -E '(id|workyy)'"

echo ""
echo "=== 部署完成 ==="
echo "✅ 配置已更新并重新加载"
echo "🔗 静态文件访问地址: http://**************/uploads/"
echo "📝 请重新测试图片上传功能"

echo ""
echo "🔍 如果问题仍然存在，请检查："
echo "  1. 上传目录是否有文件: ls -la /www/wwwroot/server/uploads/"
echo "  2. 文件权限是否正确: ls -la /www/wwwroot/server/uploads/images/"
echo "  3. 直接访问: curl http://**************/uploads/test.txt" 
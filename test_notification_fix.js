#!/usr/bin/env node

/**
 * 通知开关功能修复验证脚本
 * 用于验证通知配置更新功能是否已修复
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 测试用的管理员登录信息
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

/**
 * 管理员登录获取token
 */
async function adminLogin() {
  try {
    console.log('🔐 正在进行管理员登录...');
    const response = await axios.post(`${BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    
    if (response.data.success) {
      authToken = response.data.token;
      console.log('✅ 管理员登录成功');
      return true;
    } else {
      console.error('❌ 管理员登录失败:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 管理员登录错误:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * 获取通知配置列表
 */
async function getNotificationConfigs() {
  try {
    console.log('📋 获取通知配置列表...');
    const response = await axios.get(`${BASE_URL}/system/notification-configs`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      console.log('✅ 获取通知配置成功，共', response.data.data.length, '个配置');
      console.log('配置详情:');
      response.data.data.forEach(config => {
        console.log(`  - ${config.typeName} (${config.notificationType}): ${config.enabled ? '启用' : '禁用'}`);
      });
      return response.data.data;
    } else {
      console.error('❌ 获取通知配置失败:', response.data.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 获取通知配置错误:', error.response?.data?.message || error.message);
    return [];
  }
}

/**
 * 测试更新通知配置
 */
async function testUpdateNotificationConfig(notificationType, enabled) {
  try {
    console.log(`🔧 测试更新通知配置: ${notificationType} -> ${enabled ? '启用' : '禁用'}`);
    
    const updateData = {
      enabled: enabled,
      webhookUrl: 'https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc'
    };
    
    const response = await axios.put(`${BASE_URL}/system/notification-configs/${notificationType}`, updateData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('📤 API响应状态:', response.status);
    console.log('📤 API响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      console.log(`✅ 更新配置成功: ${notificationType}`);
      console.log('   响应数据:', {
        enabled: response.data.data.enabled,
        webhookUrl: response.data.data.webhookUrl ? '已设置' : '未设置',
        typeName: response.data.data.typeName
      });
      return true;
    } else {
      console.error(`❌ 更新配置失败: ${notificationType}`, response.data.message);
      return false;
    }
  } catch (error) {
    console.error(`❌ 更新配置错误: ${notificationType}`);
    console.error('错误详情:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 验证配置是否正确更新
 */
async function verifyConfigUpdate(notificationType, expectedEnabled) {
  try {
    console.log(`🔍 验证配置更新: ${notificationType}`);
    
    const response = await axios.get(`${BASE_URL}/system/notification-configs/${notificationType}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      const actualEnabled = response.data.data.enabled;
      if (actualEnabled === expectedEnabled) {
        console.log(`✅ 验证成功: ${notificationType} 状态为 ${actualEnabled ? '启用' : '禁用'}`);
        return true;
      } else {
        console.error(`❌ 验证失败: ${notificationType} 期望 ${expectedEnabled ? '启用' : '禁用'}，实际 ${actualEnabled ? '启用' : '禁用'}`);
        return false;
      }
    } else {
      console.error(`❌ 验证失败: 无法获取配置 ${notificationType}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 验证错误: ${notificationType}`, error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始通知开关功能修复验证测试...\n');
  
  // 1. 管理员登录
  const loginSuccess = await adminLogin();
  if (!loginSuccess) {
    console.log('❌ 测试终止：无法登录');
    return;
  }
  
  console.log('');
  
  // 2. 获取当前配置
  const configs = await getNotificationConfigs();
  if (configs.length === 0) {
    console.log('❌ 测试终止：无法获取配置');
    return;
  }
  
  console.log('');
  
  // 3. 测试几个关键配置的开关切换
  const testConfigs = [
    'exchange_notification',
    'stock_alert', 
    'order_alert'
  ];
  
  let successCount = 0;
  let totalTests = 0;
  
  for (const configType of testConfigs) {
    const config = configs.find(c => c.notificationType === configType);
    if (!config) {
      console.log(`⚠️ 跳过测试: ${configType} (配置不存在)`);
      continue;
    }
    
    console.log(`\n🧪 测试配置: ${config.typeName} (${configType})`);
    console.log(`   当前状态: ${config.enabled ? '启用' : '禁用'}`);
    
    // 测试切换到相反状态
    const newState = !config.enabled;
    totalTests++;
    
    const updateResult = await testUpdateNotificationConfig(configType, newState);
    if (updateResult) {
      // 等待一秒后验证
      await new Promise(resolve => setTimeout(resolve, 1000));
      const verifyResult = await verifyConfigUpdate(configType, newState);
      if (verifyResult) {
        successCount++;
        console.log(`✅ ${configType} 测试通过`);
      } else {
        console.log(`❌ ${configType} 验证失败`);
      }
    } else {
      console.log(`❌ ${configType} 更新失败`);
    }
    
    // 等待一秒再进行下一个测试
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 4. 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   成功数: ${successCount}`);
  console.log(`   失败数: ${totalTests - successCount}`);
  console.log(`   成功率: ${totalTests > 0 ? ((successCount / totalTests) * 100).toFixed(1) : 0}%`);
  
  if (successCount === totalTests && totalTests > 0) {
    console.log('🎉 所有测试通过！通知开关功能修复成功');
  } else {
    console.log('⚠️ 部分或全部测试失败，请检查修复效果');
  }
  
  // 5. 最终获取配置状态
  console.log('\n📋 最终配置状态:');
  await getNotificationConfigs();
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试脚本执行错误:', error);
  process.exit(1);
});

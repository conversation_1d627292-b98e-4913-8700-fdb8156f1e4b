#!/usr/bin/env node

/**
 * 通知开关功能测试脚本
 * 用于验证通知配置更新功能是否正常工作
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 测试用的管理员登录信息
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

/**
 * 管理员登录获取token
 */
async function adminLogin() {
  try {
    console.log('🔐 正在进行管理员登录...');
    const response = await axios.post(`${BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    
    if (response.data.success) {
      authToken = response.data.token;
      console.log('✅ 管理员登录成功');
      return true;
    } else {
      console.error('❌ 管理员登录失败:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 管理员登录错误:', error.message);
    return false;
  }
}

/**
 * 获取通知配置列表
 */
async function getNotificationConfigs() {
  try {
    console.log('📋 获取通知配置列表...');
    const response = await axios.get(`${BASE_URL}/system/notification-configs`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      console.log('✅ 获取通知配置成功，共', response.data.data.length, '个配置');
      return response.data.data;
    } else {
      console.error('❌ 获取通知配置失败:', response.data.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 获取通知配置错误:', error.message);
    return [];
  }
}

/**
 * 测试更新通知配置
 */
async function testUpdateNotificationConfig(notificationType, enabled) {
  try {
    console.log(`🔧 测试更新通知配置: ${notificationType} -> ${enabled ? '启用' : '禁用'}`);
    
    const updateData = {
      enabled: enabled,
      webhookUrl: 'https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc'
    };
    
    const response = await axios.put(`${BASE_URL}/system/notification-configs/${notificationType}`, updateData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      console.log(`✅ 更新配置成功: ${notificationType}`);
      console.log('   响应数据:', {
        enabled: response.data.data.enabled,
        webhookUrl: response.data.data.webhookUrl ? '已设置' : '未设置'
      });
      return true;
    } else {
      console.error(`❌ 更新配置失败: ${notificationType}`, response.data.message);
      return false;
    }
  } catch (error) {
    console.error(`❌ 更新配置错误: ${notificationType}`, error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始通知开关功能测试...\n');
  
  // 1. 管理员登录
  const loginSuccess = await adminLogin();
  if (!loginSuccess) {
    console.log('❌ 测试终止：无法登录');
    return;
  }
  
  console.log('');
  
  // 2. 获取当前配置
  const configs = await getNotificationConfigs();
  if (configs.length === 0) {
    console.log('❌ 测试终止：无法获取配置');
    return;
  }
  
  console.log('');
  
  // 3. 测试几个关键配置的开关切换
  const testConfigs = [
    'exchange_notification',
    'stock_alert', 
    'new_user_welcome',
    'order_alert'
  ];
  
  let successCount = 0;
  let totalTests = 0;
  
  for (const configType of testConfigs) {
    const config = configs.find(c => c.notificationType === configType);
    if (!config) {
      console.log(`⚠️ 跳过测试: ${configType} (配置不存在)`);
      continue;
    }
    
    // 测试禁用
    totalTests++;
    const disableResult = await testUpdateNotificationConfig(configType, false);
    if (disableResult) successCount++;
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试启用
    totalTests++;
    const enableResult = await testUpdateNotificationConfig(configType, true);
    if (enableResult) successCount++;
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('');
  }
  
  // 4. 输出测试结果
  console.log('📊 测试结果汇总:');
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   成功数: ${successCount}`);
  console.log(`   失败数: ${totalTests - successCount}`);
  console.log(`   成功率: ${((successCount / totalTests) * 100).toFixed(1)}%`);
  
  if (successCount === totalTests) {
    console.log('🎉 所有测试通过！通知开关功能正常工作');
  } else {
    console.log('⚠️ 部分测试失败，请检查错误信息');
  }
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试脚本执行错误:', error);
  process.exit(1);
});

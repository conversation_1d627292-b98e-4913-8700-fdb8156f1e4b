<template>
  <div class="notifications-page">
    <div class="page-header">
      <h2>系统通知中心</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleReadAll" :disabled="!hasUnread">
          全部标为已读
        </el-button>
      </div>
    </div>
    
    <el-card class="notification-card">
      <template #header>
        <div class="card-header">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="全部通知" name="all"></el-tab-pane>
            <el-tab-pane label="未读通知" name="unread"></el-tab-pane>
          </el-tabs>
          
          <div class="filter-actions">
            <el-select v-model="typeFilter" placeholder="全部类型" clearable>
              <el-option label="兑换申请" value="exchange"></el-option>
              <el-option label="用户反馈" value="feedback"></el-option>
            </el-select>
          </div>
        </div>
      </template>
      
      <div class="notification-list">
        <el-empty v-if="filteredNotifications.length === 0" description="暂无通知"></el-empty>
        
        <div v-else>
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.isRead }"
          >
            <div class="notification-content" @click="handleNotificationClick(notification)">
              <div class="notification-header">
                <h4>{{ notification.title }}</h4>
                <el-tag size="small" :type="getTagType(notification.type)">
                  {{ getTypeLabel(notification.type) }}
                </el-tag>
              </div>
              <p>{{ notification.content }}</p>
              <div class="notification-footer">
                <small>{{ formatDate(notification.createdAt) }}</small>
              </div>
            </div>
            
            <div class="notification-actions">
              <el-button 
                v-if="!notification.isRead"
                type="primary" 
                link 
                size="small" 
                @click="handleMarkRead(notification.id)"
              >
                标为已读
              </el-button>
              <el-button 
                type="danger" 
                link 
                size="small" 
                @click="handleDelete(notification.id)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useNotificationStore } from '../../stores/notifications';
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();
const notificationStore = useNotificationStore();

// 状态
const activeTab = ref('all');
const typeFilter = ref('');

// 获取通知数据
const notifications = computed(() => notificationStore.notifications);
const hasUnread = computed(() => notificationStore.hasUnread);

// 过滤通知
const filteredNotifications = computed(() => {
  let result = [...notifications.value];
  
  // 按标签过滤
  if (activeTab.value === 'unread') {
    result = result.filter(n => !n.isRead);
  }
  
  // 按类型过滤
  if (typeFilter.value) {
    result = result.filter(n => n.type === typeFilter.value);
  }
  
  return result;
});

// 加载通知数据
const loadNotifications = async () => {
  await notificationStore.fetchNotifications();
  await notificationStore.fetchUnreadCount();
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取类型标签
const getTypeLabel = (type) => {
  switch (type) {
    case 'exchange': return '兑换申请';
    case 'feedback': return '用户反馈';
    default: return '其他通知';
  }
};

// 获取标签类型
const getTagType = (type) => {
  switch (type) {
    case 'exchange': return 'success';
    case 'feedback': return 'info';
    default: return 'default';
  }
};

// 处理点击通知
const handleNotificationClick = async (notification) => {
  // 标记为已读
  if (!notification.isRead) {
    await notificationStore.readNotification(notification.id);
  }
  
  // 根据通知类型跳转
  if (notification.type === 'exchange') {
    router.push(`/admin/exchanges?id=${notification.sourceId}`);
  } else if (notification.type === 'feedback') {
    router.push(`/admin/feedbacks?id=${notification.sourceId}`);
  }
};

// 标记为已读
const handleMarkRead = async (id) => {
  await notificationStore.readNotification(id);
  ElMessage.success('已标记为已读');
};

// 标记所有为已读
const handleReadAll = async () => {
  try {
    await ElMessageBox.confirm('确定将所有通知标记为已读吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });
    
    await notificationStore.readAllNotifications();
    ElMessage.success('已将所有通知标记为已读');
  } catch (error) {
    // 用户取消操作，不做处理
  }
};

// 处理删除通知
const handleDelete = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除此通知吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await notificationStore.removeNotification(id);
    ElMessage.success('通知已删除');
  } catch (error) {
    // 用户取消删除，不做处理
  }
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadNotifications();
});

// 监听标签变化，刷新数据
watch(activeTab, () => {
  loadNotifications();
});
</script>

<style scoped>
.notifications-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-list {
  margin-top: 20px;
}

.notification-item {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.notification-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  background-color: #ecf5ff;
  border-left: 4px solid #409eff;
}

.notification-content {
  flex: 1;
  cursor: pointer;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.notification-content p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.notification-footer {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.notification-footer small {
  color: #909399;
  font-size: 12px;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style> 
<template>
  <div class="category-management">
    <div class="header">
      <h2>分类管理</h2>
      <el-button type="primary" @click="showAddCategoryDialog">添加分类</el-button>
    </div>
    
    <el-table 
      :data="categories" 
      border 
      style="width: 100%" 
      v-loading="loading"
    >
      <el-table-column type="index" width="50" />
      
      <el-table-column label="分类名称" prop="name" min-width="180" />
      
      <el-table-column label="描述" prop="description" min-width="220" show-overflow-tooltip />
      
      <el-table-column label="商品数量" width="100">
        <template #default="scope">
          {{ scope.row.productCount || 0 }}
        </template>
      </el-table-column>
      
      <el-table-column label="排序" prop="sortOrder" width="80" />
      
      <el-table-column label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button 
            size="small" 
            type="primary" 
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          
          <el-popconfirm 
            title="确定要删除该分类吗？此操作不可恢复！" 
            @confirm="handleDelete(scope.row.id)"
          >
            <template #reference>
              <el-button 
                size="small" 
                type="danger" 
                :disabled="scope.row.productCount > 0"
              >
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 添加/编辑分类对话框 -->
    <el-dialog 
      v-model="categoryDialogVisible" 
      :title="isEditing ? '编辑分类' : '添加分类'"
      width="500px"
      destroy-on-close
    >
      <el-form 
        ref="categoryFormRef" 
        :model="categoryForm" 
        :rules="categoryRules" 
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="categoryForm.description" 
            type="textarea" 
            rows="3" 
            placeholder="请输入分类描述" 
          />
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="categoryForm.sortOrder" :min="0" :precision="0" />
          <div class="form-tip">数字越小，排序越靠前</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="categoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitCategoryForm" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  getCategories, 
  getCategoriesWithProductCount, 
  createCategory, 
  updateCategory, 
  deleteCategory 
} from '../../api/categories';

// 分类数据
const categories = ref([]);
const loading = ref(false);

// 表单相关
const categoryDialogVisible = ref(false);
const isEditing = ref(false);
const submitting = ref(false);
const categoryFormRef = ref(null);

const categoryForm = reactive({
  id: null,
  name: '',
  description: '',
  sortOrder: 0
});

const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ]
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true;
  try {
    const response = await getCategoriesWithProductCount();
    categories.value = response;
  } catch (error) {
    console.error('获取分类列表失败:', error);
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

// 添加分类
const showAddCategoryDialog = () => {
  isEditing.value = false;
  Object.assign(categoryForm, {
    id: null,
    name: '',
    description: '',
    sortOrder: 0
  });
  categoryDialogVisible.value = true;
};

// 编辑分类
const handleEdit = (category) => {
  isEditing.value = true;
  Object.assign(categoryForm, {
    id: category.id,
    name: category.name,
    description: category.description || '',
    sortOrder: category.sortOrder
  });
  categoryDialogVisible.value = true;
};

// 提交分类表单
const submitCategoryForm = async () => {
  if (!categoryFormRef.value) return;
  
  await categoryFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    submitting.value = true;
    
    try {
      const categoryData = {
        name: categoryForm.name,
        description: categoryForm.description,
        sortOrder: categoryForm.sortOrder
      };
      
      if (isEditing.value) {
        await updateCategory(categoryForm.id, categoryData);
        ElMessage.success('分类更新成功');
      } else {
        await createCategory(categoryData);
        ElMessage.success('分类添加成功');
      }
      
      categoryDialogVisible.value = false;
      fetchCategories();
    } catch (error) {
      console.error('保存分类失败:', error);
      const errorMessage = error.response?.data?.message || '保存分类失败';
      ElMessage.error(errorMessage);
    } finally {
      submitting.value = false;
    }
  });
};

// 删除分类
const handleDelete = async (categoryId) => {
  try {
    await deleteCategory(categoryId);
    ElMessage.success('分类已删除');
    fetchCategories();
  } catch (error) {
    console.error('删除分类失败:', error);
    const errorMessage = error.response?.data?.message || '删除分类失败';
    ElMessage.error(errorMessage);
  }
};

// 初始化
onMounted(() => {
  fetchCategories();
});
</script>

<style scoped>
.category-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style> 
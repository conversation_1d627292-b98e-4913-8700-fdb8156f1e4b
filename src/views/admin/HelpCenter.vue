<template>
  <div class="help-center">
    <div class="help-header">
      <h2>帮助中心</h2>
      <p>欢迎使用帮助中心，您可以在这里找到系统使用的相关指南和常见问题解答</p>
    </div>

    <el-row :gutter="20" class="help-container">
      <el-col :span="6">
        <div class="help-sidebar">
          <el-menu
            :default-active="activeSection"
            class="help-menu"
            @select="handleSelect"
          >
            <el-menu-item index="system-intro">
              <el-icon><InfoFilled /></el-icon>
              <span>系统介绍</span>
            </el-menu-item>
            
            <el-sub-menu index="user-guide">
              <template #title>
                <el-icon><Document /></el-icon>
                <span>使用指南</span>
              </template>
              <el-menu-item index="product-management">
                <span>商品管理</span>
              </el-menu-item>
              <el-menu-item index="category-management">
                <span>分类管理</span>
              </el-menu-item>
              <el-menu-item index="user-management">
                <span>用户管理</span>
              </el-menu-item>
              <el-menu-item index="feedback-management">
                <span>反馈管理</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item index="faq">
              <el-icon><QuestionFilled /></el-icon>
              <span>常见问题</span>
            </el-menu-item>
            
            <el-menu-item index="system-updates">
              <el-icon><Refresh /></el-icon>
              <span>系统更新</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-col>
      
      <el-col :span="18">
        <div class="help-content">
          <component :is="currentComponent" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, shallowRef, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { InfoFilled, Document, QuestionFilled, Refresh } from '@element-plus/icons-vue';

// 导入帮助内容组件
import SystemIntro from './help/SystemIntro.vue';
import ProductManagement from './help/ProductManagement.vue';
import CategoryManagement from './help/CategoryManagement.vue';
import UserManagement from './help/UserManagement.vue';
import FeedbackManagement from './help/FeedbackManagement.vue';
import FAQ from './help/FAQ.vue';
import SystemUpdates from './help/SystemUpdates.vue';

const route = useRoute();
const router = useRouter();

// 当前激活的菜单项
const activeSection = ref('system-intro');

// 当前显示的内容组件
const currentComponent = shallowRef(SystemIntro);

// 内容组件映射
const componentMap = {
  'system-intro': SystemIntro,
  'product-management': ProductManagement,
  'category-management': CategoryManagement,
  'user-management': UserManagement,
  'feedback-management': FeedbackManagement,
  'faq': FAQ,
  'system-updates': SystemUpdates
};

// 处理菜单选择
const handleSelect = (key) => {
  activeSection.value = key;
  if (componentMap[key]) {
    currentComponent.value = componentMap[key];
  }
  
  // 更新路由参数以便分享和刷新页面
  router.push({
    path: '/admin/help',
    query: { section: key }
  });
};

// 组件挂载时，根据URL参数设置当前显示的内容
onMounted(() => {
  if (route.query.section && componentMap[route.query.section]) {
    activeSection.value = route.query.section;
    currentComponent.value = componentMap[route.query.section];
  }
});
</script>

<style scoped>
.help-center {
  height: 100%;
}

.help-header {
  padding: 20px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.help-header h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

.help-header p {
  color: #606266;
  font-size: 14px;
}

.help-container {
  height: calc(100% - 90px);
}

.help-sidebar {
  border-right: 1px solid #e0e0e0;
  height: 100%;
}

.help-menu {
  border-right: none;
}

.help-content {
  padding: 0 20px;
  height: 100%;
  overflow-y: auto;
}
</style> 
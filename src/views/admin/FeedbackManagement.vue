<template>
  <div class="feedback-management">
    <div class="page-header">
      <h1 class="page-title">反馈管理</h1>
      <div class="filter-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索反馈内容"
          clearable
          class="search-input"
          @clear="fetchFeedbacks"
          @keyup.enter="fetchFeedbacks"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          clearable
          @change="fetchFeedbacks"
          class="filter-select"
        >
          <el-option label="待处理" value="pending" />
          <el-option label="处理中" value="processing" />
          <el-option label="已完成" value="completed" />
        </el-select>
        
        <el-select
          v-model="typeFilter"
          placeholder="类型筛选"
          clearable
          @change="fetchFeedbacks"
          class="filter-select"
        >
          <el-option label="商品相关" value="product" />
          <el-option label="功能建议" value="feature" />
          <el-option label="问题反馈" value="bug" />
          <el-option label="其他" value="other" />
        </el-select>
        
        <el-button type="primary" @click="fetchFeedbacks">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
      </div>
    </div>
    
    <!-- 添加错误信息展示组件 -->
    <ErrorMessage
      v-if="permissionError"
      title="权限不足"
      :message="permissionErrorMessage"
      :details="permissionErrorDetails"
      type="error"
      :showRetry="false"
      @close="permissionError = false"
    />
    
    <el-table
      v-loading="loading"
      :data="feedbacks"
      border
      stripe
      style="width: 100%"
      class="feedback-table"
    >
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <el-tag 
            :type="getFeedbackTypeTag(row.type)" 
            effect="light"
          >
            {{ getFeedbackTypeName(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="title" label="标题" min-width="180" show-overflow-tooltip />
      
      <el-table-column prop="user.username" label="提交用户" width="120" show-overflow-tooltip />
      
      <el-table-column prop="createdAt" label="提交时间" width="160">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag 
            :type="getFeedbackStatusTag(row.status)" 
            effect="plain"
          >
            {{ getFeedbackStatusName(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            plain 
            @click="openFeedbackDetail(row)"
          >
            查看
          </el-button>
          
          <el-button 
            v-if="row.status !== 'completed'"
            type="success" 
            size="small" 
            plain 
            @click="openReplyDialog(row)"
          >
            回复
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 反馈详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="反馈详情"
      width="700px"
      destroy-on-close
    >
      <template v-if="currentFeedback">
        <div class="feedback-detail">
          <div class="detail-header">
            <div class="detail-title">
              <h2>{{ currentFeedback.title }}</h2>
              <el-tag 
                :type="getFeedbackTypeTag(currentFeedback.type)" 
                effect="light"
              >
                {{ getFeedbackTypeName(currentFeedback.type) }}
              </el-tag>
              <el-tag 
                :type="getFeedbackStatusTag(currentFeedback.status)" 
                effect="plain"
                class="status-tag"
              >
                {{ getFeedbackStatusName(currentFeedback.status) }}
              </el-tag>
            </div>
            <div class="detail-meta">
              <span class="meta-item">
                <el-icon><User /></el-icon>
                {{ currentFeedback.user?.username || '未知用户' }}
              </span>
              <span class="meta-item">
                <el-icon><Calendar /></el-icon>
                {{ formatDate(currentFeedback.createdAt) }}
              </span>
            </div>
          </div>
          
          <div class="detail-content">
            <div class="content-label">反馈内容：</div>
            <p class="content-text">{{ currentFeedback.content }}</p>
          </div>
          
          <div v-if="currentFeedback.adminReply" class="detail-reply">
            <div class="reply-header">
              <div class="content-label">管理员回复：</div>
              <span class="reply-time">
                {{ formatDate(currentFeedback.updatedAt) }}
              </span>
            </div>
            <p class="reply-content">{{ currentFeedback.adminReply }}</p>
          </div>
        </div>
      </template>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button 
            v-if="currentFeedback && currentFeedback.status !== 'completed'"
            type="primary" 
            @click="openReplyDialog(currentFeedback)"
          >
            回复反馈
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 回复对话框 -->
    <el-dialog
      v-model="replyDialogVisible"
      title="回复反馈"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <template v-if="currentFeedback">
        <el-form
          ref="replyFormRef"
          :model="replyForm"
          :rules="replyRules"
          label-position="top"
        >
          <el-form-item label="反馈标题" class="readonly-item">
            <el-input v-model="currentFeedback.title" disabled />
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-select v-model="replyForm.status" class="w-full">
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="回复内容" prop="adminReply">
            <el-input
              v-model="replyForm.adminReply"
              type="textarea"
              :rows="5"
              placeholder="请输入回复内容..."
            />
          </el-form-item>
        </el-form>
      </template>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="replyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReply" :loading="submitting">
            提交回复
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, User, Calendar } from '@element-plus/icons-vue';
import { getAllFeedbacks, updateFeedback, getFeedbackDetail } from '../../api/feedback';
import ErrorMessage from '../../components/ErrorMessage.vue';

// 数据加载状态
const loading = ref(false);
const submitting = ref(false);

// 反馈列表数据
const feedbacks = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 筛选条件
const searchQuery = ref('');
const statusFilter = ref('');
const typeFilter = ref('');

// 对话框控制
const detailDialogVisible = ref(false);
const replyDialogVisible = ref(false);
const currentFeedback = ref(null);

// 回复表单
const replyFormRef = ref(null);
const replyForm = reactive({
  status: '',
  adminReply: ''
});

// 表单验证规则
const replyRules = {
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  adminReply: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { min: 5, max: 500, message: '回复内容长度应在5-500个字符之间', trigger: 'blur' }
  ]
};

// 权限错误状态
const permissionError = ref(false);
const permissionErrorMessage = ref('您没有管理员权限，无法访问此页面');
const permissionErrorDetails = ref('请联系系统管理员授权后再访问');

// 获取反馈列表
const fetchFeedbacks = async () => {
  loading.value = true;
  permissionError.value = false; // 重置权限错误状态
  
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value,
      type: typeFilter.value
    };
    
    const response = await getAllFeedbacks(params);
    feedbacks.value = response.data || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取反馈列表失败:', error);
    
    // 根据错误类型显示不同的错误信息
    if (error.response) {
      const status = error.response.status;
      const errorData = error.response.data;
      
      if (status === 403) {
        permissionError.value = true;
        permissionErrorMessage.value = errorData.message || '您没有查看反馈管理的权限';
        permissionErrorDetails.value = errorData.details || '请联系系统管理员授权后再访问';
        // 清空反馈数据
        feedbacks.value = [];
        total.value = 0;
      } else if (status === 401) {
        ElMessage.error('登录已过期，请重新登录');
        // 可以在这里添加重定向到登录页面的逻辑
      } else {
        ElMessage.error('获取反馈列表失败: ' + (errorData.message || '未知错误'));
      }
    } else {
      ElMessage.error('获取反馈列表失败，请检查网络连接');
    }
  } finally {
    loading.value = false;
  }
};

// 打开反馈详情
const openFeedbackDetail = async (feedback) => {
  try {
    console.log('正在获取反馈详情，ID:', feedback.id);
    
    const detailResponse = await getFeedbackDetail(feedback.id);
    console.log('获取反馈详情成功:', detailResponse);
    
    currentFeedback.value = detailResponse;
    detailDialogVisible.value = true;
  } catch (error) {
    console.error('获取反馈详情失败:', error);
    
    // 更详细的错误处理
    let errorMessage = '获取反馈详情失败';
    
    if (error.response) {
      const status = error.response.status;
      const errorData = error.response.data;
      
      if (status === 403) {
        errorMessage = errorData.details || '无权查看该反馈';
      } else if (status === 404) {
        errorMessage = '反馈不存在';
      } else if (status === 401) {
        errorMessage = '登录已过期，请重新登录';
      } else {
        errorMessage = `获取失败: ${errorData.message || '未知错误'}`;
      }
    } else if (error.message) {
      errorMessage = `获取失败: ${error.message}`;
    }
    
    ElMessage.error(errorMessage);
  }
};

// 打开回复对话框
const openReplyDialog = (feedback) => {
  currentFeedback.value = feedback;
  replyForm.status = feedback.status || 'pending';
  replyForm.adminReply = feedback.adminReply || '';
  
  replyDialogVisible.value = true;
  detailDialogVisible.value = false;
};

// 提交回复
const submitReply = async () => {
  if (!replyFormRef.value) return;
  
  await replyFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    try {
      submitting.value = true;
      
      await updateFeedback(currentFeedback.value.id, replyForm);
      
      ElMessage.success('回复提交成功');
      replyDialogVisible.value = false;
      fetchFeedbacks();
    } catch (error) {
      console.error('提交回复失败:', error);
      
      // 根据错误类型显示不同的错误信息
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        if (status === 403) {
          ElMessage.error(errorData.details || '您没有回复反馈的权限');
        } else if (status === 401) {
          ElMessage.error('登录已过期，请重新登录');
        } else if (status === 404) {
          ElMessage.error('反馈不存在或已被删除');
        } else {
          ElMessage.error('提交回复失败: ' + (errorData.message || '未知错误'));
        }
      } else {
        ElMessage.error('提交回复失败，请稍后重试');
      }
    } finally {
      submitting.value = false;
    }
  });
};

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchFeedbacks();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchFeedbacks();
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取反馈类型名称和标签样式
const getFeedbackTypeName = (type) => {
  const typeMap = {
    product: '商品相关',
    feature: '功能建议',
    bug: '问题反馈',
    other: '其他'
  };
  
  return typeMap[type] || '未知类型';
};

const getFeedbackTypeTag = (type) => {
  const typeTagMap = {
    product: 'success',
    feature: 'primary',
    bug: 'danger',
    other: 'info'
  };
  
  return typeTagMap[type] || 'info';
};

// 获取反馈状态名称和标签样式
const getFeedbackStatusName = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成'
  };
  
  return statusMap[status] || '未知状态';
};

const getFeedbackStatusTag = (status) => {
  const statusTagMap = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success'
  };
  
  return statusTagMap[status] || 'info';
};

// 初始化
onMounted(() => {
  fetchFeedbacks();
});
</script>

<style scoped>
.feedback-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.filter-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.search-input {
  width: 220px;
}

.filter-select {
  width: 140px;
}

.feedback-table {
  margin-bottom: 20px;
  border-radius: 4px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 详情对话框样式 */
.feedback-detail {
  padding: 10px;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.detail-title h2 {
  margin: 0 10px 0 0;
  font-size: 20px;
  color: #303133;
}

.status-tag {
  margin-left: 10px;
}

.detail-meta {
  display: flex;
  gap: 20px;
  color: #606266;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-content {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.content-label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
}

.content-text {
  margin: 0;
  white-space: pre-line;
  line-height: 1.6;
  color: #606266;
}

.detail-reply {
  background-color: #f0f9eb;
  padding: 15px;
  border-radius: 4px;
  border-left: 3px solid #67c23a;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reply-time {
  font-size: 13px;
  color: #909399;
}

.reply-content {
  margin: 8px 0 0 0;
  white-space: pre-line;
  line-height: 1.6;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.readonly-item :deep(.el-input__wrapper) {
  background-color: #f5f7fa;
}

.w-full {
  width: 100%;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-actions {
    width: 100%;
  }
  
  .search-input,
  .filter-select {
    width: 100%;
  }
  
  .detail-title {
    flex-wrap: wrap;
  }
}
</style> 
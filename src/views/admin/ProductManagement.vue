<template>
  <div class="product-management">
    <div class="header">
      <h1>商品管理</h1>
      <div class="action-buttons">
        <el-upload
          class="import-upload"
          action="#"
          :auto-upload="false"
          :on-change="handleImportFileChange"
          :limit="1"
          :file-list="importFileList"
          accept=".csv,.xlsx,.xls"
        >
          <template #trigger>
            <el-button type="success">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
          </template>
          <el-button type="primary" @click="handleImport" :loading="importing">
            <el-icon><Check /></el-icon>
            开始导入
          </el-button>
          <template #tip>
            <div class="el-upload__tip">支持.csv、.xlsx格式，<el-link type="primary" @click="downloadTemplate">下载模板</el-link></div>
          </template>
        </el-upload>
        <el-button type="warning" @click="handleExport" :loading="exporting">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
        <el-button type="danger" @click="handleBatchDelete" :loading="batchDeleteLoading" :disabled="multipleSelection.length === 0">
          <el-icon><Delete /></el-icon>
          批量删除 <span v-if="multipleSelection.length > 0">({{ multipleSelection.length }})</span>
        </el-button>
        <el-button type="primary" @click="showAddProductDialog">
          <el-icon><Plus /></el-icon>
          添加商品
        </el-button>
      </div>
    </div>
    
    <div class="filters">
      <el-alert
        type="info"
        title="库存与兑换量自动管理"
        description="当订单被拒绝或取消时，系统会自动恢复商品库存并减少相应的兑换量统计。"
        show-icon
        :closable="false"
        style="margin-bottom: 15px; width: 100%;"
      />
      
      <el-input
        v-model="filterParams.search"
        placeholder="搜索商品名称"
        clearable
        @input="handleSearchChange"
        style="width: 240px"
      />
      
      <el-select
        v-model="filterParams.category"
        placeholder="选择分类"
        clearable
        @change="handleCategoryChange"
        style="width: 180px"
      >
        <el-option
          v-for="category in categories"
          :key="category.id"
          :label="category.name"
          :value="category.id"
        />
      </el-select>
      
      <el-select
        v-model="filterParams.sort"
        placeholder="排序方式"
        @change="handleSortChange"
        style="width: 200px"
      >
        <el-option label="默认排序" value="default" />
        <el-option label="最新添加" value="newest" />
        <el-option label="最早添加" value="oldest" />
        <el-option label="名称 A-Z" value="name-asc" />
        <el-option label="名称 Z-A" value="name-desc" />
        <el-option label="光年币 低-高" value="ly-asc" />
        <el-option label="光年币 高-低" value="ly-desc" />
        <el-option label="人民币 低-高" value="rmb-asc" />
        <el-option label="人民币 高-低" value="rmb-desc" />
        <el-option label="库存 低-高" value="stock-asc" />
        <el-option label="库存 高-低" value="stock-desc" />
        <el-option label="兑换量 低-高" value="exchange-asc" />
        <el-option label="兑换量 高-低" value="exchange-desc" />
      </el-select>
      
      <el-checkbox
        v-model="filterParams.showAll"
        @change="handleShowAllChange"
      >
        显示下架商品
      </el-checkbox>
      
      <el-button type="info" plain @click="resetFilters">重置筛选</el-button>
      
      <!-- 高级筛选按钮 -->
      <el-button 
        type="primary" 
        plain 
        @click="showAdvancedFilters = !showAdvancedFilters"
      >
        {{ showAdvancedFilters ? '收起高级筛选' : '高级筛选' }}
        <el-icon class="el-icon--right">
          <component :is="showAdvancedFilters ? 'ArrowUp' : 'ArrowDown'" />
        </el-icon>
      </el-button>
    </div>
    
    <!-- 高级筛选区域 -->
    <el-collapse-transition>
      <div v-show="showAdvancedFilters" class="advanced-filters">
        <el-divider content-position="left">价格范围</el-divider>
        
        <div class="price-range">
          <div class="price-group">
            <span class="price-label">光年币价格</span>
            <div class="price-inputs">
              <el-slider
                v-model="lyPriceRange"
                range
                :min="priceRanges.minLyPrice"
                :max="priceRanges.maxLyPrice"
                :show-input="true"
                :show-stops="false"
                :show-tooltip="true"
                @change="handleLySliderChange"
              />
            </div>
          </div>
          
          <div class="price-group">
            <span class="price-label">人民币价格</span>
            <div class="price-inputs">
              <el-slider
                v-model="rmbPriceRange"
                range
                :min="priceRanges.minRmbPrice"
                :max="priceRanges.maxRmbPrice"
                :show-input="true"
                :show-stops="false"
                :show-tooltip="true"
                :format-tooltip="val => `¥${val.toFixed(2)}`"
                @change="handleRmbSliderChange"
              />
            </div>
          </div>
        </div>
        
        <el-divider content-position="left">商品属性</el-divider>
        
        <div class="filter-tags">
          <el-checkbox v-model="filterParams.inStock" @change="debouncedHandleFilterChange(filterParams)">有库存</el-checkbox>
          <el-checkbox v-model="filterParams.isNew" @change="debouncedHandleFilterChange(filterParams)">新品</el-checkbox>
          <el-checkbox v-model="filterParams.isHot" @change="debouncedHandleFilterChange(filterParams)">热门</el-checkbox>
        </div>
        
        <el-divider content-position="left">上架日期</el-divider>
        
        <div class="date-range">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </div>
        
        <div class="filter-actions">
          <el-button type="primary" @click="applyFilters">应用筛选</el-button>
          <el-button type="info" plain @click="resetFilters">重置筛选</el-button>
        </div>
        
        <!-- 已激活筛选条件 -->
        <div v-if="hasActiveFilters" class="active-filters">
          <div class="active-filters-title">已选条件:</div>
          
          <el-tag 
            v-if="filterParams.search" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('search')"
          >
            关键词: {{ filterParams.search }}
          </el-tag>
          
          <el-tag 
            v-if="filterParams.category" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('category')"
          >
            分类: {{ getCategoryName(filterParams.category) }}
          </el-tag>
          
          <el-tag 
            v-if="filterParams.minLyPrice || filterParams.maxLyPrice" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('lyPrice')"
          >
            光年币: {{ filterParams.minLyPrice || 0 }} - {{ filterParams.maxLyPrice || '∞' }}
          </el-tag>
          
          <el-tag 
            v-if="filterParams.minRmbPrice || filterParams.maxRmbPrice" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('rmbPrice')"
          >
            人民币: ¥{{ filterParams.minRmbPrice || 0 }} - ¥{{ filterParams.maxRmbPrice || '∞' }}
          </el-tag>
          
          <el-tag 
            v-if="filterParams.inStock" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('inStock')"
          >
            有库存
          </el-tag>
          
          <el-tag 
            v-if="filterParams.isNew" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('isNew')"
          >
            新品
          </el-tag>
          
          <el-tag 
            v-if="filterParams.isHot" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('isHot')"
          >
            热门
          </el-tag>
          
          <el-tag 
            v-if="dateRange" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('dateRange')"
          >
            上架日期: {{ formatDate(dateRange[0]) }} 至 {{ formatDate(dateRange[1]) }}
          </el-tag>
        </div>
      </div>
    </el-collapse-transition>
    
    <el-table
      :data="products"
      border
      stripe
      style="width: 100%"
      v-loading="loading"
      :cell-style="{ 'text-align': 'center', 'vertical-align': 'middle' }"
      :header-cell-style="{ 'text-align': 'center', 'background-color': '#f5f7fa' }"
      :row-class-name="lowStockRowClass"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
      />
      
      <el-table-column
        prop="id"
        label="ID"
        width="70"
      />
      
      <el-table-column
        prop="name"
        label="商品名称"
        min-width="150"
        :show-overflow-tooltip="true"
        align="left"
      >
        <template #default="scope">
          <div class="product-name-cell">
            <el-tooltip v-if="scope.row.images && scope.row.images.length > 0" class="box-item" effect="dark" placement="top-start">
              <template #content>
                <img :src="scope.row.images[0]" style="width: 120px; height: 120px; object-fit: cover;" />
              </template>
              <span>{{ scope.row.name }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.name }}</span>
            <div class="tags-container">
              <el-tag v-if="scope.row.isNew" type="success" size="small" effect="plain">新品</el-tag>
              <el-tag v-if="scope.row.isHot" type="danger" size="small" effect="plain">热门</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="category.name"
        label="分类"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.category && scope.row.category.name">
            {{ scope.row.category.name }}
          </span>
          <span v-else-if="scope.row.categoryId">
            {{ getCategoryName(scope.row.categoryId) }}
          </span>
          <span v-else>未分类</span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="lyPrice"
        label="光年币价格"
        width="110"
      />
      
      <el-table-column
        prop="rmbPrice"
        label="人民币价格"
        width="110"
      >
        <template #default="scope">
          <span class="rmb-price">¥{{ scope.row.rmbPrice }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="stock"
        label="库存"
        width="80"
      >
        <template #default="scope">
          <span
            :class="{'low-stock': isLowStock(scope.row.stock)}"
            :title="isLowStock(scope.row.stock) ? '库存不足，请及时补货！' : ''"
          >
            {{ scope.row.stock }}
            <el-icon v-if="isLowStock(scope.row.stock)" class="warning-icon"><Warning /></el-icon>
          </span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="exchangeCount"
        label="累计兑换"
        width="90"
      />
      
      <el-table-column
        label="状态"
        width="100"
      >
        <template #default="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
            {{ scope.row.status === 'active' ? '已上架' : '已下架' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="createdAt"
        label="上线日期"
        width="150"
      >
        <template #default="scope">
          <span>{{ formatDate(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        min-width="200"
        fixed="right"
      >
        <template #default="scope">
          <div class="operation-buttons">
            <el-tooltip content="编辑商品" placement="top">
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">
                <el-icon><Edit /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="图片管理" placement="top">
              <el-button type="success" size="small" @click="handleImages(scope.row)">
                <el-icon><Picture /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip :content="scope.row.status === 'active' ? '下架商品' : '上架商品'" placement="top">
              <el-button
                :type="scope.row.status === 'active' ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(scope.row)"
              >
                <el-icon><component :is="scope.row.status === 'active' ? 'Hide' : 'View'" /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="删除商品" placement="top">
              <el-popconfirm
                title="确定要删除此商品吗？此操作不可恢复。"
                @confirm="handleDelete(scope.row.id)"
                confirm-button-type="danger"
                cancel-button-type="info"
              >
                <template #reference>
                  <el-button type="danger" size="small">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </template>
              </el-popconfirm>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 商品表单对话框 -->
    <el-dialog
      v-model="productDialogVisible"
      :title="isEditing ? '编辑商品' : '添加商品'"
      width="600px"
      @closed="productFormRef?.clearValidate()"
    >
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="100px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入商品名称"></el-input>
        </el-form-item>
        
        <el-form-item label="商品分类" prop="categoryId">
          <el-select v-model="productForm.categoryId" placeholder="请选择商品分类" style="width: 100%">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="光年币价格" prop="lyPrice">
          <el-input-number v-model="productForm.lyPrice" :min="0" :precision="0" style="width: 100%"></el-input-number>
        </el-form-item>
        
        <el-form-item label="人民币价格" prop="rmbPrice">
          <el-input-number v-model="productForm.rmbPrice" :min="0" :precision="2" style="width: 100%"></el-input-number>
        </el-form-item>
        
        <el-form-item label="库存数量" prop="stock">
          <el-input-number v-model="productForm.stock" :min="0" :precision="0" style="width: 100%"></el-input-number>
        </el-form-item>
        
        <el-form-item label="累计兑换" v-if="isEditing">
          <el-input-number v-model="productForm.exchangeCount" :min="0" :precision="0" style="width: 100%"></el-input-number>
        </el-form-item>
        
        <el-form-item label="上线日期" v-if="isEditing">
          <el-date-picker
            v-model="productForm.createdAt"
            type="datetime"
            placeholder="选择上线日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="商品描述">
          <el-input v-model="productForm.description" type="textarea" rows="4" placeholder="请输入商品描述"></el-input>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-checkbox v-model="productForm.isNew">新品</el-checkbox>
          <el-checkbox v-model="productForm.isHot">热门</el-checkbox>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="productForm.status">
            <el-radio label="active">上架</el-radio>
            <el-radio label="inactive">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProductForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 图片管理对话框 -->
    <el-dialog
      v-model="imagesDialogVisible"
      :title="`图片管理 - ${currentProduct?.name || ''}`"
      width="800px"
      destroy-on-close
    >
      <div class="upload-container">
        <el-upload
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :on-change="handleImageChange"
          :on-remove="handleImageRemove"
          multiple
          :file-list="imageFileList"
          accept="image/jpeg,image/png,image/gif,image/webp"
        >
          <el-icon><Plus /></el-icon>
          <template #tip>
            <div class="el-upload__tip">
              支持 JPG/PNG/GIF/WEBP 格式，单个文件不超过 5MB
            </div>
          </template>
        </el-upload>
        
        <el-button 
          type="primary" 
          @click="uploadImages" 
          :loading="uploadingImages"
          :disabled="imageFileList.length === 0"
        >
          上传所选图片
        </el-button>
      </div>
      
      <div v-if="productImages.length > 0" class="preview-gallery">
        <h3>当前商品图片</h3>
        <ImageGallery 
          :images="productImages" 
          :title="currentProduct?.name" 
          height="300px"
        />
      </div>
      
      <el-divider v-if="productImages.length > 0">排序与管理</el-divider>
      
      <el-row :gutter="20" v-if="productImages.length > 0">
        <el-col :span="8" v-for="image in productImages" :key="image.id" class="image-item">
          <el-card :body-style="{ padding: '0px' }" shadow="hover">
            <img :src="image.imageUrl" class="image" />
            <div style="padding: 10px;">
              <div class="image-sort">
                <span>排序：</span>
                <el-input-number 
                  v-model="image.sortOrder" 
                  :min="0" 
                  :max="999" 
                  size="small"
                  @change="updateImageSortOrder(image)"
                />
              </div>
              <div class="image-actions">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="deleteImage(image.id)"
                  :loading="deletingImageId === image.id"
                >
                  删除
                </el-button>
                <el-button
                  v-if="index !== 0"
                  type="primary"
                  size="small"
                  plain
                  @click="setMainImage(image)"
                >
                  设为主图
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-empty v-else description="暂无图片" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Plus, Edit, Picture, Delete, Hide, View, Upload, Download, 
  Check, ArrowUp, ArrowDown, Warning 
} from '@element-plus/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import { getProducts, createProduct, updateProduct, updateProductStatus, deleteProduct, importProducts, exportProducts, getProductPriceRanges, bulkDeleteProducts } from '../../api/products';
import { getCategories } from '../../api/categories';
import { getProductImages, uploadProductImage, updateProductImage, deleteProductImage } from '../../api/productImages';
import ImageGallery from '../../components/ImageGallery.vue';
import { useProductStore } from '../../stores/products';
import { debounce } from 'lodash-es';

// 获取路由参数
const route = useRoute();
const router = useRouter();

// 获取产品Store
const productStore = useProductStore();

// 状态
const loading = ref(false);
const products = ref([]);
const categories = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const showAdvancedFilters = ref(false);
// 添加请求序列号以防止竞态条件
const requestSequence = ref(0);

// 批量操作相关
const multipleSelection = ref([]);
const batchDeleteLoading = ref(false);

// 导入导出状态
const importing = ref(false);
const exporting = ref(false);
const importFileList = ref([]);

// 价格滑块值和范围
const lyPriceRange = ref([0, 100]);
const rmbPriceRange = ref([0, 100]);
const priceRanges = ref({
  minLyPrice: 0,
  maxLyPrice: 100,
  minRmbPrice: 0,
  maxRmbPrice: 100
});

// 日期范围
const dateRange = ref(null);

// 筛选参数
const filterParams = reactive({
  search: '',
  category: '',
  sort: 'default',
  showAll: false,
  minLyPrice: null,
  maxLyPrice: null,
  minRmbPrice: null,
  maxRmbPrice: null,
  inStock: false,
  isNew: false,
  isHot: false
});

// 检查是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return filterParams.search || 
         filterParams.category || 
         filterParams.minLyPrice || 
         filterParams.maxLyPrice ||
         filterParams.minRmbPrice || 
         filterParams.maxRmbPrice ||
         filterParams.inStock ||
         filterParams.isNew ||
         filterParams.isHot ||
         dateRange.value;
});

// 获取价格范围
const fetchPriceRanges = async () => {
  try {
    const params = {};
    // 如果已选择了分类，传递分类参数以获取更精确的价格范围
    if (filterParams.category) {
      params.category = filterParams.category;
    }
    // 如果有搜索关键词，传递搜索参数
    if (filterParams.search) {
      params.search = filterParams.search;
    }
    
    const response = await getProductPriceRanges(params);
    priceRanges.value = response;
    
    // 更新滑块值
    updateSliderValues();
  } catch (error) {
    console.error('获取价格范围失败:', error);
    ElMessage.error('获取价格范围数据失败');
  }
};

// 更新滑块值
const updateSliderValues = () => {
  // 光年币价格滑块初始化
  if (filterParams.minLyPrice !== null && filterParams.maxLyPrice !== null) {
    lyPriceRange.value = [filterParams.minLyPrice, filterParams.maxLyPrice];
  } else {
    lyPriceRange.value = [priceRanges.value.minLyPrice, priceRanges.value.maxLyPrice];
  }
  
  // 人民币价格滑块初始化
  if (filterParams.minRmbPrice !== null && filterParams.maxRmbPrice !== null) {
    rmbPriceRange.value = [filterParams.minRmbPrice, filterParams.maxRmbPrice];
  } else {
    rmbPriceRange.value = [priceRanges.value.minRmbPrice, priceRanges.value.maxRmbPrice];
  }
};

// 处理光年币滑块变化
const handleLySliderChange = (values) => {
  filterParams.minLyPrice = values[0];
  filterParams.maxLyPrice = values[1];
  // 不立即触发筛选，等待用户点击应用按钮
};

// 处理人民币滑块变化
const handleRmbSliderChange = (values) => {
  filterParams.minRmbPrice = values[0];
  filterParams.maxRmbPrice = values[1];
  // 不立即触发筛选，等待用户点击应用按钮
};

// 处理日期变化
const handleDateChange = () => {
  // 不立即触发筛选，等待用户点击应用按钮
};

// 应用筛选
const applyFilters = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    filterParams.startDate = dateRange.value[0].toISOString().split('T')[0];
    filterParams.endDate = dateRange.value[1].toISOString().split('T')[0];
  } else {
    filterParams.startDate = null;
    filterParams.endDate = null;
  }
  
  currentPage.value = 1;
  fetchProducts();
};

// 获取分类名称
const getCategoryName = (categoryId) => {
  if (!categoryId) return '未分类';
  const category = categories.value.find(c => c.id === categoryId);
  return category ? category.name : '未分类';
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

// 移除筛选条件
const removeFilter = (filterType) => {
  switch (filterType) {
    case 'search':
      filterParams.search = '';
      break;
    case 'category':
      filterParams.category = '';
      break;
    case 'lyPrice':
      filterParams.minLyPrice = null;
      filterParams.maxLyPrice = null;
      updateSliderValues();
      break;
    case 'rmbPrice':
      filterParams.minRmbPrice = null;
      filterParams.maxRmbPrice = null;
      updateSliderValues();
      break;
    case 'inStock':
      filterParams.inStock = false;
      break;
    case 'isNew':
      filterParams.isNew = false;
      break;
    case 'isHot':
      filterParams.isHot = false;
      break;
    case 'dateRange':
      dateRange.value = null;
      filterParams.startDate = null;
      filterParams.endDate = null;
      break;
  }
  
  handleFilterChange(filterParams);
};

// 获取分类数据
const fetchCategoriesData = async () => {
  try {
    categories.value = await getCategories();
  } catch (error) {
    console.error('获取分类失败:', error);
    ElMessage.error('获取分类失败');
  }
};

// 获取商品列表
const fetchProducts = async () => {
  // 递增请求序列号
  const currentSequence = ++requestSequence.value;
  
  loading.value = true;
  try {
    console.log('准备发送筛选参数:', filterParams);
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...filterParams
    };
    
    // 强制明确处理showAll参数，确保未勾选时传递false
    params.showAll = !!filterParams.showAll;
    
    console.log('发送最终参数 (序列号:', currentSequence, '):', params);
    const response = await getProducts(params);
    
    // 检查这是否是最新的请求，如果不是则忽略结果
    if (currentSequence !== requestSequence.value) {
      console.log('忽略过期的API响应，序列号:', currentSequence, '当前序列号:', requestSequence.value);
      return response;
    }
    
    products.value = response.data;
    totalItems.value = response.total;
    
    return response;
  } catch (error) {
    // 只有在这是最新请求时才显示错误
    if (currentSequence === requestSequence.value) {
      console.error('获取商品列表失败:', error);
      ElMessage.error('获取商品列表失败');
    }
  } finally {
    // 只有在这是最新请求时才设置loading为false
    if (currentSequence === requestSequence.value) {
      loading.value = false;
    }
  }
};

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchProducts();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchProducts();
};

// 筛选处理
const handleFilterChange = (filters) => {
  console.log('筛选条件变更:', filters);
  Object.assign(filterParams, filters);
  currentPage.value = 1;
  fetchProducts();
};

// 防抖处理的搜索筛选
const debouncedHandleFilterChange = debounce((filters) => {
  handleFilterChange(filters);
}, 300);

const resetFilters = async () => {
  try {
    // 重置所有筛选参数
    Object.assign(filterParams, {
      search: '',
      category: '',
      sort: 'default',
      showAll: false,
      minLyPrice: null,
      maxLyPrice: null,
      minRmbPrice: null,
      maxRmbPrice: null,
      inStock: false,
      isNew: false,
      isHot: false,
      startDate: null,
      endDate: null
    });
    
    // 重置日期范围
    dateRange.value = null;
    
    // 确保价格范围数据存在，如果不存在则重新获取
    if (!priceRanges.value || 
        priceRanges.value.minLyPrice === undefined || 
        priceRanges.value.maxLyPrice === undefined ||
        priceRanges.value.minRmbPrice === undefined || 
        priceRanges.value.maxRmbPrice === undefined) {
      await fetchPriceRanges();
    }
    
    // 重置价格滑块值
    lyPriceRange.value = [priceRanges.value.minLyPrice || 0, priceRanges.value.maxLyPrice || 100];
    rmbPriceRange.value = [priceRanges.value.minRmbPrice || 0, priceRanges.value.maxRmbPrice || 100];
    
    // 重置页码
    currentPage.value = 1;
    
    // 重置产品存储的筛选状态（但不触发fetchProducts）
    if (productStore && typeof productStore.resetFilters === 'function') {
      // 临时保存原始方法
      const originalFetchProducts = productStore.fetchProducts;
      // 暂时替换为空函数，避免重复调用
      productStore.fetchProducts = () => Promise.resolve();
      // 调用重置
      productStore.resetFilters();
      // 恢复原始方法
      productStore.fetchProducts = originalFetchProducts;
    }
    
    // 刷新商品列表
    await fetchProducts();
    
    ElMessage.success('筛选条件已重置');
  } catch (error) {
    console.error('重置筛选失败:', error);
    ElMessage.error('重置筛选失败，请刷新页面重试');
  }
};

// 商品表单
const productDialogVisible = ref(false);
const isEditing = ref(false);
const submitting = ref(false);
const productFormRef = ref(null);

const productForm = reactive({
  id: null,
  name: '',
  categoryId: '',
  lyPrice: 0,
  rmbPrice: 0,
  description: '',
  stock: 0,
  exchangeCount: 0,
  isNew: false,
  isHot: false,
  status: 'active',
  createdAt: null
});

const productRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在2到100个字符之间', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  lyPrice: [
    { required: true, message: '请输入光年币价格', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === '') {
          callback(new Error('请输入光年币价格'));
        } else if (isNaN(Number(value)) || Number(value) < 0) {
          callback(new Error('光年币价格必须是大于等于0的数字'));
        } else if (!Number.isInteger(Number(value))) {
          callback(new Error('光年币价格必须是整数'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  rmbPrice: [
    { required: true, message: '请输入人民币价格', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === '') {
          callback(new Error('请输入人民币价格'));
        } else if (isNaN(Number(value)) || Number(value) < 0) {
          callback(new Error('人民币价格必须是大于等于0的数字'));
        } else if (Number(value) > 999999.99) {
          callback(new Error('人民币价格不能超过999999.99'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  stock: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === '') {
          callback(new Error('请输入库存数量'));
        } else if (isNaN(Number(value)) || Number(value) < 0) {
          callback(new Error('库存数量必须是大于等于0的数字'));
        } else if (!Number.isInteger(Number(value))) {
          callback(new Error('库存数量必须是整数'));
        } else if (Number(value) > 999999) {
          callback(new Error('库存数量不能超过999999'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ]
};

// 添加商品
const showAddProductDialog = () => {
  isEditing.value = false;
  Object.assign(productForm, {
    id: null,
    name: '',
    categoryId: '',
    lyPrice: 0,
    rmbPrice: 0,
    description: '',
    stock: 0,
    exchangeCount: 0,
    isNew: false,
    isHot: false,
    status: 'active',
    createdAt: null
  });
  productDialogVisible.value = true;
};

// 编辑商品
const handleEdit = (product) => {
  isEditing.value = true;
  Object.assign(productForm, {
    id: product.id,
    name: product.name,
    categoryId: product.categoryId,
    lyPrice: product.lyPrice,
    rmbPrice: product.rmbPrice,
    description: product.description || '',
    stock: product.stock,
    exchangeCount: product.exchangeCount || 0,
    isNew: product.isNew,
    isHot: product.isHot,
    status: product.status,
    createdAt: product.createdAt
  });
  productDialogVisible.value = true;
};

// 提交商品表单
const submitProductForm = async () => {
  if (!productFormRef.value) return;
  
  await productFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    submitting.value = true;
    
    try {
      const productData = { ...productForm };
      let result;
      
      if (isEditing.value) {
        const { id, ...data } = productData;
        result = await updateProduct(id, data);
        ElMessage.success('商品更新成功');
      } else {
        result = await createProduct(productData);
        ElMessage.success('商品添加成功');
      }
      
      productDialogVisible.value = false;
      fetchProducts();
    } catch (error) {
      console.error('保存商品失败:', error);
      ElMessage.error('保存商品失败');
    } finally {
      submitting.value = false;
    }
  });
};

// 切换商品状态
const handleToggleStatus = async (product) => {
  try {
    const newStatus = product.status === 'active' ? 'inactive' : 'active';
    await updateProductStatus(product.id, newStatus);
    ElMessage.success(`商品已${newStatus === 'active' ? '上架' : '下架'}`);
    fetchProducts();
  } catch (error) {
    console.error('更新商品状态失败:', error);
    ElMessage.error('更新商品状态失败');
  }
};

// 删除商品
const handleDelete = async (productId) => {
  try {
    await deleteProduct(productId);
    ElMessage.success('商品已删除');
    fetchProducts();
  } catch (error) {
    console.error('删除商品失败:', error);
    ElMessage.error('删除商品失败');
  }
};

// 商品图片管理
const imagesDialogVisible = ref(false);
const currentProduct = ref(null);
const productImages = ref([]);
const imageFileList = ref([]);
const uploadingImages = ref(false);
const deletingImageId = ref(null);

// 打开图片管理对话框
const handleImages = async (product) => {
  currentProduct.value = product;
  imagesDialogVisible.value = true;
  await fetchProductImages(product.id);
};

// 获取商品图片
const fetchProductImages = async (productId) => {
  try {
    const images = await getProductImages(productId);
    // 按照 sortOrder 排序
    productImages.value = images.sort((a, b) => a.sortOrder - b.sortOrder);
  } catch (error) {
    console.error('获取商品图片失败:', error);
    ElMessage.error('获取商品图片失败');
  }
};

// 处理图片上传前的操作
const handleImageChange = (file, fileList) => {
  imageFileList.value = fileList;
};

const handleImageRemove = (file, fileList) => {
  imageFileList.value = fileList;
};

// 上传商品图片
const uploadImages = async () => {
  if (imageFileList.value.length === 0) {
    ElMessage.warning('请先选择图片');
    return;
  }
  
  uploadingImages.value = true;
  
  try {
    console.log(`正在上传 ${imageFileList.value.length} 张图片...`);
    
    const promises = imageFileList.value.map(async file => {
      const formData = new FormData();
      // 确保字段名与后端 multer 中间件配置匹配
      formData.append('image', file.raw);
      
      console.log(`准备上传图片: ${file.name}, 大小: ${file.size} 字节`);
      
      try {
        const result = await uploadProductImage(currentProduct.value.id, formData);
        console.log('图片上传成功:', result);
        return result;
      } catch (uploadError) {
        console.error(`图片 ${file.name} 上传失败:`, uploadError);
        throw uploadError;
      }
    });
    
    const results = await Promise.all(promises);
    console.log('所有图片上传完成:', results);
    
    ElMessage.success(`成功上传 ${results.length} 张图片`);
    imageFileList.value = [];
    await fetchProductImages(currentProduct.value.id);
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error(`上传图片失败: ${error.message || '未知错误'}`);
  } finally {
    uploadingImages.value = false;
  }
};

// 更新图片排序
const updateImageSortOrder = async (image) => {
  try {
    await updateProductImage(image.id, { sortOrder: image.sortOrder });
    ElMessage.success('图片排序已更新');
    await fetchProductImages(currentProduct.value.id);
  } catch (error) {
    console.error('更新图片排序失败:', error);
    ElMessage.error('更新图片排序失败');
  }
};

// 设置主图（排序为0的图片）
const setMainImage = async (image) => {
  try {
    // 获取当前所有图片的排序信息
    const currentImages = [...productImages.value];
    const targetImageIndex = currentImages.findIndex(img => img.id === image.id);
    
    if (targetImageIndex === -1) {
      ElMessage.error('图片不存在');
      return;
    }
    
    // 如果已经是主图，无需操作
    if (currentImages[targetImageIndex].sortOrder === 0) {
      ElMessage.info('该图片已经是主图');
      return;
    }
    
    // 批量更新排序：将目标图片设为0，其他图片按原顺序递增
    const updatePromises = currentImages.map((img, index) => {
      let newSortOrder;
      if (img.id === image.id) {
        newSortOrder = 0; // 设为主图
      } else if (img.sortOrder < currentImages[targetImageIndex].sortOrder) {
        newSortOrder = img.sortOrder + 1; // 原来排在前面的图片后移一位
      } else {
        newSortOrder = img.sortOrder; // 其他图片保持不变
      }
      
      // 只更新需要改变排序的图片
      if (newSortOrder !== img.sortOrder) {
        return updateProductImage(img.id, { sortOrder: newSortOrder });
      }
      return Promise.resolve();
    });
    
    // 等待所有更新完成
    await Promise.all(updatePromises);
    
    ElMessage.success('已设置为主图');
    await fetchProductImages(currentProduct.value.id);
  } catch (error) {
    console.error('设置主图失败:', error);
    ElMessage.error('设置主图失败');
  }
};

// 删除商品图片
const deleteImage = async (imageId) => {
  deletingImageId.value = imageId;
  
  try {
    await deleteProductImage(imageId);
    ElMessage.success('图片已删除');
    await fetchProductImages(currentProduct.value.id);
  } catch (error) {
    console.error('删除图片失败:', error);
    ElMessage.error('删除图片失败');
  } finally {
    deletingImageId.value = null;
  }
};

// 处理文件上传
const handleImportFileChange = (file, fileList) => {
  importFileList.value = fileList;
};

// 下载模板
const downloadTemplate = () => {
  // 创建CSV模板内容
  const headers = ['商品名称', '分类', '光年币价格', '人民币价格', '库存', '累计兑换', '描述', '是否新品', '是否热门', '状态'];
  const csvContent = 
    headers.join(',') + '\n' + 
    '示例商品,日常用品,100,10.00,50,0,这是一个示例商品,是,是,上架';
  
  // 创建Blob对象
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  try {
    // 创建下载链接并触发下载
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', '商品导入模板.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } finally {
    // 及时清理URL对象，避免内存泄漏
    URL.revokeObjectURL(url);
  }
};

// 导入商品
const handleImport = async () => {
  if (importFileList.value.length === 0) {
    ElMessage.warning('请先选择要导入的文件');
    return;
  }
  
  const file = importFileList.value[0].raw;
  const formData = new FormData();
  formData.append('file', file);
  
  importing.value = true;
  
  try {
    const result = await importProducts(formData);
    ElMessage.success(`导入成功，共导入${result.count}件商品`);
    importFileList.value = [];
    fetchProducts(); // 刷新列表
  } catch (error) {
    console.error('导入商品失败:', error);
    ElMessage.error(`导入失败: ${error.message || '未知错误'}`);
  } finally {
    importing.value = false;
  }
};

// 导出商品
const handleExport = async () => {
  exporting.value = true;
  
  try {
    // 确认导出格式
    const action = await ElMessageBox.confirm(
      '请选择导出格式',
      '导出商品数据',
      {
        confirmButtonText: 'Excel',
        cancelButtonText: 'CSV',
        type: 'info',
        showClose: false,
        distinguishCancelAndClose: true,
      }
    ).then(() => 'excel').catch(action => {
      if (action === 'cancel') {
        return 'csv';
      }
      throw action; // 重新抛出其他类型的取消操作
    });
    
    await exportProductsData(action);
  } catch (error) {
    if (error === 'close' || error === 'cancel') {
      // 用户取消操作，不显示错误
      console.log('用户取消导出操作');
    } else {
      console.error('导出对话框错误:', error);
      ElMessage.error('导出操作失败');
    }
  } finally {
    exporting.value = false;
  }
};

// 导出商品数据
const exportProductsData = async (format) => {
  try {
    // 构建查询参数，包含当前筛选条件
    const params = {
      ...filterParams,
      format
    };
    
    // 发起请求并处理文件下载
    const response = await exportProducts(params);
    
    // 创建blob链接
    const blob = new Blob([response], { 
      type: format === 'excel' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        : 'text/csv;charset=utf-8'
    });
    const url = window.URL.createObjectURL(blob);
    
    try {
      // 创建临时链接并下载
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `products-${Date.now()}.${format === 'excel' ? 'xlsx' : 'csv'}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      ElMessage.success('导出成功');
    } finally {
      // 及时清理URL对象，避免内存泄漏
      window.URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error('导出商品失败:', error);
    ElMessage.error('导出商品失败');
  }
};

// 库存阈值常量
const STOCK_THRESHOLD = 5;

// 判断是否低库存
const isLowStock = (stock) => {
  return stock < STOCK_THRESHOLD;
};

// 低库存行样式
const lowStockRowClass = ({ row }) => {
  if (isLowStock(row.stock) && row.status === 'active') {
    return 'low-stock-row';
  }
  return '';
};

// 处理URL参数
const handleUrlParams = () => {
  // 处理库存告警通知跳转
  if (route.query.lowStock === 'true' && route.query.id) {
    const productId = parseInt(route.query.id);
    
    // 设置筛选条件
    filterParams.showAll = true;
    
    // 查找目标商品
    const targetProduct = products.value.find(p => p.id === productId);
    if (targetProduct) {
      // 滚动到该商品行
      setTimeout(() => {
        const el = document.querySelector(`.el-table__row[data-row-key="${productId}"]`);
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'center' });
          // 高亮显示
          el.classList.add('highlight-row');
          setTimeout(() => {
            el.classList.remove('highlight-row');
          }, 3000);
        }
      }, 300);
    }
  }
};

// 监听路由参数变化
watch(() => route.query, (newQuery, oldQuery) => {
  // 只在查询参数真正变化时处理，避免初始化时的不必要处理
  if (!oldQuery || (newQuery.lowStock === 'true' && newQuery.id && 
      (oldQuery.lowStock !== 'true' || oldQuery.id !== newQuery.id))) {
    // 清除URL参数但不刷新页面
    router.replace({ path: route.path, query: {} }, null, { shallow: true });
    
    // 处理库存告警跳转
    const productId = parseInt(newQuery.id);
    filterParams.showAll = true; // 显示所有商品，包括下架的
    fetchProducts().then(() => {
      // 查找目标商品
      const targetProduct = products.value.find(p => p.id === productId);
      if (targetProduct) {
        ElMessage.warning({
          message: `商品 "${targetProduct.name}" 库存不足，当前剩余 ${targetProduct.stock} 个`,
          duration: 5000
        });
        
        // 滚动到该商品行
        setTimeout(() => {
          const el = document.querySelector(`.el-table__row`);
          if (el) {
            el.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // 高亮显示
            el.classList.add('highlight-row');
            setTimeout(() => {
              el.classList.remove('highlight-row');
            }, 3000);
          }
        }, 300);
      }
    });
  }
});

// 处理表格选择变化
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

// 批量删除商品
const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一个商品');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个商品吗？此操作不可恢复。`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    batchDeleteLoading.value = true;
    const ids = multipleSelection.value.map(item => item.id);
    
    await bulkDeleteProducts(ids);
    ElMessage.success(`成功删除 ${multipleSelection.value.length} 个商品`);
    multipleSelection.value = [];
    fetchProducts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除商品失败:', error);
      ElMessage.error('批量删除商品失败');
    }
  } finally {
    batchDeleteLoading.value = false;
  }
};

// 处理排序变化
const handleSortChange = (value) => {
  console.log('商品管理排序选择器变化:', { 
    oldValue: filterParams.sort, 
    newValue: value 
  });
  
  console.log('排序值发生变化，触发API请求');
  
  // 确保值已经更新（v-model会自动更新，但为了保险起见再次设置）
  filterParams.sort = value;
  
  // 重置到第一页
  currentPage.value = 1;
  
  // 立即触发数据获取
  fetchProducts();
};

// 处理搜索变化
const handleSearchChange = debounce((value) => {
  console.log('商品管理搜索变化:', { 
    oldValue: filterParams.search, 
    newValue: value 
  });
  
  // 重置到第一页并触发数据获取
  currentPage.value = 1;
  fetchProducts();
}, 300);

// 处理分类变化
const handleCategoryChange = (value) => {
  console.log('商品管理分类选择器变化:', { 
    oldValue: filterParams.category, 
    newValue: value 
  });
  
  console.log('分类值发生变化，触发API请求');
  
  // 确保值已经更新（v-model会自动更新，但为了保险起见再次设置）
  filterParams.category = value;
  
  // 重置到第一页
  currentPage.value = 1;
  
  // 立即触发数据获取
  fetchProducts();
};

// 处理显示下架商品的变化
const handleShowAllChange = () => {
  console.log('显示下架商品变化:', { 
    oldValue: filterParams.showAll, 
    newValue: filterParams.showAll 
  });
  
  // 重置到第一页并触发数据获取
  currentPage.value = 1;
  fetchProducts();
};

// 初始化
onMounted(async () => {
  await fetchCategoriesData();
  await fetchPriceRanges();
  await fetchProducts();
  handleUrlParams(); // 处理URL参数
});
</script>

<style scoped>
.product-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.import-upload {
  display: flex;
  align-items: center;
}

.el-upload__tip {
  margin-left: 10px;
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.upload-container .el-upload {
  margin-bottom: 10px;
}

.preview-gallery {
  margin: 20px 0;
}

.preview-gallery h3 {
  margin-bottom: 15px;
  font-weight: normal;
  color: #606266;
}

.image-item {
  margin-bottom: 20px;
}

.image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  display: block;
}

.image-sort {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.image-actions {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}

/* 新增样式 */
.product-name-cell {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 5px 0;
}

.tags-container {
  display: flex;
  gap: 5px;
  margin-top: 5px;
}

.operation-buttons {
  display: flex;
  flex-direction: row;
  gap: 5px;
  justify-content: center;
}

.mb-8 {
  margin-bottom: 0;
}

.rmb-price {
  color: #F56C6C;
  font-weight: 500;
}

.el-table :deep(.el-button + .el-button) {
  margin-left: 0;
}

.el-table :deep(.el-button-group .el-button + .el-button) {
  margin-left: -1px;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters > * {
    width: 100% !important;
    margin-bottom: 10px;
  }
  
  .image-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .image-actions .el-button {
    width: 100%;
  }
}

/* 高级筛选区域样式 */
.advanced-filters {
  margin-top: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.price-range {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin: 16px 0;
}

.price-group {
  flex: 1;
  min-width: 280px;
}

.price-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 16px 0;
}

.date-range {
  margin: 16px 0;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  justify-content: flex-end;
}

/* 活跃筛选条件样式 */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #f0f0f0;
}

.active-filters-title {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.active-filter-tag {
  margin-bottom: 8px;
}

.low-stock {
  color: #e6a23c;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.warning-icon {
  color: #e6a23c;
}

:deep(.low-stock-row) {
  background-color: #fef5eb !important;
}

:deep(.highlight-row) {
  background-color: #fdf4f4 !important;
  transition: background-color 0.3s;
  box-shadow: 0 0 8px rgba(230, 162, 60, 0.5);
}
</style> 
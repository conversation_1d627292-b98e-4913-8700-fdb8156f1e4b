<template>
  <div class="help-doc product-management">
    <h2>商品管理</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>商品列表</h3>
        </div>
      </template>
      <div class="card-content">
        <p>商品管理页面是系统的核心功能之一，用于管理所有商品信息。您可以在这里添加、编辑、删除商品，以及进行上下架操作。</p>
        
        <div class="image-container">
          <el-image src="https://example.com/images/product-list.jpg" fit="contain">
            <template #error>
              <div class="image-placeholder">
                <el-icon><Picture /></el-icon>
                <span>商品列表示例图片</span>
              </div>
            </template>
          </el-image>
          <div class="image-caption">商品列表界面</div>
        </div>
        
        <h4>功能说明</h4>
        <ul>
          <li><strong>搜索筛选</strong>：您可以通过商品名称、商品编号、分类等条件筛选商品</li>
          <li><strong>高级筛选</strong>：支持按价格范围、库存状态、上架时间等多维度筛选</li>
          <li><strong>批量操作</strong>：支持批量上下架、删除、分类调整等操作</li>
          <li><strong>排序</strong>：可以按照价格、库存、上架时间等字段进行排序</li>
          <li><strong>快速编辑</strong>：部分字段支持直接在列表中编辑</li>
          <li><strong>商品标签</strong>：可为商品添加自定义标签，实现灵活的商品分组管理</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>添加/编辑商品</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>基本信息</h4>
        <p>添加或编辑商品时，您需要填写以下基本信息：</p>
        <ul>
          <li><strong>商品名称</strong>：商品的名称，建议简洁明了</li>
          <li><strong>商品分类</strong>：选择商品所属的分类</li>
          <li><strong>商品价格</strong>：设置商品的价格</li>
          <li><strong>库存数量</strong>：设置商品的库存</li>
          <li><strong>商品状态</strong>：上架、下架或草稿状态</li>
          <li><strong>商品标签</strong>：为商品添加标签，便于分组和查找</li>
        </ul>
        
        <el-alert
          title="注意：商品名称一旦创建后建议不要频繁修改，以免影响用户查找"
          type="warning"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
        
        <h4>图片上传</h4>
        <p>您可以为商品上传多张图片，第一张图片将作为商品的主图。支持的图片格式包括JPG、PNG、WEBP等。系统支持图片裁剪和简单编辑功能。</p>
        
        <h4>商品描述</h4>
        <p>商品描述支持富文本编辑，您可以添加文字、图片、表格等内容，详细描述商品的特点和使用方法。</p>
        
        <h4>多语言描述</h4>
        <p>系统支持为商品添加多语言描述，您可以为每种支持的语言单独设置商品名称、简介和详细描述，满足国际化需求。</p>
        
        <h4>商品变体</h4>
        <p>对于具有多种型号或规格的商品，您可以使用商品变体功能，为不同规格设置不同的价格和库存：</p>
        <ol>
          <li>在商品编辑页面，切换到「商品变体」选项卡</li>
          <li>添加规格类型（如颜色、尺寸等）</li>
          <li>为每种规格添加选项值</li>
          <li>系统会自动生成所有可能的组合</li>
          <li>为每个组合设置价格、库存等信息</li>
        </ol>
        
        <div class="steps-container">
          <h4>操作步骤</h4>
          <el-steps direction="vertical" :active="4">
            <el-step title="点击「添加商品」按钮" description="在商品列表页面点击右上角的添加商品按钮" />
            <el-step title="填写基本信息" description="输入商品名称、选择分类、设置价格和库存等" />
            <el-step title="上传商品图片" description="上传商品的主图和详情图片" />
            <el-step title="编写商品描述" description="使用富文本编辑器添加商品详细描述" />
            <el-step title="设置多语言和变体" description="根据需要设置多语言描述和商品变体" />
            <el-step title="保存提交" description="点击保存按钮完成商品添加" />
          </el-steps>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>常见问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse>
          <el-collapse-item title="如何批量导入商品？" name="1">
            <p>系统支持通过Excel表格批量导入商品。点击商品列表页面右上角的「批量导入」按钮，下载最新导入模板，填写商品信息后上传即可。新版模板支持设置多语言描述和商品变体信息。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何设置商品促销？" name="2">
            <p>在编辑商品页面，您可以设置商品的促销信息，包括促销价格、促销时间等。设置后，系统会在促销时间内自动显示促销价格。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何复制现有商品？" name="3">
            <p>在商品列表页面，点击操作列中的「复制」按钮，系统会创建一个与原商品信息相同的新商品（商品编号会重新生成），您可以在此基础上进行修改。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何查看商品销售情况？" name="4">
            <p>在商品列表页面，点击操作列中的「数据」按钮，可以查看该商品的销售数据和趋势图表。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何设置商品的多语言描述？" name="5">
            <p>设置商品多语言描述的步骤：</p>
            <ol>
              <li>在商品编辑页面，切换到「多语言设置」选项卡</li>
              <li>您会看到系统支持的所有语言选项卡</li>
              <li>在每种语言的选项卡中，填写对应语言的商品名称、简介和详细描述</li>
              <li>如果某种语言没有填写，系统会显示默认语言（通常是中文）的内容</li>
              <li>保存商品后，用户可以在前台切换语言查看不同语言的商品信息</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="如何使用商品标签功能？" name="6">
            <p>使用商品标签功能可以灵活管理商品分组：</p>
            <ol>
              <li>在商品列表页面，可以通过批量操作为多个商品添加同一标签</li>
              <li>在商品编辑页面，可以在「基本信息」区域添加或移除标签</li>
              <li>在商品列表页面，可以使用左侧的标签筛选器快速找到带有特定标签的商品</li>
              <li>标签可以自定义颜色，便于直观区分不同类型的标签</li>
              <li>标签可以用于临时活动、特殊分类或任何需要灵活分组的场景</li>
            </ol>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>操作技巧</h3>
        </div>
      </template>
      <div class="card-content">
        <el-tag type="success" effect="dark" class="tip-tag">技巧1</el-tag>
        <p>使用快捷键 <code>Ctrl+S</code>（Mac上为 <code>Command+S</code>）可以快速保存正在编辑的商品。</p>
        
        <el-tag type="success" effect="dark" class="tip-tag">技巧2</el-tag>
        <p>商品列表支持拖拽排序，您可以通过拖拽调整商品在前台的显示顺序。</p>
        
        <el-tag type="success" effect="dark" class="tip-tag">技巧3</el-tag>
        <p>使用标签功能对商品进行分类管理，可以更方便地进行批量操作和数据统计。</p>
        
        <el-tag type="success" effect="dark" class="tip-tag">技巧4</el-tag>
        <p>商品图片支持裁剪功能，您可以在上传后调整图片的显示区域。</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Picture } from '@element-plus/icons-vue';
</script>

<style scoped>
.product-management h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.image-container {
  margin: 15px 0;
  text-align: center;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #f5f7fa;
  color: #909399;
  border: 1px dashed #d9d9d9;
}

.image-placeholder .el-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.image-caption {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.steps-container {
  margin: 20px 0;
}

.tip-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

code {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  color: #c41d7f;
}
</style> 
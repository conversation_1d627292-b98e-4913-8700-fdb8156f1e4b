<template>
  <div class="help-doc system-intro">
    <h2>系统介绍</h2>
    
    <el-card class="intro-card">
      <template #header>
        <div class="card-header">
          <h3>系统概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>光年小卖部管理系统是一套完整的商品管理解决方案，用于便捷管理商品、分类、用户和反馈等数据。系统采用现代化的设计，提供了丰富的功能和良好的用户体验。</p>
        
        <h4>主要功能模块</h4>
        <ul>
          <li><strong>数据仪表盘</strong>：展示系统运营数据和统计信息，帮助管理员快速了解系统状态</li>
          <li><strong>商品管理</strong>：管理所有商品信息，包括添加、编辑、删除和上下架操作</li>
          <li><strong>分类管理</strong>：对商品分类进行管理，支持多级分类结构</li>
          <li><strong>用户管理</strong>：管理系统用户，包括权限设置和账号状态管理</li>
          <li><strong>反馈管理</strong>：处理用户提交的反馈和建议</li>
          <li><strong>公告管理</strong>：发布和管理系统公告</li>
          <li><strong>系统日志</strong>：记录和查询系统操作记录，提高系统安全性和可追溯性</li>
          <li><strong>数据导出</strong>：支持多种格式的数据导出功能，便于数据分析和备份</li>
          <li><strong>多语言支持</strong>：系统界面和内容支持多语言切换，满足不同用户需求</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="intro-card">
      <template #header>
        <div class="card-header">
          <h3>系统特点</h3>
        </div>
      </template>
      <div class="card-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#409EFF"><Ship /></el-icon>
              <h4>高效管理</h4>
              <p>简化商品和订单管理流程，提高工作效率</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#67C23A"><DataAnalysis /></el-icon>
              <h4>数据分析</h4>
              <p>提供详细的数据统计和可视化图表，辅助决策制定</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#E6A23C"><Connection /></el-icon>
              <h4>互动反馈</h4>
              <p>及时处理用户反馈，提升用户满意度</p>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#F56C6C"><Cellphone /></el-icon>
              <h4>移动端适配</h4>
              <p>完美支持各种移动设备，随时随地管理系统</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#909399"><ChatLineRound /></el-icon>
              <h4>多语言支持</h4>
              <p>支持中文、英文等多种语言，满足国际化需求</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#9254DE"><Lock /></el-icon>
              <h4>安全可靠</h4>
              <p>严格的权限控制和完整的操作日志，确保数据安全</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <el-card class="intro-card">
      <template #header>
        <div class="card-header">
          <h3>系统要求</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>推荐浏览器</h4>
        <p>为了获得最佳使用体验，建议使用以下现代浏览器：</p>
        <ul>
          <li>Google Chrome 100+</li>
          <li>Mozilla Firefox 95+</li>
          <li>Microsoft Edge 100+</li>
          <li>Safari 15+</li>
        </ul>
        
        <h4>设备支持</h4>
        <p>本系统完全支持在以下设备上使用：</p>
        <ul>
          <li>桌面电脑（Windows、macOS、Linux）</li>
          <li>平板电脑（iPad、Android平板）</li>
          <li>手机（iPhone、Android手机）</li>
        </ul>
        
        <h4>网络要求</h4>
        <p>系统对网络要求如下：</p>
        <ul>
          <li>稳定的网络连接</li>
          <li>推荐带宽：1Mbps以上</li>
          <li>支持HTTPS安全协议</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Ship, DataAnalysis, Connection, Cellphone, ChatLineRound, Lock } from '@element-plus/icons-vue';
</script>

<style scoped>
.system-intro h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.intro-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.feature-item {
  text-align: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
  height: 100%;
}

.feature-item h4 {
  margin: 12px 0;
}

.feature-item p {
  font-size: 14px;
  color: #606266;
}
</style> 
<template>
  <div class="help-doc faq">
    <h2>常见问题解答</h2>
    
    <div class="search-container">
      <el-input
        v-model="searchText"
        placeholder="搜索常见问题..."
        prefix-icon="Search"
        clearable
        @input="handleSearch"
      />
    </div>
    
    <el-card class="help-card" v-if="filteredQuestions.length > 0">
      <template #header>
        <div class="card-header">
          <h3>常见问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse v-model="activeNames">
          <el-collapse-item 
            v-for="(item, index) in filteredQuestions" 
            :key="index" 
            :title="item.title" 
            :name="index.toString()"
          >
            <div v-html="item.answer"></div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    
    <el-empty v-else description="没有找到匹配的问题，请尝试其他关键词" />
    
    <el-divider content-position="center">
      <span class="divider-text">没有找到您的问题？</span>
    </el-divider>
    
    <div class="contact-container">
      <p>如果您的问题在常见问题中没有找到解答，您可以通过以下方式联系我们：</p>
      <el-row :gutter="20" class="contact-ways">
        <el-col :span="8">
          <div class="contact-item">
            <el-icon size="40" color="#409EFF"><ChatDotRound /></el-icon>
            <h4>在线客服</h4>
            <p>工作时间: 周一至周五 9:00-18:00</p>
            <el-button type="primary">在线咨询</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="contact-item">
            <el-icon size="40" color="#67C23A"><Message /></el-icon>
            <h4>电子邮件</h4>
            <p>我们会在24小时内回复您的邮件</p>
            <el-button type="success">发送邮件</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="contact-item">
            <el-icon size="40" color="#E6A23C"><Document /></el-icon>
            <h4>提交工单</h4>
            <p>详细描述您的问题，获得技术支持</p>
            <el-button type="warning">创建工单</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Search, ChatDotRound, Message, Document } from '@element-plus/icons-vue';

// 常见问题数据
const questions = [
  {
    title: '如何修改个人账户密码？',
    answer: '您可以通过以下步骤修改密码：<ol><li>点击右上角的用户头像</li><li>在下拉菜单中选择"个人中心"</li><li>切换到"账户安全"选项卡</li><li>点击"修改密码"按钮</li><li>输入原密码和新密码，点击确认即可</li></ol>'
  },
  {
    title: '忘记密码怎么办？',
    answer: '如果您忘记了密码，可以通过以下方式重置：<ol><li>在登录页面点击"忘记密码"</li><li>输入您的注册邮箱或手机号</li><li>系统会向您的邮箱或手机发送验证码</li><li>输入验证码后，您可以设置新密码</li></ol>'
  },
  {
    title: '如何导出数据报表？',
    answer: '导出数据报表的步骤如下：<ol><li>进入对应的数据页面（如销售报表、商品数据等）</li><li>设置好需要导出的日期范围和筛选条件</li><li>点击页面右上角的"导出"按钮</li><li>选择导出格式（Excel、PDF、CSV或JSON）</li><li>如需自定义导出字段，可点击"自定义"选项</li><li>点击确认，文件将自动下载到您的设备上</li></ol>'
  },
  {
    title: '如何添加新用户并设置权限？',
    answer: '添加新用户并设置权限的步骤：<ol><li>进入"用户管理"页面</li><li>点击"添加用户"按钮</li><li>填写用户基本信息（姓名、邮箱、手机等）</li><li>在"角色权限"区域，为用户分配适当的角色</li><li>如需更细粒度的权限控制，可点击"自定义权限"</li><li>设置完成后点击"保存"按钮</li></ol>'
  },
  {
    title: '系统支持哪些浏览器？',
    answer: '我们的系统支持以下现代浏览器的最新版本：<ul><li>Google Chrome 100+</li><li>Mozilla Firefox 95+</li><li>Microsoft Edge 100+</li><li>Safari 15+</li></ul>为获得最佳体验，我们建议使用Chrome或Firefox浏览器。'
  },
  {
    title: '如何设置系统通知？',
    answer: '设置系统通知的步骤：<ol><li>点击右上角用户头像，选择"系统设置"</li><li>切换到"通知设置"选项卡</li><li>您可以选择启用或禁用不同类型的通知</li><li>对于重要通知，您可以设置额外的提醒方式（如邮件、短信）</li><li>完成设置后点击"保存"按钮</li></ol>'
  },
  {
    title: '如何批量处理数据？',
    answer: '系统支持多种批量处理方式：<ol><li>在列表页面，使用左侧的复选框选择需要处理的项目</li><li>选择完成后，点击上方的批量操作按钮</li><li>根据不同数据类型，可用的批量操作包括：<ul><li>批量修改状态</li><li>批量删除</li><li>批量导出</li><li>批量分类/标记</li></ul></li><li>确认操作后，系统会处理所有选中的项目</li></ol>'
  },
  {
    title: '遇到系统错误怎么办？',
    answer: '如果您遇到系统错误，请尝试以下步骤：<ol><li>刷新页面，看问题是否仍然存在</li><li>清除浏览器缓存并重新登录</li><li>尝试使用不同的浏览器</li><li>记录错误发生的时间和操作步骤</li><li>联系系统管理员或技术支持，提供详细的错误信息</li></ol>大多数情况下，简单的刷新或重新登录可以解决临时性问题。'
  },
  {
    title: '数据安全如何保障？',
    answer: '我们采取多层次的措施保障数据安全：<ul><li><strong>数据传输安全</strong>：所有数据通过HTTPS加密传输</li><li><strong>数据存储安全</strong>：数据库采用强加密存储敏感信息</li><li><strong>访问控制</strong>：严格的权限管理和身份验证</li><li><strong>数据备份</strong>：定期自动备份，确保数据不会丢失</li><li><strong>安全审计</strong>：系统自动记录所有关键操作的日志</li></ul>'
  },
  {
    title: '如何使用数据分析功能？',
    answer: '使用数据分析功能的基本步骤：<ol><li>进入"数据仪表盘"页面</li><li>选择您感兴趣的数据类型（销售、用户、商品等）</li><li>设置日期范围和其他筛选条件</li><li>系统会自动生成数据图表和关键指标</li><li>您可以通过点击图表上的数据点查看更详细的信息</li><li>对于特定分析需求，可以使用"自定义报表"功能</li></ol>'
  },
  {
    title: '如何使用多语言功能？',
    answer: '使用系统多语言功能的步骤：<ol><li>点击页面右上角的语言切换图标</li><li>在下拉菜单中选择您需要的语言</li><li>系统界面将立即切换为所选语言</li><li>如果您是管理员，还可以在"系统设置"中配置默认语言</li><li>添加商品时，您可以为每种支持的语言输入商品名称和描述</li></ol>目前系统支持简体中文、英文、日文和韩文。'
  },
  {
    title: '如何在移动设备上获得最佳体验？',
    answer: '在移动设备上使用系统的建议：<ol><li>使用最新版本的移动浏览器（Chrome、Safari等）</li><li>保持设备系统更新到最新版本</li><li>使用设备的横屏模式可以获得更好的操作体验</li><li>对于复杂的数据分析和批量操作，建议使用平板电脑而非手机</li><li>可以将系统添加到设备主屏幕，获得类似App的使用体验</li></ol>系统会自动适应您的屏幕大小，提供最佳的移动端体验。'
  },
  {
    title: '如何使用商品标签功能？',
    answer: '使用商品标签功能的步骤：<ol><li>在商品列表或编辑页面，找到"标签管理"区域</li><li>点击"添加标签"按钮创建新标签，或从现有标签中选择</li><li>您可以为标签设置颜色和描述，方便识别</li><li>在商品列表中，可以按标签筛选商品</li><li>还可以对带有特定标签的商品进行批量操作</li></ol>商品标签是一种灵活的方式，可以按照您的业务需求组织和管理商品，不受分类限制。'
  }
];

// 当前激活的折叠面板
const activeNames = ref(['0']);
// 搜索文本
const searchText = ref('');

// 根据搜索文本过滤问题
const filteredQuestions = computed(() => {
  if (!searchText.value) return questions;
  
  const search = searchText.value.toLowerCase();
  return questions.filter(q => 
    q.title.toLowerCase().includes(search) || 
    q.answer.toLowerCase().includes(search)
  );
});

// 处理搜索
const handleSearch = () => {
  // 如果只有一个结果，自动展开
  if (filteredQuestions.value.length === 1) {
    activeNames.value = ['0'];
  }
};
</script>

<style scoped>
.faq h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.search-container {
  margin-bottom: 20px;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.divider-text {
  font-size: 14px;
  color: #909399;
}

.contact-container {
  margin-top: 20px;
  text-align: center;
}

.contact-container p {
  margin-bottom: 20px;
  color: #606266;
}

.contact-ways {
  margin-top: 20px;
}

.contact-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  height: 100%;
  background-color: #fafafa;
  transition: all 0.3s;
}

.contact-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.contact-item h4 {
  margin: 12px 0;
  color: #303133;
}

.contact-item p {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.contact-item .el-button {
  width: 100%;
}
</style> 
<template>
  <div class="help-doc user-management">
    <h2>用户管理</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>用户管理概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>用户管理模块允许管理员创建、编辑和管理系统用户账户，控制用户权限，以及监控用户活动。有效的用户管理对于系统安全和运营效率至关重要。</p>
        
        <div class="info-block">
          <h4>主要功能</h4>
          <ul>
            <li>用户账号创建与管理</li>
            <li>用户权限设置</li>
            <li>用户状态监控与统计</li>
            <li>用户操作日志查看</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>用户列表</h3>
        </div>
      </template>
      <div class="card-content">
        <p>用户列表页面展示了系统中的所有用户，提供了多种筛选和管理功能。</p>
        
        <h4>列表操作说明</h4>
        <ul>
          <li><strong>搜索功能</strong>：可通过姓名、邮箱、手机号等条件筛选用户</li>
          <li><strong>状态筛选</strong>：可按用户状态（活跃、禁用、待审核等）进行筛选</li>
          <li><strong>角色筛选</strong>：可按用户角色（管理员、普通用户等）进行筛选</li>
          <li><strong>批量操作</strong>：支持批量启用/禁用、批量分配角色等操作</li>
        </ul>
        
        <el-alert
          title="注意：系统会自动保护超级管理员账号，防止误操作导致无法登录"
          type="info"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>添加/编辑用户</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>基本信息</h4>
        <p>添加或编辑用户时，需要填写以下基本信息：</p>
        <ul>
          <li><strong>姓名</strong>：用户的登录名，创建后不可修改</li>
          <li><strong>密码</strong>：默认密码或修改密码，应符合系统安全策略</li>
          <li><strong>邮箱</strong>：用户的电子邮箱，用于接收系统通知和找回密码</li>
          <li><strong>手机号</strong>：用户的联系电话，可用于短信验证</li>
          <li><strong>用户状态</strong>：启用、禁用或审核中</li>
        </ul>
        
        <h4>角色与权限设置</h4>
        <p>为用户分配适当的角色和权限：</p>
        <ul>
          <li><strong>角色选择</strong>：从预设角色中选择（如管理员、编辑、查看者等）</li>
          <li><strong>自定义权限</strong>：可单独设置特定功能的权限</li>
          <li><strong>数据访问权限</strong>：设置用户可访问的数据范围</li>
        </ul>
        
        <div class="steps-container">
          <h4>添加用户步骤</h4>
          <el-steps direction="vertical" :active="4">
            <el-step title="点击「添加用户」按钮" description="在用户列表页面点击右上角的添加用户按钮" />
            <el-step title="填写基本信息" description="输入姓名、邮箱、手机号等基本信息" />
            <el-step title="设置密码" description="设置初始密码，或选择系统生成随机密码" />
            <el-step title="选择角色和权限" description="为用户分配合适的角色和权限" />
            <el-step title="保存提交" description="点击保存按钮完成用户创建" />
          </el-steps>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>用户权限管理</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>角色管理</h4>
        <p>系统预设了以下常用角色：</p>
        <el-table :data="roles" style="width: 100%">
          <el-table-column prop="name" label="角色名称" width="180" />
          <el-table-column prop="description" label="说明" />
        </el-table>
        
        <h4>自定义角色</h4>
        <p>如果预设角色不满足需求，您可以创建自定义角色：</p>
        <ol>
          <li>进入「角色管理」页面</li>
          <li>点击「新建角色」按钮</li>
          <li>设置角色名称和描述</li>
          <li>勾选该角色应具有的权限</li>
          <li>点击「保存」按钮创建角色</li>
        </ol>
        
        <h4>权限说明</h4>
        <p>系统权限分为以下几个部分：</p>
        <ul>
          <li><strong>功能权限</strong>：控制用户可以访问的功能和页面</li>
          <li><strong>数据权限</strong>：控制用户可以查看和操作的数据范围</li>
          <li><strong>操作权限</strong>：控制用户在特定功能中可以执行的操作</li>
          <li><strong>API访问权限</strong>：控制用户通过API接口访问系统的权限</li>
          <li><strong>数据导出权限</strong>：控制用户导出数据的格式和范围</li>
        </ul>
        
        <div class="tips-block">
          <h4>权限最佳实践</h4>
          <ul>
            <li>遵循最小权限原则，只赋予用户完成工作所需的最小权限</li>
            <li>定期审查用户权限，移除不必要的权限</li>
            <li>对于敏感操作（如删除数据），设置二次确认机制</li>
            <li>使用角色而非个别权限进行管理，简化权限维护工作</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>用户管理常见问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse>
          <el-collapse-item title="如何重置用户密码？" name="1">
            <p>管理员可以通过以下步骤重置用户密码：</p>
            <ol>
              <li>在用户列表中找到需要重置密码的用户</li>
              <li>点击「操作」列中的「更多」按钮，选择「重置密码」</li>
              <li>系统会生成一个临时密码，或者您可以手动设置新密码</li>
              <li>将新密码通知给用户（通过邮件或其他安全方式）</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="如何禁用用户账号？" name="2">
            <p>禁用用户账号的步骤：</p>
            <ol>
              <li>在用户列表中找到需要禁用的用户</li>
              <li>点击「状态」列中的开关，或使用「操作」列中的「禁用」选项</li>
              <li>在确认对话框中点击「确认」</li>
            </ol>
            <p>禁用后，该用户将无法登录系统，但其账号信息和历史数据仍然保留。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何删除用户账号？" name="3">
            <p>出于数据完整性考虑，系统不建议直接删除用户账号。如果必须删除，请按照以下步骤操作：</p>
            <ol>
              <li>在用户列表中找到需要删除的用户</li>
              <li>点击「操作」列中的「删除」按钮</li>
              <li>阅读并确认删除警告</li>
              <li>输入管理员密码进行二次确认</li>
            </ol>
            <p>注意：删除用户会导致该用户相关的历史记录无法关联到具体用户。建议先禁用账号，而不是直接删除。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何查看用户操作日志？" name="4">
            <p>查看用户操作日志的步骤：</p>
            <ol>
              <li>在用户列表中找到相关用户</li>
              <li>点击「操作」列中的「日志」按钮</li>
              <li>系统会显示该用户的所有操作记录，包括登录、数据修改等行为</li>
              <li>您可以使用筛选条件查看特定时间段或特定类型的操作</li>
              <li>如需导出日志，点击右上角的「导出」按钮</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="如何设置用户的API访问权限？" name="5">
            <p>设置用户API访问权限的步骤：</p>
            <ol>
              <li>在用户列表中找到需要设置的用户</li>
              <li>点击「编辑」进入用户编辑页面</li>
              <li>切换到「API访问」选项卡</li>
              <li>启用API访问权限开关</li>
              <li>设置允许访问的API范围和权限级别</li>
              <li>设置API密钥的有效期（可选）</li>
              <li>点击「生成API密钥」按钮</li>
              <li>保存设置并将生成的密钥安全地传递给用户</li>
            </ol>
            <p>注意：API密钥具有较高的权限，应当妥善保管，避免泄露。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何批量导入用户？" name="6">
            <p>批量导入用户的步骤：</p>
            <ol>
              <li>在用户管理页面，点击「批量导入」按钮</li>
              <li>下载导入模板Excel文件</li>
              <li>按照模板格式填写用户信息</li>
              <li>上传填写好的Excel文件</li>
              <li>系统会验证数据格式，显示预览结果</li>
              <li>确认无误后，点击「确认导入」按钮</li>
              <li>系统将批量创建用户账号，并生成初始密码</li>
            </ol>
            <p>导入完成后，您可以下载包含初始密码的用户账号信息表，以便通知新用户。</p>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 角色数据
const roles = [
  {
    name: '超级管理员',
    description: '拥有系统所有权限，可以管理其他管理员账号'
  },
  {
    name: '管理员',
    description: '拥有大部分系统管理权限，但无法管理其他管理员账号'
  },
  {
    name: '数据分析师',
    description: '可以查看和分析系统数据，生成报表，但不能修改核心配置'
  },
  {
    name: '编辑员',
    description: '可以添加和编辑内容，但不能进行系统设置'
  },
  {
    name: '操作员',
    description: '可以执行日常运营操作，但不能添加或修改重要数据'
  },
  {
    name: '查看者',
    description: '只有查看权限，不能进行任何修改操作'
  }
];
</script>

<style scoped>
.user-management h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

.steps-container {
  margin: 20px 0;
}

.tips-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}
</style> 
<template>
  <div class="help-doc category-management">
    <h2>分类管理</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>分类概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>分类管理是商品管理的重要组成部分，良好的分类结构可以帮助用户更快地找到所需商品，同时也便于系统的数据统计和商品管理。</p>
        
        <div class="info-block">
          <h4>分类特点</h4>
          <ul>
            <li>支持多级分类结构（最多四级）</li>
            <li>可以设置分类图标和展示顺序</li>
            <li>支持分类的启用/禁用管理</li>
            <li>可以为分类设置自定义属性</li>
            <li>支持分类别名，提高搜索准确性</li>
            <li>支持分类关联标签，实现交叉分类</li>
            <li>支持分类多语言显示</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>分类列表</h3>
        </div>
      </template>
      <div class="card-content">
        <p>分类列表页面展示了系统中所有的商品分类，采用树形结构直观显示分类层级关系。</p>
        
        <h4>列表字段说明</h4>
        <el-table :data="categoryFields" style="width: 100%">
          <el-table-column prop="field" label="字段名称" width="180" />
          <el-table-column prop="description" label="说明" />
        </el-table>
        
        <h4>操作按钮说明</h4>
        <ul class="operation-list">
          <li>
            <el-tag size="small" type="primary">添加子分类</el-tag>
            <span class="operation-desc">在当前分类下创建一个新的子分类</span>
          </li>
          <li>
            <el-tag size="small" type="success">编辑</el-tag>
            <span class="operation-desc">修改当前分类的信息</span>
          </li>
          <li>
            <el-tag size="small" type="danger">删除</el-tag>
            <span class="operation-desc">删除当前分类（注意：有子分类或关联商品的分类不能直接删除）</span>
          </li>
          <li>
            <el-tag size="small" type="info">排序</el-tag>
            <span class="operation-desc">调整分类在同级中的显示顺序</span>
          </li>
          <li>
            <el-tag size="small" type="warning">批量管理</el-tag>
            <span class="operation-desc">对多个分类进行批量操作</span>
          </li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>添加/编辑分类</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>基本信息</h4>
        <p>添加或编辑分类时，您需要填写以下信息：</p>
        <ul>
          <li><strong>分类名称</strong>：分类的显示名称</li>
          <li><strong>上级分类</strong>：选择该分类的父级分类（创建一级分类时不需要选择）</li>
          <li><strong>分类图标</strong>：上传分类的图标图片</li>
          <li><strong>排序值</strong>：数字越小，排序越靠前</li>
          <li><strong>状态</strong>：启用或禁用</li>
          <li><strong>分类别名</strong>：设置分类的别名，提高搜索效率</li>
        </ul>
        
        <h4>高级设置</h4>
        <p>根据需要，您还可以设置以下高级选项：</p>
        <ul>
          <li><strong>SEO信息</strong>：设置分类页面的标题、关键词和描述，有助于搜索引擎优化</li>
          <li><strong>自定义属性</strong>：为分类添加特定的属性字段</li>
          <li><strong>展示模板</strong>：选择分类页面的展示模板</li>
          <li><strong>关联标签</strong>：将分类与特定标签关联，实现交叉分类</li>
          <li><strong>多语言设置</strong>：为不同语言环境设置分类名称和描述</li>
        </ul>
        
        <div class="tips-block">
          <h4>使用建议</h4>
          <ul>
            <li>分类名称应简洁明了，避免使用过长的名称</li>
            <li>为保持分类结构清晰，建议控制分类层级，一般不超过四级</li>
            <li>重要的分类可以适当调整排序值，使其显示在前面</li>
            <li>不常用的分类可以设置为禁用状态，而不是直接删除</li>
            <li>善用分类别名和关联标签，提高商品的可发现性</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>分类管理最佳实践</h3>
        </div>
      </template>
      <div class="card-content">
        <el-timeline>
          <el-timeline-item
            timestamp="第一步"
            placement="top"
            :hollow="true"
            type="primary"
          >
            <h4>规划分类结构</h4>
            <p>在添加分类前，先规划好整体的分类结构。考虑用户的浏览习惯和商品的特性，设计合理的分类层级。</p>
          </el-timeline-item>
          
          <el-timeline-item
            timestamp="第二步"
            placement="top"
            :hollow="true"
            type="primary"
          >
            <h4>创建一级分类</h4>
            <p>先创建主要的一级分类，这些分类通常代表商品的大类，如"电子产品"、"家居用品"等。</p>
          </el-timeline-item>
          
          <el-timeline-item
            timestamp="第三步"
            placement="top"
            :hollow="true"
            type="primary"
          >
            <h4>添加子分类</h4>
            <p>为一级分类添加子分类，细化商品分类。例如，在"电子产品"下添加"手机"、"电脑"、"相机"等二级分类，再在"手机"下添加"智能手机"、"功能手机"等三级分类。</p>
          </el-timeline-item>
          
          <el-timeline-item
            timestamp="第四步"
            placement="top"
            :hollow="true"
            type="primary"
          >
            <h4>调整分类顺序</h4>
            <p>根据商品重要性和用户关注度，调整分类的显示顺序，确保热门分类更容易被用户找到。</p>
          </el-timeline-item>
          
          <el-timeline-item
            timestamp="第五步"
            placement="top"
            :hollow="true"
            type="primary"
          >
            <h4>设置分类属性</h4>
            <p>为分类设置合适的图标、描述和自定义属性，提升用户体验。对于多语言站点，设置各语言环境下的分类名称和描述。</p>
          </el-timeline-item>
          
          <el-timeline-item
            timestamp="第六步"
            placement="top"
            :hollow="true"
            type="primary"
          >
            <h4>分类导入导出</h4>
            <p>大型分类体系可以使用导入导出功能批量管理。导出现有分类，在Excel中编辑后再导入，可以高效地调整大量分类。</p>
          </el-timeline-item>
          
          <el-timeline-item
            timestamp="第七步"
            placement="top"
            :hollow="true"
            type="primary"
          >
            <h4>定期维护分类</h4>
            <p>随着商品种类的变化，定期检查和更新分类结构，确保分类系统始终符合业务需求。使用系统的分类分析工具，查看各分类的访问和销售数据，优化分类结构。</p>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 分类字段说明数据
const categoryFields = [
  {
    field: '分类名称',
    description: '分类的名称，显示在前台页面'
  },
  {
    field: '分类ID',
    description: '系统自动生成的唯一标识符'
  },
  {
    field: '上级分类',
    description: '该分类的父级分类名称'
  },
  {
    field: '层级',
    description: '分类的层级，一级、二级或三级'
  },
  {
    field: '排序值',
    description: '决定分类在同级中的显示顺序，数值越小越靠前'
  },
  {
    field: '状态',
    description: '分类的启用/禁用状态'
  },
  {
    field: '关联商品数',
    description: '该分类下的商品数量'
  },
  {
    field: '创建时间',
    description: '分类的创建时间'
  }
];
</script>

<style scoped>
.category-management h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

.tips-block {
  background-color: #f2f6fc;
  border-left: 4px solid #909399;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

.operation-list {
  list-style: none;
  padding: 0;
}

.operation-list li {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.operation-desc {
  margin-left: 10px;
  color: #606266;
}
</style> 
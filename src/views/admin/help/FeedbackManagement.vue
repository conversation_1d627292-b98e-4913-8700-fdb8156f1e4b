<template>
  <div class="help-doc feedback-management">
    <h2>反馈管理</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>反馈管理概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>反馈管理模块用于收集、处理和响应用户提交的反馈、建议和问题。及时处理用户反馈不仅可以提高用户满意度，还能帮助系统持续改进。</p>
        
        <div class="info-block">
          <h4>反馈类型</h4>
          <ul>
            <li><strong>功能建议</strong>：用户对系统功能的改进建议</li>
            <li><strong>商品相关</strong>：关于商品信息、价格、库存等的反馈</li>
            <li><strong>操作问题</strong>：用户在使用系统时遇到的操作困难</li>
            <li><strong>技术故障</strong>：系统故障或错误报告</li>
            <li><strong>界面体验</strong>：关于系统界面设计和用户体验的反馈</li>
            <li><strong>系统建议</strong>：对系统整体的改进建议</li>
            <li><strong>其他反馈</strong>：不属于以上类型的反馈</li>
          </ul>
        </div>
        
        <div class="info-block">
          <h4>反馈渠道</h4>
          <ul>
            <li><strong>系统内置表单</strong>：用户通过系统内的反馈表单提交</li>
            <li><strong>电子邮件</strong>：通过指定邮箱接收用户反馈</li>
            <li><strong>客服聊天</strong>：通过在线客服收集的反馈</li>
            <li><strong>社交媒体</strong>：从社交媒体平台收集的用户反馈</li>
            <li><strong>APP反馈</strong>：通过移动应用提交的反馈</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>反馈列表</h3>
        </div>
      </template>
      <div class="card-content">
        <p>反馈列表页面展示了系统中的所有用户反馈，提供多种筛选和管理功能。</p>
        
        <div class="image-container">
          <el-image src="https://example.com/images/feedback-list.jpg" fit="contain">
            <template #error>
              <div class="image-placeholder">
                <el-icon><Picture /></el-icon>
                <span>反馈列表示例图片</span>
              </div>
            </template>
          </el-image>
          <div class="image-caption">反馈列表界面</div>
        </div>
        
        <h4>列表字段说明</h4>
        <el-table :data="feedbackFields" style="width: 100%">
          <el-table-column prop="field" label="字段名称" width="180" />
          <el-table-column prop="description" label="说明" />
        </el-table>
        
        <h4>状态说明</h4>
        <div class="status-container">
          <div class="status-item">
            <el-tag type="info">待处理</el-tag>
            <span class="status-desc">新提交的反馈，尚未开始处理</span>
          </div>
          <div class="status-item">
            <el-tag type="warning">处理中</el-tag>
            <span class="status-desc">已开始处理但尚未完成的反馈</span>
          </div>
          <div class="status-item">
            <el-tag type="success">已解决</el-tag>
            <span class="status-desc">问题已经解决，反馈处理完成</span>
          </div>
          <div class="status-item">
            <el-tag type="danger">已关闭</el-tag>
            <span class="status-desc">反馈被关闭，可能无法解决或不属于有效反馈</span>
          </div>
          <div class="status-item">
            <el-tag type="primary">待确认</el-tag>
            <span class="status-desc">已回复用户，等待用户确认问题是否解决</span>
          </div>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>处理反馈</h3>
        </div>
      </template>
      <div class="card-content">
        <p>处理用户反馈是提高用户满意度的重要环节。以下是处理反馈的推荐流程：</p>
        
        <div class="steps-container">
          <el-steps direction="vertical" :active="5">
            <el-step title="接收反馈" description="系统接收用户提交的反馈，状态为「待处理」" />
            <el-step title="审核分类" description="管理员审核反馈内容，确认其类型和优先级" />
            <el-step title="开始处理" description="指派专人处理反馈，状态更新为「处理中」" />
            <el-step title="解决问题" description="根据反馈内容解决问题或采纳建议" />
            <el-step title="回复用户" description="向用户发送处理结果，获取用户确认" />
            <el-step title="关闭反馈" description="用户确认后将状态更新为「已解决」或「已关闭」" />
          </el-steps>
        </div>
        
        <h4>回复反馈</h4>
        <p>回复用户反馈时，请注意以下几点：</p>
        <ul>
          <li>使用礼貌、专业的语言</li>
          <li>清晰解释处理结果或解决方案</li>
          <li>如果无法解决，解释原因并提供替代方案</li>
          <li>感谢用户提供反馈，鼓励继续参与系统改进</li>
        </ul>
        
        <h4>批量处理</h4>
        <p>对于相似的反馈问题，系统支持批量处理功能：</p>
        <ol>
          <li>在反馈列表中选择多个相似的反馈</li>
          <li>点击「批量处理」按钮</li>
          <li>选择共同的处理操作（如更改状态、分配处理人）</li>
          <li>如需批量回复，可以使用反馈模板</li>
          <li>确认后，系统会对所有选中的反馈执行相同操作</li>
        </ol>
        
        <h4>反馈模板</h4>
        <p>系统提供常用反馈回复模板，提高回复效率：</p>
        <ul>
          <li>在回复编辑器中，点击「模板」按钮选择预设模板</li>
          <li>根据实际情况修改模板内容</li>
          <li>您也可以将常用回复保存为新模板，方便后续使用</li>
          <li>模板支持变量插入，如姓名、日期等</li>
        </ul>
        
        <el-alert
          title="提示：系统会自动记录反馈处理的全过程，包括状态变更和回复内容"
          type="info"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>反馈统计与分析</h3>
        </div>
      </template>
      <div class="card-content">
        <p>反馈统计与分析功能可以帮助管理员了解用户反馈的趋势和重点问题，从而有针对性地改进系统。</p>
        
        <h4>统计数据</h4>
        <p>系统提供以下统计数据：</p>
        <ul>
          <li><strong>反馈总量</strong>：按时间段统计的反馈数量</li>
          <li><strong>反馈类型分布</strong>：各类型反馈的占比</li>
          <li><strong>处理时长</strong>：反馈平均处理时间</li>
          <li><strong>满意度</strong>：用户对反馈处理的满意程度</li>
          <li><strong>热点问题</strong>：被多次反馈的共性问题</li>
          <li><strong>渠道分析</strong>：不同反馈渠道的数量和质量对比</li>
          <li><strong>处理效率</strong>：各处理人员的反馈处理效率对比</li>
        </ul>
        
        <h4>数据分析</h4>
        <p>通过对反馈数据的分析，可以发现以下有价值的信息：</p>
        <ul>
          <li>系统中存在的主要问题和改进空间</li>
          <li>用户最关注的功能和特性</li>
          <li>反馈处理效率和质量</li>
          <li>不同用户群体的需求差异</li>
        </ul>
        
        <h4>趋势分析</h4>
        <p>系统提供反馈数据的趋势分析图表，帮助您了解：</p>
        <ul>
          <li>反馈数量的变化趋势</li>
          <li>问题类型的演变</li>
          <li>用户满意度的变化</li>
          <li>处理效率的提升情况</li>
        </ul>
        
        <div class="image-container">
          <el-image src="https://example.com/images/feedback-analysis.jpg" fit="contain">
            <template #error>
              <div class="image-placeholder">
                <el-icon><DataAnalysis /></el-icon>
                <span>反馈分析示例图表</span>
              </div>
            </template>
          </el-image>
          <div class="image-caption">反馈分析仪表盘</div>
        </div>
        
        <p>建议定期（如每月）生成反馈分析报告，作为系统改进的重要参考。分析报告可以导出为PDF或Excel格式，便于分享和存档。</p>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>最佳实践</h3>
        </div>
      </template>
      <div class="card-content">
        <div class="tips-block">
          <h4>反馈处理技巧</h4>
          <ul>
            <li><strong>及时响应</strong>：尽快处理新收到的反馈，即使无法立即解决，也应先回复用户</li>
            <li><strong>分类优先级</strong>：按照反馈的紧急程度和影响范围设置处理优先级</li>
            <li><strong>集中处理</strong>：对相似的反馈进行归类，集中解决共性问题</li>
            <li><strong>闭环管理</strong>：确保每个反馈都得到妥善处理，不遗漏任何用户反馈</li>
            <li><strong>持续改进</strong>：根据反馈统计分析结果，定期优化系统功能和服务</li>
          </ul>
        </div>
        
        <el-divider content-position="center">
          <small>反馈是改进的源泉</small>
        </el-divider>
        
        <blockquote class="quote">
          良好的反馈管理不仅能提高用户满意度，还能帮助团队发现系统中的问题和改进机会。重视每一条用户反馈，它们是系统持续进步的宝贵资源。
        </blockquote>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Picture, DataAnalysis } from '@element-plus/icons-vue';

// 反馈字段说明数据
const feedbackFields = [
  {
    field: 'ID',
    description: '反馈的唯一标识符'
  },
  {
    field: '类型',
    description: '反馈的分类（功能建议、商品相关、操作问题等）'
  },
  {
    field: '标题',
    description: '反馈的简短描述'
  },
  {
    field: '提交用户',
    description: '提交反馈的姓名'
  },
  {
    field: '提交时间',
    description: '反馈的提交时间'
  },
  {
    field: '状态',
    description: '反馈的处理状态（待处理、处理中、已解决、已关闭）'
  },
  {
    field: '操作',
    description: '可对反馈执行的操作（查看、回复、处理、关闭等）'
  }
];
</script>

<style scoped>
.feedback-management h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

.tips-block {
  background-color: #f2f6fc;
  border-left: 4px solid #909399;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

.image-container {
  margin: 15px 0;
  text-align: center;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #f5f7fa;
  color: #909399;
  border: 1px dashed #d9d9d9;
}

.image-placeholder .el-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.image-caption {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.steps-container {
  margin: 20px 0;
}

.status-container {
  margin: 15px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-desc {
  margin-left: 10px;
  color: #606266;
}

.quote {
  font-style: italic;
  color: #606266;
  padding: 0 16px;
  border-left: 4px solid #dcdfe6;
  margin: 16px 0;
}
</style> 
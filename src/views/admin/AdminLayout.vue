<template>
  <div class="admin-layout">
    <el-container style="height: 100vh;">
      <!-- 侧边栏 -->
      <el-aside :width="`${layoutStore.sidebarWidth}px`" class="admin-sidebar">
        <div class="logo" :class="{ 'collapsed': layoutStore.isSidebarCollapsed }">
          <h2 v-if="!layoutStore.isSidebarCollapsed">管理后台</h2>
          <el-icon v-else size="24"><Monitor /></el-icon>
        </div>
        <el-menu
          :default-active="activeMenu"
          router
          unique-opened
          :collapse="layoutStore.isSidebarCollapsed"
          background-color="#111827"
          text-color="#e5e7eb"
          active-text-color="#60a5fa"
        >
          <el-menu-item index="/admin/dashboard">
            <el-icon><DataLine /></el-icon>
            <template #title>
              <span>数据仪表盘</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/products">
            <el-icon><Goods /></el-icon>
            <template #title>
              <span>商品管理</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/exchanges">
            <el-icon><Document /></el-icon>
            <template #title>
              <span>订单管理</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/categories">
            <el-icon><Menu /></el-icon>
            <template #title>
              <span>分类管理</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/announcements">
            <el-icon><Bell /></el-icon>
            <template #title>
              <span>公告管理</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/users">
            <el-icon><User /></el-icon>
            <template #title>
              <span>用户管理</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/feedbacks">
            <el-icon><ChatLineRound /></el-icon>
            <template #title>
              <span>反馈管理</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/notifications">
            <el-icon><MessageBox /></el-icon>
            <template #title>
              <span>通知中心</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/logs">
            <el-icon><Document /></el-icon>
            <template #title>
              <span>日志管理</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/help">
            <el-icon><QuestionFilled /></el-icon>
            <template #title>
              <span>帮助中心</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/admin/system">
            <el-icon><Setting /></el-icon>
            <template #title>
              <span>系统设置</span>
            </template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 侧边栏拖拽分隔线 -->
      <div 
        class="sidebar-resizer" 
        @mousedown="startResize" 
        @dblclick="handleResizerDoubleClick"
        :class="{ 'active': isResizing, 'near-threshold': isNearThreshold }"
      >
        <div class="resizer-indicator">
          <el-icon><DArrowLeft v-if="layoutStore.isSidebarCollapsed" /><DArrowRight v-else /></el-icon>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <el-container>
        <el-header class="admin-header">
          <div class="left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="right">
            <div class="header-actions">
              <el-tooltip content="帮助文档" placement="bottom">
                <el-button type="text" class="action-btn" @click="goToHelpCenter">
                  <el-icon><QuestionFilled /></el-icon>
                </el-button>
              </el-tooltip>
              
              <NotificationIcon />
            </div>
            
            <el-dropdown trigger="click" @command="handleCommand">
              <div class="user-info">
                <el-avatar :size="36" :src="avatarUrl" />
                <div class="user-details">
                  <span class="username">{{ username }}</span>
                  <small class="user-role">管理员</small>
                </div>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><UserFilled /></el-icon>
                    <span>个人中心</span>
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    <span>系统设置</span>
                  </el-dropdown-item>
                  <el-dropdown-item command="home">
                    <el-icon><House /></el-icon>
                    <span>返回前台</span>
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    <span>退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <el-main class="admin-main">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watchEffect } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import { useLayoutStore } from '../../stores/layout';
import { ElMessage } from 'element-plus';
import NotificationIcon from '../../components/admin/NotificationIcon.vue';
import {
  Goods, Expand, Fold, ArrowDown, Menu, Bell, User, ChatLineRound,
  QuestionFilled, UserFilled, Setting, House, SwitchButton, Monitor, 
  DataLine, Document, MessageBox, DArrowLeft, DArrowRight
} from '@element-plus/icons-vue';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const layoutStore = useLayoutStore();

// 用户信息
const username = computed(() => authStore.user?.username || '管理员');
const avatarUrl = computed(() => authStore.user?.feishuAvatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png');

// 计算当前激活的菜单项
const activeMenu = computed(() => route.path);

// 计算当前页面标题
const pageTitle = computed(() => route.meta.title || '管理后台');

// 拖拽相关状态
const isResizing = ref(false);
const startX = ref(0);
const startWidth = ref(0);
const lastUpdateTime = ref(0);
const isNearThreshold = ref(false);

// 检测是否接近阈值
watchEffect(() => {
  const threshold = layoutStore.COLLAPSE_THRESHOLD;
  const currentWidth = layoutStore.sidebarWidth;
  // 如果宽度在阈值附近20px范围内
  isNearThreshold.value = Math.abs(currentWidth - threshold) < 20;
});

// 节流函数
const throttle = (fn, delay) => {
  let lastCall = 0;
  return (...args) => {
    const now = Date.now();
    if (now - lastCall < delay) return;
    lastCall = now;
    return fn(...args);
  };
};

// 处理分隔线双击
const handleResizerDoubleClick = () => {
  layoutStore.toggleSidebar();
};

// 开始拖拽操作
const startResize = (e) => {
  isResizing.value = true;
  startX.value = e.clientX;
  startWidth.value = layoutStore.sidebarWidth;
  
  // 添加全局事件监听
  document.addEventListener('mousemove', handleMousemoveThrottled);
  document.addEventListener('mouseup', stopResize);
  
  // 添加拖拽时的样式
  document.body.style.cursor = 'col-resize';
  document.body.style.userSelect = 'none';
  
  // 添加拖拽辅助线
  const resizeGuide = document.createElement('div');
  resizeGuide.id = 'resize-guide';
  resizeGuide.style.position = 'fixed';
  resizeGuide.style.top = '0';
  resizeGuide.style.left = `${layoutStore.sidebarWidth}px`;
  resizeGuide.style.width = '1px';
  resizeGuide.style.height = '100%';
  resizeGuide.style.backgroundColor = '#409eff';
  resizeGuide.style.opacity = '0.5';
  resizeGuide.style.zIndex = '9999';
  document.body.appendChild(resizeGuide);
};

// 处理鼠标移动 - 使用requestAnimationFrame优化
const handleMousemove = (e) => {
  if (!isResizing.value) return;
  
  // 更新辅助线位置
  const resizeGuide = document.getElementById('resize-guide');
  if (resizeGuide) {
    // 计算新宽度
    const deltaX = e.clientX - startX.value;
    const newWidth = startWidth.value + deltaX;
    resizeGuide.style.left = `${newWidth}px`;
  }
  
  // 使用requestAnimationFrame进行宽度更新
  requestAnimationFrame(() => {
    // 计算新宽度
    const deltaX = e.clientX - startX.value;
    const newWidth = startWidth.value + deltaX;
    
    // 更新侧边栏宽度
    layoutStore.setSidebarWidth(newWidth);
  });
};

// 使用节流函数包装handleMousemove，限制调用频率
const handleMousemoveThrottled = throttle(handleMousemove, 16); // 约60fps

// 停止拖拽操作
const stopResize = () => {
  isResizing.value = false;
  
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMousemoveThrottled);
  document.removeEventListener('mouseup', stopResize);
  
  // 恢复默认样式
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
  
  // 移除辅助线
  const resizeGuide = document.getElementById('resize-guide');
  if (resizeGuide) {
    document.body.removeChild(resizeGuide);
  }
};

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中...');
      break;
    case 'settings':
      router.push('/admin/system');
      break;
    case 'home':
      router.push('/');
      break;
    case 'logout':
      authStore.logout();
      ElMessage.success('已退出登录');
      router.push('/login');
      break;
  }
};

// 跳转到帮助中心
const goToHelpCenter = () => {
  router.push('/admin/help');
};

// 添加键盘快捷键
const handleKeydown = (e) => {
  // Ctrl+B 切换侧边栏
  if (e.ctrlKey && e.key === 'b') {
    e.preventDefault();
    layoutStore.toggleSidebar();
  }
};

// 组件挂载时添加窗口大小监听
onMounted(() => {
  window.addEventListener('resize', layoutStore.handleScreenResize);
  window.addEventListener('keydown', handleKeydown);
  // 初始检查屏幕大小
  layoutStore.handleScreenResize();
});

// 在组件卸载前移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', layoutStore.handleScreenResize);
  window.removeEventListener('keydown', handleKeydown);
  // 确保拖拽相关事件监听器也被移除
  document.removeEventListener('mousemove', handleMousemoveThrottled);
  document.removeEventListener('mouseup', stopResize);
  // 移除可能存在的辅助线
  const resizeGuide = document.getElementById('resize-guide');
  if (resizeGuide) {
    document.body.removeChild(resizeGuide);
  }
});
</script>

<style scoped>
.admin-layout {
  height: 100%;
  position: relative;
}

.admin-sidebar {
  background-color: #111827;
  overflow-x: hidden;
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

/* 拖拽分隔线 */
.sidebar-resizer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 6px;
  background-color: transparent;
  cursor: col-resize;
  z-index: 100;
  /* 将分隔线定位到侧边栏的右侧边缘 */
  transform: translateX(calc(v-bind('layoutStore.sidebarWidth') * 1px));
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-resizer:hover,
.sidebar-resizer.active {
  background-color: rgba(64, 158, 255, 0.5);
}

/* 拖拽指示器 */
.resizer-indicator {
  opacity: 0;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.sidebar-resizer:hover .resizer-indicator {
  opacity: 0.8;
}

.sidebar-resizer.active .resizer-indicator {
  opacity: 1;
}

/* 接近阈值时的样式 */
.sidebar-resizer.near-threshold {
  background-color: rgba(230, 162, 60, 0.3);
}

.sidebar-resizer.near-threshold:hover,
.sidebar-resizer.near-threshold.active {
  background-color: rgba(230, 162, 60, 0.5);
}

.logo {
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 18px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s;
  background-color: #0f172a;
}

.logo.collapsed {
  padding: 0;
}

.logo h2 {
  font-weight: 500;
  letter-spacing: 1px;
  margin: 0;
}

.admin-header {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 64px;
}

.admin-header .left,
.admin-header .right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f1f5f9;
}

.user-details {
  margin: 0 12px;
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.user-role {
  font-size: 12px;
  color: #6b7280;
}

.admin-main {
  padding: 24px;
  background-color: #f9fafb;
  overflow-y: auto;
}

.header-actions {
  display: flex;
  align-items: center;
  margin-right: 24px;
}

.action-btn {
  font-size: 18px;
  padding: 8px;
  color: #64748b;
}

.action-badge {
  margin: 0 12px;
}

/* Element UI 组件样式覆盖 */
:deep(.el-menu) {
  border-right: none;
  flex: 1;
  overflow-y: auto;
}

:deep(.el-menu--collapse) {
  width: 64px;
}

:deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
}

:deep(.el-menu-item.is-active) {
  background-color: #1e293b !important;
}

:deep(.el-menu-item:hover) {
  background-color: #1e293b !important;
}

:deep(.el-menu-item .el-icon) {
  margin-right: 16px;
  font-size: 18px;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 10px 20px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 10px;
  font-size: 16px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
  .admin-header {
    padding: 0 12px;
  }
  
  .header-actions {
    margin-right: 12px;
  }
  
  .admin-main {
    padding: 16px;
  }
  
  /* 小屏幕上隐藏拖拽分隔线 */
  .sidebar-resizer {
    display: none;
  }
}
</style> 
<template>
  <div class="exchange-management">
    <div class="page-header">
      <h2 class="page-title">订单管理</h2>
      <div class="header-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索姓名、部门或邮箱"
          clearable
          @clear="fetchExchanges"
          style="width: 220px"
        >
          <template #append>
            <el-button @click="fetchExchanges">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
        
        <el-dropdown @command="handleExportCommand" split-button type="primary">
          导出
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="exportExcel">导出为Excel</el-dropdown-item>
              <el-dropdown-item command="exportCsv">导出为CSV</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <el-button type="success" @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入
        </el-button>
        
        <el-button type="primary" @click="fetchExchanges">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>

        <el-button 
          type="danger" 
          @click="batchDelete"
          :disabled="selectedExchanges.length === 0"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        
        <el-tooltip content="删除所有订单后重置序号，新订单将从001开始编号" placement="top">
          <el-button 
            type="warning" 
            @click="confirmResetAutoIncrement"
          >
            <el-icon><RefreshRight /></el-icon>
            重置序号
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 标签页选择不同状态的兑换记录 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane label="待处理" name="pending"></el-tab-pane>
      <el-tab-pane label="已批准" name="approved"></el-tab-pane>
      <el-tab-pane label="已发货" name="shipped"></el-tab-pane>
      <el-tab-pane label="已完成" name="completed"></el-tab-pane>
      <el-tab-pane label="已拒绝" name="rejected"></el-tab-pane>
      <el-tab-pane label="已取消" name="cancelled"></el-tab-pane>
    </el-tabs>

    <!-- 兑换记录表格 -->
    <div v-if="loading" class="loading-wrapper">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="exchanges.length === 0" class="empty-state">
      <el-empty description="暂无兑换记录" />
    </div>
    
    <el-table
      v-else
      :data="exchanges"
      style="width: 100%"
      border
      :header-cell-style="{ background: '#f5f7fa' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column label="订单编号" width="180">
        <template #default="{ row }">
          {{ formatOrderNumber(row) }}
        </template>
      </el-table-column>
      
      <el-table-column label="用户信息" width="180">
        <template #default="{ row }">
          <div v-if="row.User">
            <div><b>姓名:</b> {{ row.User.username }}</div>
            <div><b>部门:</b> {{ row.User.department || '未设置' }}</div>
            <div><b>邮箱:</b> {{ row.User.email || '未设置' }}</div>
          </div>
          <span v-else-if="row.userId" class="user-placeholder" @click="loadUserInfo(row)">
            <el-tooltip content="点击加载用户信息" placement="top">
              <span>用户ID: {{ row.userId }}</span>
            </el-tooltip>
          </span>
          <span v-else>未知用户</span>
        </template>
      </el-table-column>
      
      <el-table-column label="商品信息" min-width="200">
        <template #default="{ row }">
          <div v-if="row.Product" class="product-info">
            <div class="product-details">
              <div><b>商品名:</b> {{ row.Product.name }}</div>
              <div><b>数量:</b> {{ row.quantity }}</div>
              <div>
                <b>价格:</b> 
                <span v-if="row.paymentMethod === 'ly'">
                  {{ row.Product.lyPrice }} 光年币
                </span>
                <span v-else>
                  ¥{{ row.Product.rmbPrice }}
                </span>
              </div>
            </div>
          </div>
          <span v-else>未知商品</span>
        </template>
      </el-table-column>
      
      <el-table-column label="支付方式" width="100" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.paymentMethod === 'ly'" type="primary" effect="light">
            光年币
          </el-tag>
          <el-tag v-else type="success" effect="light">
            人民币
            <el-tooltip v-if="row.paymentProofUrl" content="已上传支付凭证" placement="top">
              <el-icon style="margin-left: 2px;"><Check /></el-icon>
            </el-tooltip>
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="总收入" width="100" align="center">
        <template #default="{ row }">
          <span v-if="row.paymentMethod === 'ly'">
            {{ row.totalAmount || calculateTotal(row, 'ly') }} 个光年币
          </span>
          <span v-else>
            ¥{{ row.totalAmount || calculateTotal(row, 'rmb') }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column label="收货信息" width="180">
        <template #default="{ row }">
          <div><b>联系方式:</b> {{ row.contactInfo }}</div>
          <div><b>位置:</b> {{ row.location }}</div>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="订单时间" width="180">
        <template #default="{ row }">
          <div>{{ formatDate(row.createdAt) }}</div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small"
            @click="showExchangeDetail(row)"
          >
            详情
          </el-button>
          
          <el-button 
            v-if="row.status === 'pending'"
            type="success" 
            size="small"
            @click="handleUpdateStatus(row, 'approved')"
          >
            批准
          </el-button>
          
          <el-button 
            v-if="row.status === 'approved'"
            type="warning" 
            size="small"
            @click="showShippingDialog(row)"
          >
            发货
          </el-button>

          <el-button 
            v-if="row.status === 'shipped'"
            type="success" 
            size="small"
            @click="handleUpdateStatus(row, 'completed')"
          >
            完成
          </el-button>
          
          <el-button 
            v-if="row.status === 'pending'"
            type="danger" 
            size="small"
            @click="handleUpdateStatus(row, 'rejected')"
          >
            拒绝
          </el-button>

          <el-button 
            v-if="['pending', 'approved', 'shipped'].includes(row.status)"
            type="info" 
            size="small"
            @click="handleUpdateStatus(row, 'cancelled')"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 兑换详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="兑换详情"
      width="700px"
    >
      <div v-if="selectedExchange" class="exchange-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单编号">{{ formatOrderNumber(selectedExchange) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedExchange.createdAt) }}</el-descriptions-item>
          
          <el-descriptions-item label="姓名" :span="1">
            {{ selectedExchange.User?.username || `用户ID: ${selectedExchange.userId}` }}
          </el-descriptions-item>
          <el-descriptions-item label="部门" :span="1">
            {{ selectedExchange.User?.department || '未设置' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="邮箱" :span="2">
            {{ selectedExchange.User?.email || '未设置' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="商品名称" :span="1">
            {{ selectedExchange.Product?.name || '未知商品' }}
          </el-descriptions-item>
          <el-descriptions-item label="数量" :span="1">
            {{ selectedExchange.quantity }}
          </el-descriptions-item>
          
          <el-descriptions-item label="支付方式" :span="1">
            <el-tag v-if="selectedExchange.paymentMethod === 'ly'" type="primary" effect="light">
              光年币 ({{ selectedExchange.Product?.lyPrice || 0 }})
            </el-tag>
            <el-tag v-else type="success" effect="light">
              人民币
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态" :span="1">
            <el-tag :type="getStatusType(selectedExchange.status)">
              {{ getStatusText(selectedExchange.status) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="总收入" :span="2">
            <span v-if="selectedExchange.paymentMethod === 'ly'">
              {{ selectedExchange.totalAmount || calculateTotal(selectedExchange, 'ly') }} 个光年币
            </span>
            <span v-else>
              ¥{{ selectedExchange.totalAmount || calculateTotal(selectedExchange, 'rmb') }}
            </span>
          </el-descriptions-item>
          
          <el-descriptions-item label="联系方式" :span="1">
            {{ selectedExchange.contactInfo }}
            <el-button 
              type="primary" 
              link 
              size="small" 
              @click="showContactInfoDialog(selectedExchange)"
              style="margin-left: 10px"
            >
              修改
            </el-button>
          </el-descriptions-item>
          <el-descriptions-item label="位置" :span="1">
            {{ selectedExchange.location }}
          </el-descriptions-item>
          
          <el-descriptions-item label="用户备注" :span="2">
            <div style="white-space: pre-wrap;">{{ selectedExchange.remarks || '无' }}</div>
          </el-descriptions-item>
          
          <el-descriptions-item label="管理员备注" :span="2">
            <div style="white-space: pre-wrap;">{{ selectedExchange.adminRemarks || '无' }}</div>
          </el-descriptions-item>
          
          <!-- 显示支付凭证图片（仅限人民币支付） -->
          <el-descriptions-item v-if="selectedExchange.paymentMethod === 'rmb' && selectedExchange.paymentProofUrl" label="支付凭证" :span="2">
            <div class="payment-proof">
              <el-image 
                :src="selectedExchange.paymentProofUrl" 
                :preview-src-list="[selectedExchange.paymentProofUrl]"
                :preview-teleported="true"
                fit="contain"
                style="width: 150px; height: 150px;"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <div>图片加载失败</div>
                  </div>
                </template>
              </el-image>
            </div>
          </el-descriptions-item>
          
          <el-descriptions-item v-if="selectedExchange.trackingNumber" label="物流单号" :span="1">
            {{ selectedExchange.trackingNumber }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedExchange.trackingCompany" label="物流公司" :span="1">
            {{ selectedExchange.trackingCompany }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-actions">
          <el-input
            v-model="adminRemarks"
            type="textarea"
            :rows="3"
            placeholder="添加管理员备注"
          />
          <div class="action-buttons">
            <el-button 
              v-if="selectedExchange.status === 'pending'"
              type="success" 
              @click="handleUpdateStatus(selectedExchange, 'approved', true)"
            >
              批准
            </el-button>
            
            <el-button 
              v-if="selectedExchange.status === 'approved'"
              type="warning" 
              @click="showShippingDialog(selectedExchange, true)"
            >
              发货
            </el-button>
            
            <el-button 
              v-if="selectedExchange.status === 'shipped'"
              type="success" 
              @click="handleUpdateStatus(selectedExchange, 'completed', true)"
            >
              完成
            </el-button>
            
            <el-button 
              v-if="selectedExchange.status === 'pending'"
              type="danger" 
              @click="handleUpdateStatus(selectedExchange, 'rejected', true)"
            >
              拒绝
            </el-button>
            
            <el-button 
              v-if="['pending', 'approved', 'shipped'].includes(selectedExchange.status)"
              type="info"
              @click="handleUpdateStatus(selectedExchange, 'cancelled', true)"
            >
              取消
            </el-button>
            
            <el-button @click="updateAdminRemarks" type="primary">
              更新备注
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 发货对话框 -->
    <el-dialog
      v-model="shippingDialogVisible"
      title="订单发货"
      width="500px"
    >
      <el-form
        ref="shippingFormRef"
        :model="shippingForm"
        :rules="shippingRules"
        label-position="top"
      >
        <el-form-item label="物流公司" prop="trackingCompany">
          <el-input v-model="shippingForm.trackingCompany" placeholder="请输入物流公司名称" />
        </el-form-item>
        
        <el-form-item label="物流单号" prop="trackingNumber">
          <el-input v-model="shippingForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        
        <el-form-item label="管理员备注" prop="adminRemarks">
          <el-input
            v-model="shippingForm.adminRemarks"
            type="textarea"
            :rows="3"
            placeholder="可选填，添加管理员备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="shippingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitShipping" :loading="submitting">
            确认发货
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 联系方式修改对话框 -->
    <el-dialog
      v-model="contactInfoDialogVisible"
      title="修改联系方式"
      width="500px"
    >
      <el-form
        ref="contactInfoFormRef"
        :model="contactInfoForm"
        :rules="contactInfoRules"
        label-position="top"
      >
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="contactInfoForm.contactInfo" placeholder="请输入联系方式" />
        </el-form-item>
        
        <el-form-item label="位置" prop="location">
          <el-select v-model="contactInfoForm.location" placeholder="请选择职场位置" style="width: 100%">
            <el-option label="北京" value="北京" />
            <el-option label="武汉" value="武汉" />
            <el-option label="长沙" value="长沙" />
            <el-option label="西安" value="西安" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="管理员备注" prop="adminRemarks">
          <el-input
            v-model="contactInfoForm.adminRemarks"
            type="textarea"
            :rows="3"
            placeholder="可选填，添加管理员备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="contactInfoDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitContactInfo" :loading="submitting">
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 导入订单对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入订单数据"
      width="550px"
    >
      <div class="import-dialog-content">
        <div class="import-tips">
          <p>请上传订单数据文件（支持CSV或Excel格式）</p>
          <p>
            <el-link type="primary" @click="downloadTemplate('csv')">下载CSV模板</el-link>
            <el-divider direction="vertical" />
            <el-link type="primary" @click="downloadTemplate('excel')">下载Excel模板</el-link>
          </p>
        </div>
        
        <el-upload
          ref="uploadRef"
          action="#"
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          :on-remove="handleRemove"
          accept=".csv,.xlsx,.xls"
        >
          <el-button type="primary">
            <el-icon><Upload /></el-icon>
            选择文件
          </el-button>
          <template #tip>
            <div class="el-upload__tip">
              仅支持.csv, .xlsx, .xls格式文件
            </div>
          </template>
        </el-upload>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importExchangeData" :loading="importing" :disabled="!importFile">
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="批量删除订单"
      width="500px"
    >
      <div>
        <p>您确定要删除选中的 {{ selectedExchanges.length }} 个订单吗？</p>
        <p class="delete-warning">此操作不可逆，删除后将无法恢复这些订单数据。</p>
        <ul class="delete-status-info">
          <li v-if="pendingCount > 0">待处理订单: {{ pendingCount }} 个</li>
          <li v-if="approvedCount > 0">已批准订单: {{ approvedCount }} 个</li>
          <li v-if="shippedCount > 0">已发货订单: {{ shippedCount }} 个</li>
          <li v-if="completedCount > 0">已完成订单: {{ completedCount }} 个</li>
          <li v-if="rejectedCount > 0">已拒绝订单: {{ rejectedCount }} 个</li>
          <li v-if="cancelledCount > 0">已取消订单: {{ cancelledCount }} 个</li>
        </ul>
        <p v-if="hasActiveOrders" class="delete-note">
          注意：删除未完成的订单将自动恢复相应商品的库存。
        </p>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button 
            type="danger" 
            @click="confirmBatchDelete" 
            :loading="deleting"
          >
            确认删除
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, Picture, Check, Upload, Delete, RefreshRight } from '@element-plus/icons-vue';
import { 
  getExchangeList, 
  getExchangeDetail, 
  updateExchangeStatus, 
  adminUpdateContactInfo,
  exportExchanges,
  importExchanges,
  downloadExchangeTemplate,
  batchDeleteExchanges,
  resetAutoIncrement
} from '../../api/exchanges';
import { formatOrderNumber } from '../../utils/format';

// 页面状态
const loading = ref(false);
const submitting = ref(false);
const exchanges = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref('');
const activeTab = ref('all');
// 添加自动刷新定时器
let refreshTimer = null;

// 批量操作相关状态
const selectedExchanges = ref([]);
const deleteDialogVisible = ref(false);
const deleting = ref(false);

// 对话框状态
const detailDialogVisible = ref(false);
const shippingDialogVisible = ref(false);
const contactInfoDialogVisible = ref(false);
const selectedExchange = ref(null);
const adminRemarks = ref('');

// 导入导出相关状态
const importDialogVisible = ref(false);
const importing = ref(false);
const uploadRef = ref(null);
const importFile = ref(null);

// 批量删除状态统计
const pendingCount = computed(() => 
  selectedExchanges.value.filter(e => e.status === 'pending').length
);
const approvedCount = computed(() => 
  selectedExchanges.value.filter(e => e.status === 'approved').length
);
const shippedCount = computed(() => 
  selectedExchanges.value.filter(e => e.status === 'shipped').length
);
const completedCount = computed(() => 
  selectedExchanges.value.filter(e => e.status === 'completed').length
);
const rejectedCount = computed(() => 
  selectedExchanges.value.filter(e => e.status === 'rejected').length
);
const cancelledCount = computed(() => 
  selectedExchanges.value.filter(e => e.status === 'cancelled').length
);
const hasActiveOrders = computed(() => 
  pendingCount.value > 0 || approvedCount.value > 0 || shippedCount.value > 0
);

// 发货表单
const shippingFormRef = ref(null);
const shippingForm = reactive({
  trackingCompany: '',
  trackingNumber: '',
  adminRemarks: ''
});

// 发货表单验证规则
const shippingRules = {
  trackingCompany: [
    { required: false, message: '请输入物流公司', trigger: 'blur' }
  ],
  trackingNumber: [
    { required: false, message: '请输入物流单号', trigger: 'blur' }
  ]
};

// 联系方式表单
const contactInfoFormRef = ref(null);
const contactInfoForm = reactive({
  contactInfo: '',
  location: '',
  adminRemarks: ''
});

// 联系方式表单验证规则
const contactInfoRules = {
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { min: 6, max: 30, message: '长度在6到30个字符之间', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请选择职场位置', trigger: 'change' }
  ]
};

// 初始化页面
onMounted(() => {
  fetchExchanges();
  // 设置自动刷新定时器，每60秒刷新一次数据
  refreshTimer = setInterval(() => {
    fetchExchanges(false);
  }, 60000);
});

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
});

// 获取兑换记录列表
const fetchExchanges = async (showLoading = true) => {
  if (showLoading) {
    loading.value = true;
  }
  
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      status: activeTab.value === 'all' ? '' : activeTab.value,
      search: searchQuery.value
    };
    
    const response = await getExchangeList(params);
    exchanges.value = response.data || [];
    total.value = response.total || 0;
    
    // 输出原始订单数据，用于调试
    console.log('原始订单数据:', JSON.stringify(response.data, null, 2));
    if (response.data && response.data.length > 0) {
      console.log('第一个订单的orderNumber:', response.data[0].orderNumber);
      console.log('第一个订单的ID:', response.data[0].id);
    }
    
    // 清空选中状态
    selectedExchanges.value = [];
  } catch (error) {
    console.error('获取兑换记录列表失败:', error);
    if (showLoading) {
      ElMessage.error('获取兑换记录列表失败，请稍后重试');
    }
  } finally {
    if (showLoading) {
      loading.value = false;
    }
  }
};

// 处理多选变化
const handleSelectionChange = (selection) => {
  selectedExchanges.value = selection;
};

// 处理批量删除
const batchDelete = () => {
  if (selectedExchanges.value.length === 0) {
    ElMessage.warning('请先选择要删除的订单');
    return;
  }
  
  deleteDialogVisible.value = true;
};

// 确认批量删除
const confirmBatchDelete = async () => {
  if (selectedExchanges.value.length === 0) {
    ElMessage.warning('请先选择要删除的订单');
    return;
  }
  
  deleting.value = true;
  
  try {
    const ids = selectedExchanges.value.map(e => e.id);
    const result = await batchDeleteExchanges(ids);
    
    ElMessage.success(result.message || `成功删除${result.deleted}个订单`);
    deleteDialogVisible.value = false;
    
    // 检查是否自动重置了自增ID
    if (result.autoIncrementReset) {
      ElMessage.success('检测到所有订单已删除，订单编号已自动重置为001');
    }
    
    // 刷新列表
    fetchExchanges();
  } catch (error) {
    console.error('批量删除订单失败:', error);
    
    let errorMsg = '批量删除订单失败，请稍后重试';
    if (error.response && error.response.data && error.response.data.message) {
      errorMsg = error.response.data.message;
    }
    
    ElMessage.error(errorMsg);
  } finally {
    deleting.value = false;
  }
};

// 处理分页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchExchanges();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchExchanges();
};

// 处理标签页切换
const handleTabChange = () => {
  currentPage.value = 1;
  fetchExchanges();
};

// 获取状态显示文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'approved': '已批准',
    'shipped': '已发货',
    'completed': '已完成',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 获取状态对应的标签类型
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'approved': 'success',
    'shipped': 'warning',
    'completed': 'success',
    'rejected': 'danger',
    'cancelled': 'info'
  };
  return typeMap[status] || 'info';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 计算总收入（作为后备计算方法，当数据库中没有totalAmount时使用）
const calculateTotal = (exchange, type) => {
  // 如果有totalAmount字段，优先使用
  if (exchange.totalAmount && parseFloat(exchange.totalAmount) > 0) {
    return type === 'ly' ? parseInt(exchange.totalAmount) : parseFloat(exchange.totalAmount).toFixed(2);
  }
  
  // 统一获取 product 对象 - 适配不同API返回格式
  const product = exchange.Product || exchange.product;
  
  if (!exchange || !product) {
    console.warn('无法计算总收入: 订单或商品数据不完整', exchange);
    return 0;
  }
  
  try {
    const quantity = parseInt(exchange.quantity) || 1;
    
    if (type === 'ly') {
      const price = parseInt(product.lyPrice) || 0;
      return quantity * price;
    } else {
      const price = parseFloat(product.rmbPrice) || 0;
      return (quantity * price).toFixed(2);
    }
  } catch (error) {
    console.error('计算总收入时出错:', error, exchange);
    return 0;
  }
};

// 加载用户信息
const loadUserInfo = async (exchange) => {
  try {
    submitting.value = true;
    // 获取详细信息
    const detail = await getExchangeDetail(exchange.id);
    
    // 如果获取到用户信息，更新当前行
    if (detail && detail.User) {
      // 找到当前行并更新用户信息
      const index = exchanges.value.findIndex(e => e.id === exchange.id);
      if (index !== -1) {
        exchanges.value[index].User = detail.User;
        ElMessage.success(`用户信息加载成功: ${detail.User.username}`);
      }
    } else {
      ElMessage.warning('无法获取用户信息');
    }
  } catch (error) {
    console.error('加载用户信息失败:', error);
    ElMessage.error('加载用户信息失败');
  } finally {
    submitting.value = false;
  }
};

// 显示兑换详情
const showExchangeDetail = async (exchange) => {
  try {
    // 获取最新状态的详细信息
    const detail = await getExchangeDetail(exchange.id);
    selectedExchange.value = detail;
    adminRemarks.value = detail.adminRemarks || '';
    detailDialogVisible.value = true;
  } catch (error) {
    console.error('获取兑换详情失败:', error);
    ElMessage.error('获取兑换详情失败，请稍后重试');
  }
};

// 显示发货对话框
const showShippingDialog = async (exchange, fromDetail = false) => {
  try {
    // 在打开对话框前重新获取最新状态
    const latestExchange = await getExchangeDetail(exchange.id);
    
    // 检查订单状态是否允许发货
    if (latestExchange.status !== 'approved') {
      ElMessage.warning(`该订单当前状态为"${getStatusText(latestExchange.status)}"，不能执行发货操作`);
      return;
    }
    
    selectedExchange.value = latestExchange;
    
    shippingForm.trackingCompany = latestExchange.trackingCompany || '';
    shippingForm.trackingNumber = latestExchange.trackingNumber || '';
    shippingForm.adminRemarks = latestExchange.adminRemarks || '';
    
    if (fromDetail) {
      detailDialogVisible.value = false;
    }
    
    shippingDialogVisible.value = true;
  } catch (error) {
    console.error('获取最新订单状态失败:', error);
    ElMessage.error('获取最新订单状态失败，请稍后重试');
  }
};

// 提交发货信息
const submitShipping = async () => {
  if (!shippingFormRef.value) return;
  
  await shippingFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    submitting.value = true;
    
    try {
      await updateExchangeStatus(selectedExchange.value.id, {
        status: 'shipped',
        trackingCompany: shippingForm.trackingCompany,
        trackingNumber: shippingForm.trackingNumber,
        adminRemarks: shippingForm.adminRemarks
      });
      
      ElMessage.success('订单已发货');
      shippingDialogVisible.value = false;
      fetchExchanges();
    } catch (error) {
      console.error('更新发货信息失败:', error);
      ElMessage.error('更新发货信息失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

// 更新订单状态
const handleUpdateStatus = async (exchange, status, fromDetail = false) => {
  try {
    // 先获取最新状态
    const latestExchange = await getExchangeDetail(exchange.id);
    
    // 检查当前状态是否允许进行操作
    if (!canChangeStatus(latestExchange.status, status)) {
      ElMessage.warning(`该订单当前状态为"${getStatusText(latestExchange.status)}"，不能更改为"${getStatusText(status)}"`);
      return;
    }
    
    // 确认提示
    const statusTextMap = {
      'approved': '批准',
      'shipped': '发货',
      'completed': '完成',
      'rejected': '拒绝',
      'cancelled': '取消'
    };
    
    await ElMessageBox.confirm(
      `确定要将该订单状态更改为"${getStatusText(status)}"吗？${status === 'rejected' || status === 'cancelled' ? '\n\n注意: 这将自动恢复商品库存并减少相应的兑换数量统计。' : ''}`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: status === 'rejected' || status === 'cancelled' ? 'warning' : 'info',
        dangerouslyUseHTMLString: false
      }
    );
    
    submitting.value = true;
    
    const updateData = {
      status,
      adminRemarks: fromDetail ? adminRemarks.value : undefined
    };
    
    await updateExchangeStatus(exchange.id, updateData);
    
    ElMessage.success(`订单已${statusTextMap[status]}`);
    
    if (fromDetail) {
      detailDialogVisible.value = false;
    }
    
    // 更新列表
    fetchExchanges();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新订单状态失败:', error);
      ElMessage.error('更新订单状态失败，请稍后重试');
    }
  } finally {
    submitting.value = false;
  }
};

// 检查状态变更是否合法
const canChangeStatus = (currentStatus, targetStatus) => {
  // 定义状态转换规则
  const statusTransitions = {
    'pending': ['approved', 'rejected', 'cancelled'],
    'approved': ['shipped', 'cancelled'],
    'shipped': ['completed', 'cancelled'],
    'completed': [], // 完成状态不能再变更
    'rejected': [], // 拒绝状态不能再变更
    'cancelled': [] // 取消状态不能再变更
  };
  
  return statusTransitions[currentStatus]?.includes(targetStatus) || false;
};

// 更新管理员备注
const updateAdminRemarks = async () => {
  try {
    submitting.value = true;
    
    await updateExchangeStatus(selectedExchange.value.id, {
      status: selectedExchange.value.status, // 保持原状态不变
      adminRemarks: adminRemarks.value
    });
    
    ElMessage.success('管理员备注已更新');
    detailDialogVisible.value = false;
    fetchExchanges();
  } catch (error) {
    console.error('更新管理员备注失败:', error);
    ElMessage.error('更新管理员备注失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
};

// 显示联系方式对话框
const showContactInfoDialog = (exchange) => {
  selectedExchange.value = exchange;
  contactInfoForm.contactInfo = exchange.contactInfo || '';
  contactInfoForm.location = exchange.location || '';
  contactInfoForm.adminRemarks = exchange.adminRemarks || '';
  contactInfoDialogVisible.value = true;
};

// 提交联系方式修改
const submitContactInfo = async () => {
  if (!contactInfoFormRef.value) return;
  
  await contactInfoFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    submitting.value = true;
    
    try {
      await adminUpdateContactInfo(selectedExchange.value.id, {
        contactInfo: contactInfoForm.contactInfo,
        location: contactInfoForm.location,
        adminRemarks: contactInfoForm.adminRemarks
      });
      
      ElMessage.success('联系方式修改成功');
      contactInfoDialogVisible.value = false;
      
      // 如果详情对话框打开，更新详情
      if (detailDialogVisible.value) {
        const detail = await getExchangeDetail(selectedExchange.value.id);
        selectedExchange.value = detail;
        adminRemarks.value = detail.adminRemarks || '';
      }
      
      // 更新列表
      fetchExchanges();
    } catch (error) {
      console.error('修改联系方式失败:', error);
      ElMessage.error('修改联系方式失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true;
  importFile.value = null;
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 处理导出命令
const handleExportCommand = (command) => {
  if (command === 'exportExcel') {
    exportExchangeData('excel');
  } else if (command === 'exportCsv') {
    exportExchangeData('csv');
  }
};

// 导出订单数据
const exportExchangeData = async (format) => {
  try {
    loading.value = true;
    
    // 准备导出参数
    const params = {
      format,
      status: activeTab.value === 'all' ? '' : activeTab.value,
      search: searchQuery.value
    };
    
    const response = await exportExchanges(params);
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]));
    const link = document.createElement('a');
    link.href = url;
    
    // 设置文件名
    const timestamp = new Date().getTime();
    const extension = format === 'excel' ? 'xlsx' : 'csv';
    const filename = `订单数据_${timestamp}.${extension}`;
    link.setAttribute('download', filename);
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success('订单数据导出成功');
  } catch (error) {
    console.error('导出订单数据失败:', error);
    ElMessage.error('导出订单数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 下载导入模板
const downloadTemplate = async (format) => {
  try {
    loading.value = true;
    
    const response = await downloadExchangeTemplate(format);
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]));
    const link = document.createElement('a');
    link.href = url;
    
    // 设置文件名
    const extension = format === 'excel' ? 'xlsx' : 'csv';
    const filename = `订单导入模板.${extension}`;
    link.setAttribute('download', filename);
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    ElMessage.error('下载模板失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 处理文件选择变化
const handleFileChange = (file) => {
  importFile.value = file.raw;
};

// 处理文件数量超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件');
};

// 处理文件移除
const handleRemove = () => {
  importFile.value = null;
};

// 导入订单数据
const importExchangeData = async () => {
  if (!importFile.value) {
    ElMessage.warning('请先选择文件');
    return;
  }
  
  // 校验文件格式
  const validFormats = ['.csv', '.xlsx', '.xls'];
  const fileName = importFile.value.name.toLowerCase();
  const isValidFormat = validFormats.some(format => fileName.endsWith(format));
  
  if (!isValidFormat) {
    ElMessage.error('文件格式不正确，请上传CSV或Excel文件');
    return;
  }
  
  importing.value = true;
  
  try {
    const formData = new FormData();
    formData.append('file', importFile.value);
    
    const response = await importExchanges(formData);
    
    // 处理导入结果
    ElMessage.success(`订单导入成功，共导入 ${response.success} 条记录`);
    
    // 关闭对话框
    importDialogVisible.value = false;
    
    // 刷新列表
    fetchExchanges();
    
    // 如果有导入错误，显示错误详情
    if (response.errors && response.errors.length > 0) {
      ElMessageBox.alert(
        `成功导入: ${response.success} 条记录<br>导入失败: ${response.errors.length} 条记录<br><br>失败原因:<br>${response.errors.map(err => `- 第 ${err.row} 行: ${err.message}`).join('<br>')}`,
        '导入结果',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          type: 'warning'
        }
      );
    }
  } catch (error) {
    console.error('导入订单数据失败:', error);
    
    // 处理不同错误情况
    let errorMsg = '导入订单数据失败，请稍后重试';
    if (error.response && error.response.data && error.response.data.message) {
      errorMsg = error.response.data.message;
    }
    
    ElMessage.error(errorMsg);
  } finally {
    importing.value = false;
  }
};

// 重置序号
const confirmResetAutoIncrement = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将检查数据库中是否有订单，如果没有将重置订单编号。确认继续吗？',
      '重置订单编号',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    loading.value = true;
    
    // 调用重置自增ID的API
    const response = await resetAutoIncrement();
    
    if (response.resetPerformed) {
      ElMessage.success('订单编号已重置，新订单将从001开始编号');
    } else {
      ElMessage.warning(`数据库中仍有${response.remainingCount}条订单记录，无法重置编号。请先删除所有订单。`);
    }
    
    fetchExchanges();
  } catch (error) {
    console.error('重置订单编号失败:', error);
    
    if (error === 'cancel') return; // 用户取消操作
    
    ElMessage.error('重置订单编号失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

</script>

<style scoped>
.exchange-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-wrapper,
.empty-state {
  margin: 40px 0;
  display: flex;
  justify-content: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  object-fit: cover;
}

.exchange-detail {
  margin-bottom: 20px;
}

.detail-actions {
  margin-top: 20px;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.el-tag {
  margin-right: 0;
}

.user-placeholder {
  cursor: pointer;
  color: #606266;
}

.payment-proof {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.payment-proof .el-image {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.payment-proof .el-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.15);
}

/* 重置可能导致图片预览层级问题的样式 */
:deep(.el-dialog__body),
:deep(.el-descriptions__body),
:deep(.el-descriptions-item__container) {
  position: static;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.image-error .el-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.import-dialog-content {
  margin: 10px 0;
}

.import-tips {
  margin-bottom: 20px;
}

.import-tips p {
  margin: 5px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.delete-warning {
  color: #f56c6c;
  font-weight: bold;
  margin: 10px 0;
}

.delete-status-info {
  margin: 15px 0;
  padding-left: 20px;
}

.delete-note {
  color: #e6a23c;
  font-style: italic;
  margin-top: 10px;
}
</style> 
<template>
  <div class="exchanges-container">
    <div class="page-header">
      <h1 class="page-title">我的兑换</h1>
      <div class="header-actions">
        <el-button type="primary" @click="goBack">返回商城</el-button>
      </div>
    </div>

    <el-card class="main-card">
      <!-- 标签页选择不同状态的兑换记录 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="待处理" name="pending"></el-tab-pane>
        <el-tab-pane label="已批准" name="approved"></el-tab-pane>
        <el-tab-pane label="已发货" name="shipped"></el-tab-pane>
        <el-tab-pane label="已完成" name="completed"></el-tab-pane>
        <el-tab-pane label="已拒绝" name="rejected"></el-tab-pane>
        <el-tab-pane label="已取消" name="cancelled"></el-tab-pane>
      </el-tabs>

      <!-- 兑换记录列表 -->
      <div v-if="loading" class="loading-wrapper">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="exchanges.length === 0" class="empty-state">
        <el-empty description="暂无兑换记录" />
      </div>
      
      <div v-else>
        <el-table :data="exchanges" style="width: 100%" border :header-cell-style="{ background: '#f5f7fa' }">
          <el-table-column prop="id" label="兑换编号" width="80" align="center" />
          
          <el-table-column label="商品信息" min-width="200">
            <template #default="{ row }">
              <div class="product-info">
                <div class="product-detail">
                  <div class="product-name">{{ row.Product?.name || '未知商品' }}</div>
                  <div class="product-quantity">数量: {{ row.quantity }}</div>
                  <div class="product-price">
                    <span v-if="row.paymentMethod === 'ly'">
                      {{ row.quantity * (row.Product?.lyPrice || 0) }} 光年币
                    </span>
                    <span v-else>
                      ¥{{ (row.quantity * (row.Product?.rmbPrice || 0)).toFixed(2) }}
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="支付方式" width="100" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.paymentMethod === 'ly'" type="primary" effect="light">
                光年币
              </el-tag>
              <el-tag v-else type="success" effect="light">
                人民币
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="申请时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="160" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.status === 'pending'"
                type="danger"
                size="small"
                @click="handleCancel(row)"
                :loading="cancelLoading === row.id"
              >
                取消
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="showExchangeDetail(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-wrapper" v-if="total > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="total"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
    
    <!-- 兑换详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="兑换详情"
      width="600px"
    >
      <div v-if="selectedExchange" class="exchange-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单编号">{{ selectedExchange.id }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDate(selectedExchange.createdAt) }}</el-descriptions-item>
          
          <el-descriptions-item label="商品名称" :span="2">
            {{ selectedExchange.Product?.name || '未知商品' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="数量">{{ selectedExchange.quantity }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag v-if="selectedExchange.paymentMethod === 'ly'" type="primary" effect="light">
              光年币 ({{ selectedExchange.Product?.lyPrice || 0 }})
            </el-tag>
            <el-tag v-else type="success" effect="light">
              人民币 (¥{{ selectedExchange.Product?.rmbPrice || 0 }})
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="联系方式">
            {{ selectedExchange.contactInfo }}
            <el-button 
              v-if="canEditContactInfo(selectedExchange)"
              type="primary" 
              link 
              size="small" 
              @click="showContactInfoDialog(selectedExchange)"
              style="margin-left: 10px"
            >
              修改
            </el-button>
          </el-descriptions-item>
          <el-descriptions-item label="职场位置">{{ selectedExchange.location }}</el-descriptions-item>
          
          <el-descriptions-item label="当前状态" :span="2">
            <el-tag :type="getStatusType(selectedExchange.status)">
              {{ getStatusText(selectedExchange.status) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item v-if="selectedExchange.remarks" label="备注" :span="2">
            <div style="white-space: pre-wrap;">{{ selectedExchange.remarks }}</div>
          </el-descriptions-item>
          
          <el-descriptions-item v-if="selectedExchange.adminRemarks" label="管理员备注" :span="2">
            <div style="white-space: pre-wrap;">{{ selectedExchange.adminRemarks }}</div>
          </el-descriptions-item>
          
          <el-descriptions-item v-if="selectedExchange.trackingNumber" label="物流单号" :span="1">
            {{ selectedExchange.trackingNumber }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedExchange.trackingCompany" label="物流公司" :span="1">
            {{ selectedExchange.trackingCompany }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="dialog-footer">
          <el-button 
            v-if="selectedExchange.status === 'pending'" 
            type="danger" 
            @click="handleCancel(selectedExchange, true)"
          >
            取消兑换
          </el-button>
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- 联系方式修改对话框 -->
    <el-dialog
      v-model="contactInfoDialogVisible"
      title="修改联系方式"
      width="500px"
    >
      <el-form
        ref="contactInfoFormRef"
        :model="contactInfoForm"
        :rules="contactInfoRules"
        label-position="top"
      >
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="contactInfoForm.contactInfo" placeholder="请输入联系方式" />
        </el-form-item>
        
        <el-form-item label="位置" prop="location">
          <el-select v-model="contactInfoForm.location" placeholder="请选择职场位置" style="width: 100%">
            <el-option label="北京" value="北京" />
            <el-option label="武汉" value="武汉" />
            <el-option label="长沙" value="长沙" />
            <el-option label="西安" value="西安" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="contactInfoDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitContactInfo" :loading="contactInfoSubmitting">
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserExchanges, getExchangeDetail, cancelExchange, updateContactInfo } from '../../api/exchanges';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// 页面状态
const loading = ref(false);
const exchanges = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const activeTab = ref('all');
const cancelLoading = ref(null);

// 兑换详情
const detailDialogVisible = ref(false);
const selectedExchange = ref(null);

// 联系方式修改
const contactInfoDialogVisible = ref(false);
const contactInfoSubmitting = ref(false);
const contactInfoFormRef = ref(null);
const contactInfoForm = reactive({
  contactInfo: '',
  location: ''
});

// 联系方式表单验证规则
const contactInfoRules = {
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { min: 6, max: 30, message: '长度在6到30个字符之间', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请选择职场位置', trigger: 'change' }
  ]
};

// 返回商城
const goBack = () => {
  router.push('/');
};

// 初始化页面
onMounted(() => {
  if (!authStore.isAuthenticated) {
    ElMessage.warning('请先登录');
    router.push('/login');
    return;
  }
  
  fetchExchanges();
});

// 获取兑换记录
const fetchExchanges = async () => {
  loading.value = true;
  
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      status: activeTab.value === 'all' ? '' : activeTab.value
    };
    
    const response = await getUserExchanges(params);
    exchanges.value = response.data || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取兑换记录失败:', error);
    ElMessage.error('获取兑换记录失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchExchanges();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchExchanges();
};

// 处理标签页切换
const handleTabChange = () => {
  currentPage.value = 1;
  fetchExchanges();
};

// 获取状态显示文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'approved': '已批准',
    'shipped': '已发货',
    'completed': '已完成',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 获取状态对应的标签类型
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'approved': 'success',
    'shipped': 'warning',
    'completed': 'success',
    'rejected': 'danger',
    'cancelled': 'info'
  };
  return typeMap[status] || 'info';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 查看兑换详情
const showExchangeDetail = async (exchange) => {
  try {
    const detail = await getExchangeDetail(exchange.id);
    selectedExchange.value = detail;
    detailDialogVisible.value = true;
  } catch (error) {
    console.error('获取兑换详情失败:', error);
    ElMessage.error('获取兑换详情失败，请稍后重试');
  }
};

// 取消兑换
const handleCancel = async (exchange, fromDetail = false) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消该兑换申请吗？',
      '取消兑换',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    cancelLoading.value = exchange.id;
    
    await cancelExchange(exchange.id);
    
    ElMessage.success('兑换申请已取消');
    
    if (fromDetail) {
      detailDialogVisible.value = false;
    }
    
    fetchExchanges();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消兑换申请失败:', error);
      ElMessage.error('取消兑换申请失败，请稍后重试');
    }
  } finally {
    cancelLoading.value = null;
  }
};

// 显示联系方式修改对话框
const showContactInfoDialog = (exchange) => {
  selectedExchange.value = exchange;
  contactInfoForm.contactInfo = exchange.contactInfo || '';
  contactInfoForm.location = exchange.location || '';
  contactInfoDialogVisible.value = true;
};

// 提交联系方式修改
const submitContactInfo = async () => {
  if (!contactInfoFormRef.value) return;
  
  await contactInfoFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    contactInfoSubmitting.value = true;
    
    try {
      await updateContactInfo(selectedExchange.value.id, {
        contactInfo: contactInfoForm.contactInfo,
        location: contactInfoForm.location
      });
      
      ElMessage.success('联系方式修改成功');
      contactInfoDialogVisible.value = false;
      
      // 如果详情对话框打开，更新详情
      if (detailDialogVisible.value) {
        const detail = await getExchangeDetail(selectedExchange.value.id);
        selectedExchange.value = detail;
      }
      
      // 更新列表
      fetchExchanges();
    } catch (error) {
      console.error('修改联系方式失败:', error);
      ElMessage.error('修改联系方式失败，请稍后重试');
    } finally {
      contactInfoSubmitting.value = false;
    }
  });
};

// 检查是否可以修改联系方式
const canEditContactInfo = (exchange) => {
  return ['pending', 'approved'].includes(exchange.status);
};
</script>

<style scoped>
.exchanges-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.main-card {
  margin-bottom: 20px;
}

.loading-wrapper,
.empty-state {
  margin: 40px 0;
  display: flex;
  justify-content: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}

.product-detail {
  flex: 1;
}

.product-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.exchange-detail {
  margin-bottom: 20px;
}

.dialog-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 
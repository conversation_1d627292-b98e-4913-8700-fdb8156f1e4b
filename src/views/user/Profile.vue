<template>
  <div class="profile-container">
    <div class="page-header">
      <h1 class="page-title">个人中心</h1>
      <div class="header-actions">
        <el-button type="primary" @click="goBack">返回商城</el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="profile-tabs">
      <el-tab-pane label="个人信息" name="info">
        <el-card class="profile-card">
          <div class="user-profile">
            <div class="avatar-section">
              <el-avatar :size="100" class="user-avatar" :src="user?.feishuAvatar">
                {{ user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
            </div>
            <div class="info-section">
              <div class="info-item">
                <span class="label">姓名：</span>
                <span class="value">{{ user?.username }}</span>
              </div>
              <div class="info-item">
                <span class="label">邮箱：</span>
                <span class="value">{{ user?.email || '未设置' }}</span>
              </div>
              <div class="info-item">
                <span class="label">手机号码：</span>
                <span class="value">{{ user?.mobile ? formatMobileNumber(user.mobile) : '未设置' }}</span>
              </div>
              <div class="info-item">
                <span class="label">部门：</span>
                <span class="value">{{ user?.department || '未设置' }}</span>
              </div>
              <div class="info-item" v-if="user?.departmentPath && user.departmentPath !== user?.department">
                <span class="label">部门路径：</span>
                <span class="value">{{ user.departmentPath }}</span>
              </div>
              <div class="info-item">
                <span class="label">职场：</span>
                <span class="value">{{ user?.workplace || '未设置' }}</span>
              </div>
              <div class="info-item">
                <span class="label">角色：</span>
                <el-tag :type="user?.role === 'admin' ? 'danger' : 'info'" size="small">
                  {{ user?.role === 'admin' ? '管理员' : '普通用户' }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="label">注册时间：</span>
                <span class="value">{{ formatDate(user?.createdAt) }}</span>
              </div>
            </div>
          </div>
          
          <div class="action-section">
            <el-button type="primary" @click="showPasswordDialog">修改密码</el-button>
          </div>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="我的兑换" name="exchanges">
        <!-- 兑换记录列表 -->
        <el-card>
          <!-- 标签页选择不同状态的兑换记录 -->
          <el-tabs v-model="exchangeStatusTab" @tab-click="handleExchangeTabChange">
            <el-tab-pane label="全部" name="all"></el-tab-pane>
            <el-tab-pane label="待处理" name="pending"></el-tab-pane>
            <el-tab-pane label="已批准" name="approved"></el-tab-pane>
            <el-tab-pane label="已发货" name="shipped"></el-tab-pane>
            <el-tab-pane label="已完成" name="completed"></el-tab-pane>
            <el-tab-pane label="已拒绝" name="rejected"></el-tab-pane>
            <el-tab-pane label="已取消" name="cancelled"></el-tab-pane>
          </el-tabs>

          <div v-if="loading" class="loading-wrapper">
            <el-skeleton :rows="5" animated />
          </div>
          
          <div v-else-if="exchanges.length > 0">
            <el-table :data="exchanges" style="width: 100%" border :header-cell-style="{ background: '#f5f7fa' }">
              <el-table-column label="兑换编号" width="180" align="center">
                <template #default="{ row }">
                  {{ formatOrderNumber(row) }}
                </template>
              </el-table-column>
              
              <el-table-column label="商品信息" min-width="200">
                <template #default="{ row }">
                  <div class="product-info">
                    <div class="product-detail">
                      <div class="product-name">{{ row.Product?.name || '未知商品' }}</div>
                      <div class="product-quantity">数量: {{ row.quantity }}</div>
                      <div class="product-price">
                        <span v-if="row.paymentMethod === 'ly'">
                          {{ row.quantity * (row.Product?.lyPrice || 0) }} 光年币
                        </span>
                        <span v-else>
                          ¥{{ (row.quantity * (row.Product?.rmbPrice || 0)).toFixed(2) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="支付方式" width="100" align="center">
                <template #default="{ row }">
                  <el-tag v-if="row.paymentMethod === 'ly'" type="primary" effect="light">
                    光年币
                  </el-tag>
                  <el-tag v-else type="success" effect="light">
                    人民币
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="申请时间" width="160">
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="160" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button
                    v-if="row.status === 'pending'"
                    type="danger"
                    size="small"
                    @click="handleCancel(row)"
                    :loading="cancelLoading === row.id"
                  >
                    取消
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleDetail(row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination-wrapper" v-if="total > 0">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50]"
                :total="total"
                layout="total, sizes, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background
              />
            </div>
          </div>
          
          <el-empty v-else description="暂无兑换记录" />
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="我的反馈" name="feedbacks">
        <!-- 反馈记录列表 -->
        <div v-if="feedbackLoading" class="loading-wrapper">
          <el-skeleton :rows="5" animated />
        </div>
        
        <div v-else-if="feedbacks.length > 0">
          <el-card>
            <!-- 标签页选择不同状态的反馈记录 -->
            <el-tabs v-model="feedbackStatusTab" @tab-click="handleFeedbackTabChange">
              <el-tab-pane label="全部" name="all"></el-tab-pane>
              <el-tab-pane label="待处理" name="pending"></el-tab-pane>
              <el-tab-pane label="处理中" name="processing"></el-tab-pane>
              <el-tab-pane label="已完成" name="completed"></el-tab-pane>
            </el-tabs>

            <el-table :data="feedbacks" style="width: 100%" border :header-cell-style="{ background: '#f5f7fa' }">
              <el-table-column prop="id" label="编号" width="80" align="center" />
              
              <el-table-column label="反馈类型" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getFeedbackTypeTag(row.type)">
                    {{ getFeedbackTypeText(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column prop="title" label="标题" min-width="180" />
              
              <el-table-column label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getFeedbackStatusType(row.status)">
                    {{ getFeedbackStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="提交时间" width="160">
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleFeedbackDetail(row.id)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination-wrapper" v-if="feedbackTotal > 0">
              <el-pagination
                v-model:current-page="feedbackCurrentPage"
                v-model:page-size="feedbackPageSize"
                :page-sizes="[10, 20, 50]"
                :total="feedbackTotal"
                layout="total, sizes, prev, pager, next"
                @size-change="handleFeedbackSizeChange"
                @current-change="handleFeedbackCurrentChange"
                background
              />
            </div>
          </el-card>
        </div>
        
        <el-empty v-else description="暂无反馈记录" />
      </el-tab-pane>
    </el-tabs>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="400px"
      destroy-on-close
    >
      <el-form 
        ref="passwordFormRef" 
        :model="passwordForm" 
        :rules="passwordRules"
        label-position="top"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword" 
            type="password" 
            show-password
            placeholder="请输入当前密码"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updatePassword" :loading="submitting">确认</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 兑换详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="兑换详情"
      width="90%"
      max-width="600px"
      destroy-on-close
    >
      <div v-if="currentRecord" class="record-detail">
        <div class="detail-header">
          <h3>{{ currentRecord.Product?.name }}</h3>
          <el-tag :type="getStatusType(currentRecord.status)" effect="light">
            {{ getStatusText(currentRecord.status) }}
          </el-tag>
        </div>
        
        <div class="detail-item">
          <div class="item-label">兑换编号</div>
          <div class="item-value">{{ formatOrderNumber(currentRecord) }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">支付方式</div>
          <div class="item-value">
            {{ currentRecord.paymentMethod === 'ly' ? '光年币' : '人民币' }}
          </div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">数量</div>
          <div class="item-value">{{ currentRecord.quantity }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">金额</div>
          <div class="item-value">
            <span v-if="currentRecord.paymentMethod === 'ly'">
              {{ currentRecord.quantity * (currentRecord.Product?.lyPrice || 0) }} 光年币
            </span>
            <span v-else>
              ¥{{ (currentRecord.quantity * (currentRecord.Product?.rmbPrice || 0)).toFixed(2) }}
            </span>
          </div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">申请时间</div>
          <div class="item-value">{{ formatDate(currentRecord.createdAt) }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">联系信息</div>
          <div class="item-value">{{ currentRecord.contactInfo }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">所在位置</div>
          <div class="item-value">{{ currentRecord.location }}</div>
        </div>
        
        <div class="detail-item" v-if="currentRecord.remarks">
          <div class="item-label">备注</div>
          <div class="item-value">{{ currentRecord.remarks }}</div>
        </div>
        
        <div class="detail-item" v-if="currentRecord.adminRemarks">
          <div class="item-label">管理员备注</div>
          <div class="item-value">{{ currentRecord.adminRemarks }}</div>
        </div>
        
        <div class="detail-item" v-if="currentRecord.trackingNumber && currentRecord.trackingCompany">
          <div class="item-label">物流信息</div>
          <div class="item-value">
            {{ currentRecord.trackingCompany }}: {{ currentRecord.trackingNumber }}
          </div>
        </div>
        
        <div class="detail-actions" v-if="currentRecord && currentRecord.status === 'pending'">
          <el-button type="danger" @click="handleCancel(currentRecord)" :loading="cancelLoading === currentRecord.id">取消申请</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 反馈详情对话框 -->
    <el-dialog
      v-model="feedbackDetailDialogVisible"
      title="反馈详情"
      width="90%"
      max-width="600px"
      destroy-on-close
    >
      <div v-if="currentFeedback" class="feedback-detail">
        <div class="detail-header">
          <h3>{{ currentFeedback.title }}</h3>
          <el-tag :type="getFeedbackStatusType(currentFeedback.status)">
            {{ getFeedbackStatusText(currentFeedback.status) }}
          </el-tag>
        </div>
        
        <div class="detail-item">
          <div class="item-label">反馈类型</div>
          <div class="item-value">
            <el-tag :type="getFeedbackTypeTag(currentFeedback.type)" effect="plain">
              {{ getFeedbackTypeText(currentFeedback.type) }}
            </el-tag>
          </div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">提交时间</div>
          <div class="item-value">{{ formatDate(currentFeedback.createdAt) }}</div>
        </div>
        
        <div class="detail-item content-item">
          <div class="item-label">反馈内容</div>
          <div class="item-value feedback-content">{{ currentFeedback.content }}</div>
        </div>
        
        <template v-if="currentFeedback.adminReply">
          <div class="reply-section">
            <div class="reply-header">
              <i class="el-icon-chat-dot-round"></i>
              <span>管理员回复</span>
            </div>
            <div class="reply-content">
              {{ currentFeedback.adminReply }}
            </div>
            <div class="reply-time" v-if="currentFeedback.updatedAt !== currentFeedback.createdAt">
              {{ formatDate(currentFeedback.updatedAt) }}
            </div>
          </div>
        </template>
        
        <div class="empty-reply" v-else-if="currentFeedback.status === 'pending'">
          <el-empty description="管理员暂未回复" :image-size="80" />
        </div>
        
        <div class="empty-reply" v-else-if="currentFeedback.status === 'processing'">
          <el-empty description="您的反馈正在处理中，请耐心等待" :image-size="80" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useAuthStore } from '../../stores/auth';
import { updatePassword as updateUserPassword } from '../../api/users';
import { getUserExchanges, cancelExchange, getExchangeDetail } from '../../api/exchanges';
import { getUserFeedbacks, getFeedbackDetail } from '../../api/feedback';
import { formatOrderNumber, formatMobileNumber } from '../../utils/format';
import api from '../../api';

const router = useRouter();
const authStore = useAuthStore();

// 主标签页
const activeTab = ref('info');

// 用户数据
const user = computed(() => authStore.user);
const loading = ref(false);
const submitting = ref(false);

// 修改密码对话框
const passwordDialogVisible = ref(false);
const passwordFormRef = ref(null);
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码表单验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 兑换记录数据
const exchanges = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const exchangeStatusTab = ref('all');

// 兑换详情对话框
const detailDialogVisible = ref(false);
const currentRecord = ref(null);
const cancelLoading = ref(null);

// 反馈记录数据
const feedbackLoading = ref(false);
const feedbacks = ref([]);
const feedbackTotal = ref(0);
const feedbackCurrentPage = ref(1);
const feedbackPageSize = ref(10);
const feedbackStatusTab = ref('all');

// 反馈详情对话框
const feedbackDetailDialogVisible = ref(false);
const currentFeedback = ref(null);

// 返回商城
const goBack = () => {
  router.push('/');
};

// 显示修改密码对话框
const showPasswordDialog = () => {
  passwordDialogVisible.value = true;
};

// 更新密码
const updatePassword = async () => {
  if (!passwordFormRef.value) return;
  
  await passwordFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    submitting.value = true;
    
    try {
      await updateUserPassword({
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword
      });
      
      ElMessage.success('密码修改成功');
      passwordDialogVisible.value = false;
      
      // 清空表单
      passwordForm.oldPassword = '';
      passwordForm.newPassword = '';
      passwordForm.confirmPassword = '';
    } catch (error) {
      console.error('修改密码失败:', error);
      ElMessage.error(error.response?.data?.message || '修改密码失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

// 获取兑换记录
const fetchExchanges = async () => {
  loading.value = true;
  
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      status: exchangeStatusTab.value === 'all' ? '' : exchangeStatusTab.value
    };
    
    const response = await getUserExchanges(params);
    exchanges.value = response.data || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取兑换记录失败:', error);
    ElMessage.error('获取兑换记录失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchExchanges();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchExchanges();
};

// 处理标签页切换
const handleExchangeTabChange = () => {
  currentPage.value = 1;
  fetchExchanges();
};

// 获取状态显示文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'approved': '已批准',
    'shipped': '已发货',
    'completed': '已完成',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 获取状态对应的标签类型
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'approved': 'success',
    'shipped': 'warning',
    'completed': 'success',
    'rejected': 'danger',
    'cancelled': 'info'
  };
  return typeMap[status] || 'info';
};

// 获取状态对应的CSS类名
const getStatusClass = (status) => {
  const classMap = {
    'pending': 'status-pending',
    'approved': 'status-approved',
    'shipped': 'status-shipped',
    'completed': 'status-completed',
    'rejected': 'status-rejected',
    'cancelled': 'status-cancelled'
  };
  return classMap[status] || '';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 查看兑换详情
const handleDetail = async (record) => {
  try {
    const response = await getExchangeDetail(record.id);
    currentRecord.value = response;
    detailDialogVisible.value = true;
  } catch (error) {
    console.error('获取兑换详情失败:', error);
    ElMessage.error('获取兑换详情失败，请稍后重试');
  }
};

// 取消兑换申请
const handleCancel = (record) => {
  ElMessageBox.confirm(
    '确定要取消该兑换申请吗？',
    '取消确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    cancelLoading.value = record.id;
    try {
      await cancelExchange(record.id);
      ElMessage.success('兑换申请已取消');
      
      // 更新当前记录状态
      if (currentRecord.value && currentRecord.value.id === record.id) {
        currentRecord.value.status = 'cancelled';
      }
      
      // 更新列表中的状态
      const index = exchanges.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        exchanges.value[index].status = 'cancelled';
      }
      
      // 刷新列表
      fetchExchanges();
      
      // 如果在详情对话框，关闭它
      if (detailDialogVisible.value) {
        detailDialogVisible.value = false;
      }
    } catch (error) {
      console.error('取消兑换申请失败:', error);
      ElMessage.error(error.response?.data?.message || '取消兑换申请失败，请稍后重试');
    } finally {
      cancelLoading.value = null;
    }
  }).catch(() => {
    // 用户取消操作，不做任何处理
  });
};

// 获取反馈记录
const fetchFeedbacks = async () => {
  feedbackLoading.value = true;
  
  try {
    const params = {
      page: feedbackCurrentPage.value,
      limit: feedbackPageSize.value,
      status: feedbackStatusTab.value === 'all' ? '' : feedbackStatusTab.value
    };
    
    const response = await getUserFeedbacks(params);
    feedbacks.value = response.data || [];
    feedbackTotal.value = response.total || 0;
  } catch (error) {
    console.error('获取反馈记录失败:', error);
    ElMessage.error('获取反馈记录失败，请稍后重试');
  } finally {
    feedbackLoading.value = false;
  }
};

// 处理分页变化
const handleFeedbackCurrentChange = (page) => {
  feedbackCurrentPage.value = page;
  fetchFeedbacks();
};

const handleFeedbackSizeChange = (size) => {
  feedbackPageSize.value = size;
  feedbackCurrentPage.value = 1;
  fetchFeedbacks();
};

// 处理标签页切换
const handleFeedbackTabChange = () => {
  feedbackCurrentPage.value = 1;
  fetchFeedbacks();
};

// 获取反馈类型文本
const getFeedbackTypeText = (type) => {
  const typeMap = {
    'product': '商品相关',
    'feature': '功能建议',
    'bug': '问题反馈',
    'other': '其他反馈'
  };
  return typeMap[type] || type;
};

// 获取反馈类型标签类型
const getFeedbackTypeTag = (type) => {
  const tagMap = {
    'product': 'info',
    'feature': 'success',
    'bug': 'warning',
    'other': 'info'
  };
  return tagMap[type] || 'info';
};

// 获取反馈状态文本
const getFeedbackStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成'
  };
  return statusMap[status] || status;
};

// 获取反馈状态标签类型
const getFeedbackStatusType = (status) => {
  const tagMap = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success'
  };
  return tagMap[status] || 'info';
};

// 查看反馈详情
const handleFeedbackDetail = async (id) => {
  try {
    const response = await getFeedbackDetail(id);
    currentFeedback.value = response;
    feedbackDetailDialogVisible.value = true;
  } catch (error) {
    console.error('获取反馈详情失败:', error);
    ElMessage.error('获取反馈详情失败，请稍后重试');
  }
};

// 监听标签页变化
watch(activeTab, (newVal) => {
  if (newVal === 'exchanges') {
    fetchExchanges();
  } else if (newVal === 'feedbacks') {
    fetchFeedbacks();
  }
});

onMounted(() => {
  // 根据路由参数切换到指定标签页
  const tab = router.currentRoute.value.query.tab;
  if (tab) {
    activeTab.value = tab;
  }
  
  // 如果初始标签页是兑换记录，则加载数据
  if (activeTab.value === 'exchanges') {
    fetchExchanges();
  } else if (activeTab.value === 'feedbacks') {
    fetchFeedbacks();
  }
});


</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #3490dc, #6574cd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.profile-tabs {
  margin-bottom: 2rem;
}

.profile-card {
  margin-bottom: 2rem;
}

.user-profile {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  padding: 1.5rem;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  background: linear-gradient(135deg, #3490dc, #6574cd);
  color: white;
  font-weight: bold;
  font-size: 2.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.info-section {
  flex: 1;
  min-width: 300px;
}

.info-item {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
}

.label {
  font-weight: 600;
  color: #64748b;
  width: 100px;
}

.value {
  color: #334155;
}

.action-section {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
  padding: 0 1.5rem 1.5rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.loading-wrapper {
  padding: 2rem 0;
}

.pagination-wrapper {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-detail {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.product-name {
  font-weight: 600;
}

.product-quantity {
  color: #64748b;
  font-size: 0.9rem;
}

/* 兑换详情样式 */
.record-detail {
  padding: 1rem 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.detail-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.detail-item {
  display: flex;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px dashed #e2e8f0;
}

.item-label {
  flex: 0 0 120px;
  color: #64748b;
  font-weight: 600;
}

.item-value {
  flex: 1;
}

.detail-actions {
  margin-top: 2rem;
  display: flex;
  justify-content: flex-end;
}

/* 状态样式 */
.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-approved {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-shipped {
  background-color: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-rejected {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-cancelled {
  background-color: #f5f5f5;
  color: #8c8c8c;
  border: 1px solid #d9d9d9;
}

/* 反馈详情样式 */
.feedback-detail {
  padding: 1rem 0;
}

.content-item {
  align-items: flex-start;
}

.feedback-content {
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
  margin-top: 8px;
}

.reply-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #f0f9eb;
  border-radius: 8px;
  border-left: 4px solid #67c23a;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #67c23a;
  margin-bottom: 12px;
}

.reply-content {
  color: #303133;
  white-space: pre-wrap;
  line-height: 1.6;
}

.reply-time {
  margin-top: 10px;
  text-align: right;
  font-size: 0.85rem;
  color: #909399;
}

.empty-reply {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

@media (max-width: 768px) {
  .profile-container {
    padding: 1rem;
  }
  
  .user-profile {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .info-item {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }
  
  .label {
    width: auto;
  }
  
  .item-label {
    flex: 0 0 100px;
  }
}
</style> 
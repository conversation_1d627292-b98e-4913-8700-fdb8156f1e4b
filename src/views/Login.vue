<template>
  <div class="login-container">
    <el-card class="login-card">
      <div class="app-info">
        <div class="app-logo">光年</div>
        <h1 class="app-name">光年小卖部</h1>
        <p class="login-subtitle">请选择登录方式</p>
      </div>
      
      <!-- 登录选择页面 -->
      <div v-if="!showLoginForm" class="login-options">
        <!-- 飞书登录选项 (主要选项) -->
        <div class="login-option-primary">
          <FeishuLogin 
            @login-success="handleFeishuLoginSuccess" 
            @login-error="handleFeishuLoginError"
          />
          <p class="login-tip">推荐！使用公司飞书账号直接登录</p>
        </div>
        
        <!-- 分隔线 -->
        <div class="divider">
          <span>或</span>
        </div>
        
        <!-- 普通登录选项 (次要选项) -->
        <div class="login-option-secondary">
          <el-button 
            class="normal-login-button"
            @click="showLoginForm = true"
          >
            账号密码登录
          </el-button>
          <p class="login-tip secondary-tip">使用已注册的账号和密码登录</p>
        </div>
      </div>
      
      <!-- 普通登录表单 -->
      <div v-else>
        <div class="form-header">
          <h2 class="form-title">{{ userType === 'admin' ? '管理员登录' : '用户登录' }}</h2>
          <div>
            <el-switch
              v-model="isAdmin"
              active-text="管理员"
              inactive-text="普通用户"
              @change="handleSwitchChange"
            />
          </div>
        </div>
        
        <el-form 
          ref="loginFormRef" 
          :model="loginForm" 
          :rules="rules" 
          label-width="80px"
          @submit.prevent="handleSubmit"
        >
          <el-form-item label="姓名" prop="username">
            <el-input 
              v-model="loginForm.username" 
              placeholder="请输入姓名"
              prefix-icon="User"
              @keyup.enter="focusPassword"
            />
            <div class="form-tip">请输入完整姓名，登录时需要同时使用姓名和邮箱</div>
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input 
              v-model="loginForm.email" 
              placeholder="请输入邮箱"
              prefix-icon="Message"
              @keyup.enter="focusPassword"
            />
            <div class="form-tip">请输入完整邮箱，登录时需要姓名和邮箱同时匹配</div>
          </el-form-item>
          
          <el-form-item label="密码" prop="password">
            <el-input 
              ref="passwordInput"
              v-model="loginForm.password" 
              type="password" 
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              @keyup.enter="handleSubmit"
            />
          </el-form-item>
          
          <!-- 添加记住我复选框 -->
          <el-form-item>
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
          </el-form-item>
          
          <div v-if="errorMessage" class="error-message">
            <el-alert
              :title="errorMessage"
              type="error"
              :closable="false"
              show-icon
            />
          </div>
          
          <div class="tips">
            <p v-if="isAdmin">提示: 管理员账号请飞书联系亚媚姐 </p>
            <p v-else>提示: 新用户需要先注册账号，已有账号的用户请直接登录</p>
            <p v-if="!isAdmin" class="register-tip">
              <strong>没有账号？</strong>
              <el-button type="text" @click="showRegisterForm = true">立即注册</el-button>
            </p>
          </div>
        </el-form>
        
        <!-- 表单按钮组 -->
        <div class="form-actions">
          <el-button @click="showLoginForm = false">返回</el-button>
          <el-button 
            type="primary" 
            :loading="loading" 
            @click="handleSubmit"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </div>
      </div>
      
      <div class="server-status" v-if="serverStatus">
        <el-tag v-if="serverStatus === 'connected'" type="success">服务器已连接</el-tag>
        <el-tag v-else-if="serverStatus === 'disconnected'" type="danger">服务器未连接</el-tag>
        <el-tag v-else type="info">检查服务器状态中...</el-tag>
      </div>
    </el-card>
    
    <!-- 注册表单对话框 -->
    <el-dialog
      v-model="showRegisterForm"
      title="用户注册"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form 
        ref="registerFormRef" 
        :model="registerForm" 
        :rules="registerRules" 
        label-width="100px"
      >
        <el-form-item label="姓名" prop="username">
          <el-input v-model="registerForm.username" placeholder="请输入姓名" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="registerForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="registerForm.mobile" placeholder="请输入手机号码（可选）" />
        </el-form-item>
        
        <el-form-item label="部门" prop="department">
          <el-input v-model="registerForm.department" placeholder="请输入所属部门" />
        </el-form-item>
        
        <el-form-item label="职场" prop="workplace">
          <el-select v-model="registerForm.workplace" placeholder="请选择所在职场">
            <el-option v-for="item in workplaceOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="registerForm.password" 
            type="password" 
            placeholder="请输入密码" 
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="registerForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入密码" 
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRegisterForm = false">取消</el-button>
          <el-button type="primary" @click="handleRegister" :loading="registerLoading">注册</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { ElMessage, ElLoading } from 'element-plus';
import { User, Lock, Message } from '@element-plus/icons-vue';
import api from '../api';
import { register, login } from '../api/auth';
import FeishuLogin from '../components/FeishuLogin.vue'; // 导入飞书登录组件

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const passwordInput = ref(null);
const serverStatus = ref('checking');

// 添加状态控制变量：是否显示登录表单
const showLoginForm = ref(false);

// 记住我功能
const rememberMe = ref(false);
const REMEMBER_ME_KEY = 'remember_login';
const REMEMBER_USERNAME_KEY = 'remember_username';
const REMEMBER_EMAIL_KEY = 'remember_email';
const REMEMBER_PASSWORD_KEY = 'remember_password';

// 用户类型切换
const isAdmin = ref(false); // 默认为普通用户模式
const userType = computed(() => isAdmin.value ? 'admin' : 'user');
const hasAccount = ref(false);

// 登录表单数据
const loginForm = reactive({
  username: '',
  email: '',
  password: ''
});

// 注册相关
const showRegisterForm = ref(false);
const registerFormRef = ref(null);
const registerLoading = ref(false);
const workplaceOptions = ref([
  '北京', '武汉', '长沙', '西安', '深圳'
]);
const registerForm = reactive({
  username: '',
  email: '',
  mobile: '',
  department: '',
  workplace: '',
  password: '',
  confirmPassword: ''
});

// 登录表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 30, message: '姓名长度应为2到30个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ]
};

// 注册表单验证规则
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
};

const registerRules = {
  username: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 30, message: '姓名长度应为2到30个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  mobile: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请输入所属部门', trigger: 'blur' }
  ],
  workplace: [
    { required: true, message: '请输入所在职场', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
};

// 表单引用
const loginFormRef = ref(null);

// 状态
const loading = computed(() => authStore.loading);
const errorMessage = computed(() => authStore.error);

// 处理用户类型切换
const handleSwitchChange = () => {
  if (isAdmin.value) {
    loginForm.username = '';
    loginForm.email = '';
    loginForm.password = '';
    console.log('切换到管理员模式');
  } else {
    loginForm.username = '';
    loginForm.email = '';
    loginForm.password = '';
    console.log('切换到普通用户模式');
  }
};

// 聚焦密码输入框
const focusPassword = () => {
  if (passwordInput.value) {
    passwordInput.value.focus();
  }
};

// 检查服务器状态
const checkServerStatus = async () => {
  try {
    serverStatus.value = 'checking';
    
    // 尝试发送一个简单请求以检查服务器状态
    try {
      // 增加超时时间，避免网络波动导致误判
      await api.get('/health', { timeout: 5000 });
      serverStatus.value = 'connected';
      authStore.error = null; // 清除之前的错误消息
      return true;
    } catch (healthError) {
      console.log('健康检查接口失败，尝试根路径', healthError);
      
      // 如果健康检查接口失败，尝试访问根路径
      try {
        await api.get('/', { timeout: 5000 });
        serverStatus.value = 'connected';
        authStore.error = null; // 清除之前的错误消息
        return true;
      } catch (rootError) {
        // 如果两个请求都失败，则认为服务器不可用
        console.error('根路径访问失败', rootError);
        serverStatus.value = 'disconnected';
        authStore.error = '无法连接到服务器，请确认服务器已启动';
        return false;
      }
    }
  } catch (error) {
    console.error('服务器状态检查完全失败', error);
    serverStatus.value = 'disconnected';
    authStore.error = '无法连接到服务器，请确认服务器已启动';
    return false;
  }
};

// 尝试从错误中恢复服务
const recoverServer = async () => {
  ElMessage.info('正在尝试重新连接服务器...');
  const connected = await checkServerStatus();
  if (connected) {
    ElMessage.success('成功连接到服务器');
    return true;
  } else {
    ElMessage.error('服务器仍然不可用，请检查服务器是否运行');
    return false;
  }
};

// 保存登录信息到本地存储
const saveLoginInfo = () => {
  if (rememberMe.value) {
    localStorage.setItem(REMEMBER_ME_KEY, 'true');
    localStorage.setItem(REMEMBER_USERNAME_KEY, loginForm.username);
    localStorage.setItem(REMEMBER_EMAIL_KEY, loginForm.email);
    localStorage.setItem(REMEMBER_PASSWORD_KEY, loginForm.password);
  } else {
    // 如果取消记住我，则清除本地存储
    clearLoginInfo();
  }
};

// 清除登录信息
const clearLoginInfo = () => {
  localStorage.removeItem(REMEMBER_ME_KEY);
  localStorage.removeItem(REMEMBER_USERNAME_KEY);
  localStorage.removeItem(REMEMBER_EMAIL_KEY);
  localStorage.removeItem(REMEMBER_PASSWORD_KEY);
};

// 处理表单提交
const handleSubmit = async () => {
  if (!loginFormRef.value) return;
  
  try {
    // 1. 验证表单
    await loginFormRef.value.validate();
    
    // 2. 确保姓名、邮箱和密码不为空
    if (!loginForm.username || !loginForm.email || !loginForm.password) {
      ElMessage.warning('姓名、邮箱和密码不能为空');
      return;
    }
    
    // 2.1 提醒用户需要同时使用姓名和邮箱进行登录
    if (!loginForm.username || !loginForm.email) {
      ElMessage.warning('请同时提供姓名和邮箱进行登录，两者需要匹配');
      return;
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(loginForm.email)) {
      ElMessage.warning('请输入正确的邮箱格式');
      return;
    }
    
    // 验证密码长度
    if (loginForm.password.length < 6) {
      ElMessage.warning('密码长度必须至少为6个字符');
      return;
    }
    
    // 3. 准备登录数据并记录
    console.log('==========================================');
    console.log('开始登录流程');
    console.log('当前模式: ' + (isAdmin.value ? '管理员' : '普通用户'));
    console.log('姓名: ' + loginForm.username);
    console.log('邮箱: ' + loginForm.email);
    console.log('密码长度: ' + loginForm.password.length);
    console.log('记住我: ' + rememberMe.value);
    
    // 4. 设置加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '登录中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    try {
      // 5. 准备请求数据
      const loginData = {
        username: loginForm.username.trim(),
        email: loginForm.email.trim(),
        password: loginForm.password.trim(),
        userType: isAdmin.value ? 'admin' : 'user',
        rememberMe: rememberMe.value // 传递rememberMe参数，仅用于控制后端token过期时间
      };
      
      // 6. 执行登录请求 - 使用auth API
      console.log('发送登录请求...');
      console.log('姓名: ' + loginData.username);
      console.log('邮箱: ' + loginData.email);
      console.log('密码长度: ' + loginData.password.length);
      
      const response = await login(loginData);
      
      // 7. 处理响应
      console.log('收到响应状态: ' + response.status);
      
      // 8. 检查错误
      if (!response || !response.token) {
        throw new Error(response?.message || '登录失败');
      }
      
      // 9. 登录成功处理
      console.log('登录成功! 用户角色: ' + response.user.role);
      
      // 保存记住我状态和登录信息（仅保存表单信息，不涉及token）
      saveLoginInfo();
      
      // 10. 更新存储
      authStore.user = response.user;
      authStore.token = response.token;
      authStore.error = null;
      
      // token始终只存储在sessionStorage中，这样关闭浏览器后会自动清除
      sessionStorage.setItem('token', response.token);
      // 确保清除localStorage中可能存在的token
      localStorage.removeItem('token');
      
      // 11. 跳转页面
      ElMessage.success('登录成功');
      
      if (response.user.role === 'admin') {
        console.log('跳转到管理页面');
        router.push('/admin/products');
      } else {
        console.log('跳转到普通用户首页');
        router.push('/');
      }
      
      console.log('登录流程完成');
      console.log('==========================================');
      
    } catch (error) {
      // 错误处理
      console.error('登录失败:', error);
      console.log('==========================================');
      ElMessage.error(error.message || '登录失败，请检查姓名、邮箱和密码');
    } finally {
      // 关闭加载
      loadingInstance.close();
    }
  } catch (validationError) {
    console.error('表单验证失败:', validationError);
    ElMessage.error('请填写有效的登录信息');
  }
};

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return;
  
  try {
    await registerFormRef.value.validate();
    
    // 确保所有必填字段都已填写
    if (!registerForm.username || !registerForm.email || !registerForm.department || 
        !registerForm.workplace || !registerForm.password || !registerForm.confirmPassword) {
      ElMessage.warning('请填写所有必填项');
      return;
    }
    
    registerLoading.value = true;
    
    // 调用注册接口
    try {
      // 这里调用注册API，请根据实际情况修改
      await register(registerForm);
      
      ElMessage.success('注册成功，请登录');
      showRegisterForm.value = false;
      
      // 设置登录表单姓名为刚注册的姓名
      loginForm.username = registerForm.username;
      loginForm.email = registerForm.email;
      loginForm.password = '';
      
      // 显示登录表单
      showLoginForm.value = true;
      
      // 清空注册表单
      registerForm.username = '';
      registerForm.email = '';
      registerForm.mobile = '';
      registerForm.department = '';
      registerForm.workplace = '';
      registerForm.password = '';
      registerForm.confirmPassword = '';
      
    } catch (error) {
      console.error('注册失败:', error);
      if (error.response?.data?.message === 'Email already exists') {
        ElMessage.error('该邮箱已被注册，请使用其他邮箱');
      } else {
        ElMessage.error(error.response?.data?.message || '注册失败，请稍后再试');
      }
    }
  } catch (validationError) {
    console.error('表单验证失败:', validationError);
  } finally {
    registerLoading.value = false;
  }
};

// 加载保存的登录信息
const loadSavedLoginInfo = () => {
  const remembered = localStorage.getItem(REMEMBER_ME_KEY);
  if (remembered === 'true') {
    rememberMe.value = true;
    loginForm.username = localStorage.getItem(REMEMBER_USERNAME_KEY) || '';
    loginForm.email = localStorage.getItem(REMEMBER_EMAIL_KEY) || '';
    loginForm.password = localStorage.getItem(REMEMBER_PASSWORD_KEY) || '';
    
    // 如果有保存的登录信息，直接显示登录表单
    if (loginForm.username && loginForm.email) {
      showLoginForm.value = true;
    }
  }
};

// 处理飞书登录成功
const handleFeishuLoginSuccess = (user) => {
  console.log('飞书登录成功:', user);
  ElMessage.success('飞书登录成功');
  
  // 跳转到首页或之前尝试访问的页面
  const redirectPath = route.query.redirect || '/';
  router.push(redirectPath);
};

// 处理飞书登录错误
const handleFeishuLoginError = (errorMessage) => {
  console.error('飞书登录错误:', errorMessage);
  ElMessage.error(`飞书登录失败: ${errorMessage}`);
};

// 组件挂载时检查服务器状态和加载保存的登录信息
onMounted(() => {
  checkServerStatus();
  
  // 加载保存的登录信息
  loadSavedLoginInfo();
  
  // 如果URL中有参数指定用户类型，则切换
  if (route.query.type === 'user') {
    isAdmin.value = false;
    handleSwitchChange();
  }
});
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f0f4f8 0%, #d7e3f3 100%);
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.login-card {
  width: 100%;
  max-width: 480px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 0;
}

.login-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

/* 应用信息区域 */
.app-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0 20px;
}

.app-logo {
  width: 70px;
  height: 70px;
  background-color: #3370ff;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 15px;
}

.app-name {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px;
}

.login-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-title {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

/* 登录选择页面样式 */
.login-options {
  display: flex;
  flex-direction: column;
  padding: 10px 30px 30px;
}

.login-option-primary {
  margin-bottom: 15px;
}

.login-option-secondary {
  text-align: center;
}

.login-tip {
  text-align: center;
  font-size: 13px;
  color: #606266;
  margin: 8px 0 0;
}

.secondary-tip {
  color: #909399;
}

.normal-login-button {
  width: 100%;
  padding: 12px 20px;
  color: #606266;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  font-size: 16px;
  transition: all 0.3s ease;
}

.normal-login-button:hover {
  color: #409EFF;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

/* 表单操作按钮样式 */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.tips {
  margin: 15px 0;
  color: #606266;
  font-size: 14px;
}

.register-tip {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 10px;
}

.server-status {
  margin-top: 15px;
  text-align: center;
  padding-bottom: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
  padding-left: 2px;
}

/* 分隔线样式 */
.divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: #c0c4cc;
  font-size: 12px;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: #ebeef5;
}

.divider span {
  padding: 0 15px;
}

/* 动画效果 */
.login-card,
.normal-login-button,
.feishu-login-button {
  transition: all 0.3s ease;
}

.error-message {
  margin: 15px 0;
}
</style> 
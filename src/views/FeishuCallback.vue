<template>
  <div class="feishu-callback-container">
    <div class="card" :class="{ mobile: isMobile }">
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>{{ isMobile ? '登录处理中...' : '飞书登录处理中，请稍候...' }}</p>
      </div>
      
      <div v-else-if="error" class="error">
        <el-alert
          :title="error"
          type="error"
          :closable="false"
          show-icon
          :class="{ mobile: isMobile }"
        />
        <el-button 
          type="primary" 
          @click="goToLogin" 
          class="mt-20"
          :class="{ mobile: isMobile }"
          size="large"
        >
          返回登录页
        </el-button>
        <div v-if="isMobile" class="mobile-hint">
          如遇问题，建议使用浏览器打开
        </div>
      </div>
      
      <div v-else class="success">
        <el-alert
          :title="isMobile ? '登录成功！' : '登录成功！正在跳转...'"
          type="success"
          :closable="false"
          show-icon
          :class="{ mobile: isMobile }"
        />
        <div v-if="isMobile" class="success-hint">
          正在为您跳转到系统首页...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { ElMessage } from 'element-plus';
import api from '../api';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const loading = ref(true);
const error = ref('');
const isMobile = ref(false);

// 检测移动端设备
const detectMobile = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  // 检测移动端设备
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  const isMobileDevice = mobileRegex.test(userAgent);
  
  // 检测屏幕尺寸
  const isSmallScreen = window.innerWidth <= 768;
  
  // 检测触摸支持
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  return isMobileDevice || (isSmallScreen && hasTouchSupport);
};

const goToLogin = () => {
  router.push('/login');
};

onMounted(async () => {
  // 检测移动端
  isMobile.value = detectMobile();
  
  // 获取URL参数中的code和state
  const { code, state } = route.query;
  
  if (!code) {
    loading.value = false;
    error.value = isMobile.value ? '登录失败，授权码无效' : '登录失败，未收到授权码';
    return;
  }
  
  try {
    // 验证state（防止CSRF攻击）
    const savedState = sessionStorage.getItem('feishu_state');
    if (savedState && state !== savedState) {
      error.value = isMobile.value ? '登录失败，请重新尝试' : '登录失败，状态参数不匹配';
      loading.value = false;
      return;
    }
    
    // 调用API进行登录
    console.log('准备调用飞书登录API，code:', code);
    const response = await api.post('/feishu/login', { code });
    console.log('飞书登录API响应:', response);
    
    // 登录成功，存储用户信息和令牌
    if (response && response.token && response.user) {
      authStore.token = response.token;
      authStore.user = response.user;
      
      // 存储令牌
      sessionStorage.setItem('token', response.token);
      
      // 显示成功消息
      const successMessage = isMobile.value ? '登录成功！' : '飞书登录成功';
      ElMessage.success(successMessage);
      
      // 跳转到首页或之前尝试访问的页面
      const jumpDelay = isMobile.value ? 2000 : 1500; // 移动端稍微延长一点时间
      setTimeout(() => {
        const redirectPath = route.query.redirect || '/';
        router.push(redirectPath);
      }, jumpDelay);
    } else {
      throw new Error('登录失败，服务器返回数据不完整: ' + JSON.stringify(response));
    }
  } catch (err) {
    console.error('飞书回调处理错误:', err);
    loading.value = false;
    
    // 移动端友好的错误信息
    if (isMobile.value) {
      if (err.message && err.message.includes('网络')) {
        error.value = '网络连接失败，请检查网络';
      } else if (err.message && err.message.includes('超时')) {
        error.value = '请求超时，请重新尝试';
      } else {
        error.value = '登录失败，请重新尝试';
      }
    } else {
      error.value = err.message || '飞书登录失败，请稍后再试';
    }
    
    ElMessage.error(error.value);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.feishu-callback-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
}

.card {
  width: 100%;
  max-width: 400px;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  text-align: center;
  position: relative;
}

.card.mobile {
  max-width: 320px;
  padding: 24px 20px;
  border-radius: 12px;
  margin: 0 auto;
}

.loading, .error, .success {
  padding: 20px 0;
}

.card.mobile .loading,
.card.mobile .error,
.card.mobile .success {
  padding: 16px 0;
}

.spinner {
  margin: 0 auto 20px;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3370ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.card.mobile .spinner {
  width: 36px;
  height: 36px;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading p {
  margin: 0;
  font-size: 16px;
  color: #606266;
  line-height: 1.4;
}

.card.mobile .loading p {
  font-size: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-20.mobile {
  margin-top: 24px;
  width: 100%;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  min-height: 44px;
}

.mobile-hint,
.success-hint {
  margin-top: 12px;
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

.card.mobile .mobile-hint,
.card.mobile .success-hint {
  font-size: 14px;
  margin-top: 16px;
}

/* Element Plus Alert 组件移动端适配 */
:deep(.el-alert.mobile) {
  padding: 12px 16px;
  font-size: 14px;
}

:deep(.el-alert.mobile .el-alert__title) {
  font-size: 15px;
  line-height: 1.4;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .feishu-callback-container {
    padding: 16px;
  }
  
  .card {
    max-width: 100%;
    padding: 24px 20px;
    border-radius: 12px;
  }
  
  .loading p {
    font-size: 15px;
  }
  
  .mt-20 {
    margin-top: 24px;
    width: 100%;
    padding: 12px 24px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .feishu-callback-container {
    padding: 12px;
  }
  
  .card {
    padding: 20px 16px;
  }
  
  .spinner {
    width: 32px;
    height: 32px;
  }
  
  .loading p {
    font-size: 14px;
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #333;
  }
}

/* 减少动画的用户偏好适配 */
@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
    border-top-color: #3370ff;
  }
}
</style> 
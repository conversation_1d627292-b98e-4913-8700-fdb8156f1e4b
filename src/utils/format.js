/**
 * 格式化日期
 * @param {string|Date} date - 日期字符串或Date对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * 格式化订单编号
 * 如果订单已有格式化的orderNumber则直接返回
 * 否则根据支付方式(ly/rmb)和订单ID生成标准格式的订单编号
 * 格式：GNB-YYYYMMDD00000001或RMB-YYYYMMDD00000001
 * 
 * @param {Object} order - 订单对象
 * @param {string} order.orderNumber - 订单编号(可选)
 * @param {string} order.paymentMethod - 支付方式(ly/rmb)
 * @param {number|string} order.id - 订单ID
 * @param {string|Date} [order.createdAt] - 订单创建时间(可选)
 * @returns {string} 格式化后的订单编号
 */
export function formatOrderNumber(order) {
  if (!order) return '';
  
  // 调试输出，查看传入的orderNumber
  console.log('formatOrderNumber - 订单信息:', 
    { id: order.id, orderNumber: order.orderNumber, paymentMethod: order.paymentMethod });
  
  // 如果已有orderNumber则直接返回
  if (order.orderNumber) {
    console.log('formatOrderNumber - 使用原始orderNumber:', order.orderNumber);
    return order.orderNumber;
  }
  
  // 创建日期
  const createdAt = order.createdAt ? new Date(order.createdAt) : new Date();
  const dateStr = createdAt.getFullYear().toString() + 
                 (createdAt.getMonth() + 1).toString().padStart(2, '0') + 
                 createdAt.getDate().toString().padStart(2, '0');
  
  // 根据支付方式确定前缀
  const prefix = order.paymentMethod === 'ly' ? 'GNB-' : 'RMB-';
  
  // 格式化ID为八位数字，前导零（与后端保持一致）
  const idStr = String(order.id).padStart(8, '0');
  
  const generatedOrderNumber = prefix + dateStr + idStr;
  console.log('formatOrderNumber - 生成新的orderNumber:', generatedOrderNumber);
  
  return generatedOrderNumber;
}

/**
 * 格式化手机号码 - 去除国际区号前缀
 * @param {string} mobile - 原始手机号码，可能包含"+86"等前缀
 * @returns {string} 格式化后的手机号码
 */
export function formatMobileNumber(mobile) {
  if (!mobile) return '';
  
  // 去除+86前缀
  if (mobile.startsWith('+86')) {
    return mobile.substring(3);
  }
  
  // 去除其他可能的国际区号前缀格式
  const mobileRegex = /^\+\d{1,4}([-\s])?(\d+)$/;
  const match = mobile.match(mobileRegex);
  if (match && match[2]) {
    return match[2];
  }
  
  // 如果没有前缀或格式无法识别，返回原始值
  return mobile;
} 
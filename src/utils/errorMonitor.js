/**
 * 安全的错误监控工具
 * 收集前端错误信息，但不影响用户体验
 */
class ErrorMonitor {
  constructor() {
    this.errors = [];
    this.maxErrors = 100; // 最多保存100个错误
    this.isEnabled = process.env.NODE_ENV === 'production'; // 只在生产环境启用
    this.init();
  }

  init() {
    if (!this.isEnabled) {
      return;
    }

    // 监听全局错误
    window.addEventListener('error', (event) => {
      this.recordError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
    });

    // 监听Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        type: 'promise',
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
    });

    // 监听资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.recordError({
          type: 'resource',
          message: `Failed to load ${event.target.tagName}: ${event.target.src || event.target.href}`,
          element: event.target.tagName,
          source: event.target.src || event.target.href,
          timestamp: Date.now(),
          url: window.location.href
        });
      }
    }, true);
  }

  recordError(errorInfo) {
    try {
      // 添加到错误列表
      this.errors.push(errorInfo);

      // 保持错误列表大小
      if (this.errors.length > this.maxErrors) {
        this.errors.shift();
      }

      // 在控制台输出（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.error('错误监控:', errorInfo);
      }

      // 可选：发送到后端（但不阻塞用户操作）
      this.sendErrorToBackend(errorInfo);
    } catch (error) {
      // 确保错误监控本身不会导致错误
      console.warn('错误监控记录失败:', error);
    }
  }

  sendErrorToBackend(errorInfo) {
    // 使用setTimeout确保不阻塞主线程
    setTimeout(() => {
      try {
        // 使用fetch发送错误信息，但不等待响应
        fetch('/api/errors/frontend', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(errorInfo)
        }).catch(() => {
          // 忽略发送失败，不影响用户体验
        });
      } catch (error) {
        // 忽略发送错误
      }
    }, 0);
  }

  // 手动记录错误
  logError(error, context = {}) {
    this.recordError({
      type: 'manual',
      message: error.message || String(error),
      stack: error.stack,
      context,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    });
  }

  // 获取错误统计
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      recent: this.errors.slice(-10) // 最近10个错误
    };

    this.errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    });

    return stats;
  }

  // 清除错误记录
  clearErrors() {
    this.errors = [];
  }
}

// 创建全局错误监控实例
const errorMonitor = new ErrorMonitor();

// 导出实例和手动记录方法
export default errorMonitor;
export const logError = (error, context) => errorMonitor.logError(error, context); 
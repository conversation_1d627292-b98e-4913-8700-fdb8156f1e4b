/**
 * 会话管理器
 * 实现功能：
 * 1. 刷新页面时保持登录状态
 * 2. 关闭浏览器后自动退出登录
 */

// 设置cookie
const setCookie = (name, value, expireDays = null) => {
  let expires = '';
  if (expireDays) {
    const date = new Date();
    date.setTime(date.getTime() + (expireDays * 24 * 60 * 60 * 1000));
    expires = `; expires=${date.toUTCString()}`;
  }
  document.cookie = `${name}=${value || ''}${expires}; path=/`;
};

// 获取cookie
const getCookie = (name) => {
  const nameEQ = `${name}=`;
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
};

// 删除cookie
const eraseCookie = (name) => {
  document.cookie = `${name}=; Max-Age=-99999999; path=/`;
};

// 生成唯一会话ID
const generateSessionId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// 会话管理
const sessionManager = {
  // 初始化会话
  init() {
    // 检查是否有会话ID
    const sessionId = getCookie('session_id');
    if (!sessionId) {
      // 没有会话ID表示是全新会话或浏览器曾经关闭
      // 清除localStorage中的token
      localStorage.removeItem('token');
      // 设置新的会话ID (仅在当前会话有效，不设置过期时间)
      setCookie('session_id', generateSessionId());
    }
  },

  // 清除会话
  clearSession() {
    eraseCookie('session_id');
    localStorage.removeItem('token');
  }
};

// 在页面加载时初始化会话
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    sessionManager.init();
  });
}

export default sessionManager; 
// 使用 Intersection Observer API 实现图片懒加载
let imageObserver = null;

export function observeImage(imgElement) {
  if (!imageObserver) {
    imageObserver = new IntersectionObserver(
      (entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            observer.unobserve(img);
          }
        });
      },
      {
        root: null,
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    );
  }
  imageObserver.observe(imgElement);
}

export function cleanupImageObserver() {
  if (imageObserver) {
    imageObserver.disconnect();
    imageObserver = null;
  }
}
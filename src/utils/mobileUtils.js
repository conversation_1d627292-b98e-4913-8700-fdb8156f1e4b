/**
 * 移动端工具函数
 * 提供设备检测、环境判断和移动端优化相关功能
 */

/**
 * 检测是否为移动端设备
 * @returns {boolean}
 */
export function isMobileDevice() {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  // 检测移动端设备
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  const isMobileUA = mobileRegex.test(userAgent);
  
  // 检测屏幕尺寸
  const isSmallScreen = window.innerWidth <= 768;
  
  // 检测触摸支持
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  return isMobileUA || (isSmallScreen && hasTouchSupport);
}

/**
 * 检测是否在微信环境中
 * @returns {boolean}
 */
export function isWeixinBrowser() {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('micromessenger');
}

/**
 * 检测是否在飞书App内
 * @returns {boolean}
 */
export function isFeishuApp() {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('lark') || userAgent.includes('feishu');
}

/**
 * 检测是否为iOS设备
 * @returns {boolean}
 */
export function isIOS() {
  const userAgent = navigator.userAgent;
  return /iPad|iPhone|iPod/.test(userAgent);
}

/**
 * 检测是否为Android设备
 * @returns {boolean}
 */
export function isAndroid() {
  const userAgent = navigator.userAgent;
  return /Android/.test(userAgent);
}

/**
 * 获取设备信息
 * @returns {object}
 */
export function getDeviceInfo() {
  return {
    isMobile: isMobileDevice(),
    isWeixin: isWeixinBrowser(),
    isFeishu: isFeishuApp(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    pixelRatio: window.devicePixelRatio || 1
  };
}

/**
 * 优化移动端弹窗参数
 * @param {string} url - 目标URL
 * @param {object} options - 弹窗选项
 * @returns {string}
 */
export function optimizePopupForMobile(url, options = {}) {
  const deviceInfo = getDeviceInfo();
  
  if (!deviceInfo.isMobile) {
    // 桌面端默认参数
    return {
      url,
      features: `width=${options.width || 800},height=${options.height || 600},scrollbars=yes,resizable=yes,status=yes,location=yes,toolbar=no,menubar=no`
    };
  }
  
  // 移动端优化
  const screenWidth = Math.min(window.innerWidth, 400);
  const screenHeight = Math.min(window.innerHeight, 600);
  
  return {
    url,
    features: `width=${screenWidth},height=${screenHeight},scrollbars=yes,resizable=no,status=no,location=yes,toolbar=no,menubar=no`
  };
}

/**
 * 移动端飞书登录策略
 * @returns {object}
 */
export function getFeishuLoginStrategy() {
  const deviceInfo = getDeviceInfo();
  
  // 在飞书App内
  if (deviceInfo.isFeishu) {
    return {
      method: 'same-window',
      reason: '飞书App内登录',
      tips: '在飞书App中登录'
    };
  }
  
  // 微信浏览器
  if (deviceInfo.isWeixin) {
    return {
      method: 'same-window',
      reason: '微信浏览器限制弹窗',
      tips: '建议复制链接到浏览器中打开'
    };
  }
  
  // 移动端浏览器
  if (deviceInfo.isMobile) {
    return {
      method: 'same-window',
      reason: '移动端浏览器优化',
      tips: '移动端登录体验优化'
    };
  }
  
  // 桌面端
  return {
    method: 'new-window',
    reason: '桌面端标准流程',
    tips: '在新窗口中完成登录'
  };
}

/**
 * 触摸反馈处理
 * @param {HTMLElement} element - 目标元素
 */
export function addTouchFeedback(element) {
  if (!isMobileDevice()) return;
  
  let touchStartTime = 0;
  
  element.addEventListener('touchstart', () => {
    touchStartTime = Date.now();
    element.style.transform = 'scale(0.98)';
    element.style.transition = 'transform 0.1s ease';
  }, { passive: true });
  
  element.addEventListener('touchend', () => {
    const touchDuration = Date.now() - touchStartTime;
    
    // 如果触摸时间很短，给予轻微反馈
    if (touchDuration < 150) {
      element.style.transform = 'scale(1.02)';
      setTimeout(() => {
        element.style.transform = 'scale(1)';
      }, 100);
    } else {
      element.style.transform = 'scale(1)';
    }
  }, { passive: true });
  
  element.addEventListener('touchcancel', () => {
    element.style.transform = 'scale(1)';
  }, { passive: true });
}

/**
 * 移动端优化的消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型
 * @param {object} options - 选项
 */
export function showMobileOptimizedMessage(message, type = 'info', options = {}) {
  const deviceInfo = getDeviceInfo();
  
  if (!deviceInfo.isMobile) {
    return { message, type, ...options };
  }
  
  // 移动端优化
  return {
    message: message.length > 20 ? message.substring(0, 17) + '...' : message,
    type,
    duration: type === 'error' ? 4000 : 2500, // 错误消息显示更久
    showClose: false,
    customClass: 'mobile-message',
    ...options
  };
}

/**
 * 防抖函数 - 移动端优化版本
 * @param {function} func - 要防抖的函数
 * @param {number} wait - 等待时间
 * @returns {function}
 */
export function mobileDebounce(func, wait = 300) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 移动端键盘处理
 */
export class MobileKeyboardHandler {
  constructor() {
    this.originalViewportHeight = window.innerHeight;
    this.isKeyboardOpen = false;
  }
  
  init() {
    if (!isMobileDevice()) return;
    
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // iOS特殊处理
    if (isIOS()) {
      document.addEventListener('focusin', this.handleFocusIn.bind(this));
      document.addEventListener('focusout', this.handleFocusOut.bind(this));
    }
  }
  
  handleResize() {
    const currentHeight = window.innerHeight;
    const heightDifference = this.originalViewportHeight - currentHeight;
    
    // 如果高度减少超过150px，认为键盘打开
    this.isKeyboardOpen = heightDifference > 150;
    
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('mobile-keyboard-toggle', {
      detail: { isOpen: this.isKeyboardOpen, heightDifference }
    }));
  }
  
  handleFocusIn(e) {
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
      document.body.classList.add('keyboard-open');
    }
  }
  
  handleFocusOut() {
    setTimeout(() => {
      document.body.classList.remove('keyboard-open');
    }, 100);
  }
  
  destroy() {
    window.removeEventListener('resize', this.handleResize.bind(this));
    if (isIOS()) {
      document.removeEventListener('focusin', this.handleFocusIn.bind(this));
      document.removeEventListener('focusout', this.handleFocusOut.bind(this));
    }
  }
} 
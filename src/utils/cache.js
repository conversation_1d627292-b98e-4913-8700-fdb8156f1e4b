/**
 * 安全的前端缓存工具
 * 提供内存缓存和localStorage缓存
 */
class SafeCache {
  constructor() {
    this.memoryCache = new Map();
    this.maxMemorySize = 50; // 最多缓存50个项目
    this.defaultTTL = 5 * 60 * 1000; // 默认5分钟过期
  }

  /**
   * 生成缓存键
   */
  generateKey(prefix, params) {
    if (typeof params === 'object') {
      return `${prefix}:${JSON.stringify(params)}`;
    }
    return `${prefix}:${params}`;
  }

  /**
   * 设置内存缓存
   */
  setMemory(key, data, ttl = this.defaultTTL) {
    // 如果缓存已满，删除最旧的项目
    if (this.memoryCache.size >= this.maxMemorySize) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }

    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 获取内存缓存
   */
  getMemory(key) {
    const cached = this.memoryCache.get(key);
    
    if (!cached) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.memoryCache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * 设置localStorage缓存（带错误处理）
   */
  setStorage(key, data, ttl = this.defaultTTL) {
    try {
      const item = {
        data,
        timestamp: Date.now(),
        ttl
      };
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.warn('localStorage设置失败:', error);
      // 降级到内存缓存
      this.setMemory(key, data, ttl);
    }
  }

  /**
   * 获取localStorage缓存（带错误处理）
   */
  getStorage(key) {
    try {
      const item = localStorage.getItem(key);
      if (!item) {
        return null;
      }

      const parsed = JSON.parse(item);
      
      // 检查是否过期
      if (Date.now() - parsed.timestamp > parsed.ttl) {
        localStorage.removeItem(key);
        return null;
      }

      return parsed.data;
    } catch (error) {
      console.warn('localStorage读取失败:', error);
      // 降级到内存缓存
      return this.getMemory(key);
    }
  }

  /**
   * 清除过期缓存
   */
  cleanup() {
    // 清理内存缓存
    for (const [key, value] of this.memoryCache.entries()) {
      if (Date.now() - value.timestamp > value.ttl) {
        this.memoryCache.delete(key);
      }
    }

    // 清理localStorage缓存
    try {
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('cache:')) {
          try {
            const item = JSON.parse(localStorage.getItem(key));
            if (Date.now() - item.timestamp > item.ttl) {
              keysToRemove.push(key);
            }
          } catch (error) {
            keysToRemove.push(key); // 删除损坏的缓存项
          }
        }
      }
      
      keysToRemove.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.warn('清理localStorage缓存失败:', error);
    }
  }

  /**
   * 清除所有缓存
   */
  clear() {
    this.memoryCache.clear();
    
    try {
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('cache:')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.warn('清理localStorage失败:', error);
    }
  }
}

// 创建全局缓存实例
const cache = new SafeCache();

// 定期清理过期缓存（每5分钟）
setInterval(() => {
  cache.cleanup();
}, 5 * 60 * 1000);

export default cache; 
/**
 * 图片URL处理工具函数
 * 用于处理不同环境下的图片URL，确保正确显示
 */

// 获取当前API基础URL
const getBaseUrl = () => {
  return import.meta.env.VITE_API_URL?.replace('/api', '') || '';
};

/**
 * 修复图片URL，确保能正确显示
 * 处理本地环境和生产环境的URL差异
 * 
 * @param {string} url - 原始图片URL
 * @returns {string} - 修复后的URL
 */
export const fixImageUrl = (url) => {
  if (!url) return '';
  
  // 已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // 处理native-resource格式
  if (url.startsWith('native-resource://')) {
    return url.replace('native-resource://', '/');
  }
  
  // 处理以localhost:3000开头的URL，替换为当前环境的baseUrl
  if (url.includes('localhost:3000')) {
    const baseUrl = getBaseUrl();
    return url.replace(/http:\/\/localhost:3000/g, baseUrl);
  }
  
  // 处理支付码图片地址
  if (url.includes('/uploads/payment/')) {
    const baseUrl = getBaseUrl();
    // 确保使用正确的基础URL
    return `${baseUrl}${url.startsWith('/') ? '' : '/'}${url.replace(/^(https?:\/\/[^\/]+)?/, '')}`;
  }
  
  // 处理相对路径，添加baseUrl
  if (url.startsWith('/')) {
    return `${getBaseUrl()}${url}`;
  }
  
  // 其他情况，添加/前缀
  return `/${url}`;
};

/**
 * 处理HTML内容中的图片URL
 * 
 * @param {string} html - HTML内容
 * @returns {string} - 处理后的HTML
 */
export const processHtmlImages = (html) => {
  if (!html) return '';
  
  try {
    // 替换所有img标签中的src属性
    return html.replace(/<img[^>]+src="([^"]+)"[^>]*>/g, (match, src) => {
      const fixedSrc = fixImageUrl(src);
      return match.replace(src, fixedSrc);
    });
  } catch (error) {
    console.error('处理HTML内容中的图片错误:', error);
    return html;
  }
};

export default {
  fixImageUrl,
  processHtmlImages
}; 
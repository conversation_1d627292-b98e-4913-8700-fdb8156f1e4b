import api from './index';

/**
 * 获取公告列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.limit - 每页数量，默认10
 * @param {string} params.type - 公告类型 (新品/促销/系统更新)
 * @param {string} params.status - 公告状态 (active/inactive)
 * @param {string} params.search - 搜索关键词
 * @param {string} params.sort - 排序方式 (newest/oldest/title-asc/title-desc)
 * @param {boolean} params.showAll - 是否显示所有公告（包括下线的公告）
 * @returns {Promise} - 返回公告列表和分页信息
 */
export const getAnnouncements = (params = {}) => {
  return api.get('/announcements', { params });
};

/**
 * 获取公告详情
 * @param {number} id - 公告ID
 * @returns {Promise} - 返回公告详情
 */
export const getAnnouncementById = (id) => {
  return api.get(`/announcements/${id}`);
};

/**
 * 创建公告(管理员)
 * @param {Object} announcementData - 公告数据
 * @param {string} announcementData.title - 公告标题
 * @param {string} announcementData.content - 公告内容 (JSON或纯文本)
 * @param {string} announcementData.contentHtml - 公告HTML内容 (可选，富文本编辑器输出)
 * @param {Array<string>} announcementData.imageUrls - 公告图片URL数组 (可选)
 * @param {string} announcementData.type - 公告类型 (新品/促销/系统更新)
 * @param {string} announcementData.status - 公告状态 (active/inactive)
 * @returns {Promise} - 返回创建的公告
 */
export const createAnnouncement = (announcementData) => {
  return api.post('/announcements', announcementData);
};

/**
 * 更新公告(管理员)
 * @param {number} id - 公告ID
 * @param {Object} announcementData - 更新的公告数据
 * @param {string} announcementData.title - 公告标题 (可选)
 * @param {string} announcementData.content - 公告内容 (可选，JSON或纯文本)
 * @param {string} announcementData.contentHtml - 公告HTML内容 (可选，富文本编辑器输出)
 * @param {Array<string>} announcementData.imageUrls - 公告图片URL数组 (可选)
 * @param {string} announcementData.type - 公告类型 (可选)
 * @returns {Promise} - 返回更新后的公告
 */
export const updateAnnouncement = (id, announcementData) => {
  return api.put(`/announcements/${id}`, announcementData);
};

/**
 * 更新公告状态(管理员)
 * @param {number} id - 公告ID
 * @param {string} status - 公告状态 ('active'/'inactive')
 * @returns {Promise} - 返回操作结果
 */
export const updateAnnouncementStatus = (id, status) => {
  return api.put(`/announcements/${id}/status`, { status });
};

/**
 * 删除公告(管理员)
 * @param {number} id - 公告ID
 * @returns {Promise} - 返回操作结果
 */
export const deleteAnnouncement = (id) => {
  return api.delete(`/announcements/${id}`);
};

/**
 * 上传公告图片
 * @param {FormData} formData - 包含图片文件的FormData对象
 * @returns {Promise} - 返回上传的图片信息
 */
export const uploadAnnouncementImage = (formData) => {
  return api.post('/upload/announcement-image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}; 
import api from './index';

/**
 * 用户登录
 * @param {Object} credentials - 登录凭证
 * @param {string} credentials.username - 姓名
 * @param {string} credentials.email - 邮箱
 * @param {string} credentials.password - 密码
 * @param {string} credentials.userType - 登录类型 (admin/user)
 * @param {boolean} credentials.rememberMe - 是否记住登录状态
 * @returns {Promise}
 */
export const login = (credentials) => {
  return api.post('/auth/login', credentials);
};

/**
 * 获取当前用户资料
 * @returns {Promise}
 */
export const getProfile = () => {
  return api.get('/auth/profile');
};

/**
 * 用户注册
 * @param {Object} userData - 用户数据
 * @param {string} userData.username - 姓名
 * @param {string} userData.email - 邮箱
 * @param {string} userData.mobile - 手机号码（可选）
 * @param {string} userData.department - 部门
 * @param {string} userData.workplace - 职场
 * @param {string} userData.password - 密码
 * @returns {Promise}
 */
export const register = (userData) => {
  return api.post('/auth/register', userData);
};

/**
 * 获取飞书登录URL
 * @returns {Promise}
 */
export const getFeishuLoginUrl = () => {
  return api.get('/feishu/login-url');
};

/**
 * 飞书登录
 * @param {Object} data - 飞书登录数据
 * @param {string} data.code - 飞书授权码
 * @returns {Promise}
 */
export const feishuLogin = (data) => {
  return api.post('/feishu/login', data);
};

/**
 * 刷新用户令牌
 * 用于更新令牌中的角色信息等
 * @returns {Promise}
 */
export const refreshToken = () => {
  return api.post('/auth/refresh-token');
}; 
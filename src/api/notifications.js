import api from './index';

/**
 * 获取当前用户的通知列表
 * @returns {Promise<Object>} 返回通知列表
 */
export function getNotifications() {
  return api.get('/notifications');
}

/**
 * 获取未读通知数量
 * @returns {Promise<Object>} 返回未读数量
 */
export function getUnreadCount() {
  return api.get('/notifications/unread-count');
}

/**
 * 标记通知为已读
 * @param {number} id - 通知ID
 * @returns {Promise<Object>} 返回操作结果
 */
export function markAsRead(id) {
  return api.put(`/notifications/${id}/read`);
}

/**
 * 标记所有通知为已读
 * @returns {Promise<Object>} 返回操作结果
 */
export function markAllAsRead() {
  return api.put('/notifications/read-all');
}

/**
 * 删除通知
 * @param {number} id - 通知ID
 * @returns {Promise<Object>} 返回操作结果
 */
export function deleteNotification(id) {
  return api.delete(`/notifications/${id}`);
} 
import api from './index';

/**
 * 提交用户反馈
 * @param {Object} feedbackData - 反馈数据
 * @param {string} feedbackData.type - 反馈类型 ('product', 'feature', 'bug', 'other')
 * @param {string} feedbackData.title - 反馈标题
 * @param {string} feedbackData.content - 反馈内容
 * @returns {Promise<Object>} - 响应对象
 */
export function submitFeedback(feedbackData) {
  return api.post('/feedback', feedbackData);
}

/**
 * 获取当前用户的反馈列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise<Object>} - 响应对象，包含反馈列表和分页信息
 */
export function getUserFeedbacks(params) {
  return api.get('/feedback/user', { params });
}

/**
 * 获取反馈详情
 * @param {string} id - 反馈ID
 * @returns {Promise<Object>} - 响应对象，包含反馈详情
 */
export function getFeedbackDetail(id) {
  return api.get(`/feedback/${id}`);
}

/**
 * 获取所有反馈列表 (仅管理员可用)
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 状态过滤 ('pending', 'processing', 'completed')
 * @param {string} params.type - 类型过滤 ('product', 'feature', 'bug', 'other')
 * @returns {Promise<Object>} - 响应对象，包含反馈列表和分页信息
 */
export function getAllFeedbacks(params) {
  return api.get('/feedback', { params });
}

/**
 * 更新反馈状态和回复 (仅管理员可用)
 * @param {string} id - 反馈ID
 * @param {Object} updateData - 更新数据
 * @param {string} updateData.status - 状态 ('pending', 'processing', 'completed')
 * @param {string} updateData.adminReply - 管理员回复
 * @returns {Promise<Object>} - 响应对象
 */
export function updateFeedback(id, updateData) {
  return api.put(`/feedback/${id}`, updateData);
} 
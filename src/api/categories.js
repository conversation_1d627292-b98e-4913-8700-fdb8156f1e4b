import api from './index';

/**
 * 获取所有分类
 * @returns {Promise} - 返回分类列表
 */
export const getCategories = () => {
  return api.get('/categories');
};

/**
 * 获取分类及其商品数量
 * @returns {Promise} - 返回分类列表及每个分类下的商品数量
 */
export const getCategoriesWithProductCount = () => {
  return api.get('/categories/with-product-count');
};

/**
 * 获取单个分类详情
 * @param {number} categoryId - 分类ID
 * @returns {Promise} - 返回分类详情
 */
export const getCategoryById = (categoryId) => {
  return api.get(`/categories/${categoryId}`);
};

/**
 * 创建新分类
 * @param {Object} categoryData - 分类数据，包含name、description、sortOrder
 * @returns {Promise} - 返回创建的分类
 */
export const createCategory = (categoryData) => {
  return api.post('/categories', categoryData);
};

/**
 * 更新分类
 * @param {number} categoryId - 分类ID
 * @param {Object} categoryData - 更新的分类数据
 * @returns {Promise} - 返回更新后的分类
 */
export const updateCategory = (categoryId, categoryData) => {
  return api.put(`/categories/${categoryId}`, categoryData);
};

/**
 * 删除分类
 * @param {number} categoryId - 分类ID
 * @returns {Promise} - 返回操作结果
 */
export const deleteCategory = (categoryId) => {
  return api.delete(`/categories/${categoryId}`);
}; 
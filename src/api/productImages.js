import api from './index';

/**
 * 获取商品的所有图片
 * @param {number} productId - 商品ID
 * @returns {Promise} - 返回图片列表
 */
export const getProductImages = (productId) => {
  return api.get(`/product-images/product/${productId}`);
};

/**
 * 添加商品图片（通过URL）
 * @param {number} productId - 商品ID
 * @param {Object} imageData - 图片数据，包含imageUrl和可选的sortOrder
 * @returns {Promise} - 返回添加的图片
 */
export const addProductImage = (productId, imageData) => {
  return api.post(`/product-images/product/${productId}`, imageData);
};

/**
 * 上传商品图片（文件上传）
 * @param {number} productId - 商品ID
 * @param {FormData} formData - 包含图片文件的FormData对象
 * @returns {Promise} - 返回上传的图片
 */
export const uploadProductImage = (productId, formData) => {
  return api.post(`/product-images/upload/product/${productId}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 更新商品图片
 * @param {number} imageId - 图片ID
 * @param {Object} imageData - 更新的图片数据，主要是sortOrder
 * @returns {Promise} - 返回更新后的图片
 */
export const updateProductImage = (imageId, imageData) => {
  return api.put(`/product-images/${imageId}`, imageData);
};

/**
 * 删除商品图片
 * @param {number} imageId - 图片ID
 * @returns {Promise} - 返回操作结果
 */
export const deleteProductImage = (imageId) => {
  return api.delete(`/product-images/${imageId}`);
}; 
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
import router from './router'
import App from './App.vue'
import sessionManager from './utils/sessionManager'

// 初始化会话管理器
sessionManager.init();

// 导入全局样式
import './styles/global.scss'

// 导入移动端优化样式
import './styles/mobile.css'

const app = createApp(App)
app.use(ElementPlus, {
  locale: zhCn
})
app.use(createPinia())
app.use(router)
app.mount('#app')
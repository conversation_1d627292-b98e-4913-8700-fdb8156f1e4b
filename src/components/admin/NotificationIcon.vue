<template>
  <el-tooltip content="系统通知" placement="bottom">
    <el-badge :value="unreadCount || ''" :hidden="!hasUnread" class="action-badge">
      <el-button type="text" class="action-btn" @click="showNotifications">
        <el-icon><Bell /></el-icon>
      </el-button>
    </el-badge>
  </el-tooltip>
  
  <!-- 通知抽屉 -->
  <el-drawer
    v-model="drawerVisible"
    title="系统通知"
    direction="rtl"
    size="350px"
    :destroy-on-close="false"
  >
    <template #header>
      <div class="notification-drawer-header">
        <h3>系统通知</h3>
        <el-button v-if="hasUnread" type="primary" link @click="handleReadAll">
          全部标为已读
        </el-button>
      </div>
    </template>

    <div class="notification-list">
      <el-empty v-if="notifications.length === 0" description="暂无通知" />
      
      <el-scrollbar v-else height="calc(100vh - 120px)">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-item"
          :class="{
            'unread': !notification.isRead,
            'alert-notification': notification.type === 'stock_alert'
          }"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-content">
            <div class="notification-header">
              <h4>
                <el-icon v-if="notification.type === 'exchange'" :size="14" class="notification-icon"><Document /></el-icon>
                <el-icon v-else-if="notification.type === 'feedback'" :size="14" class="notification-icon"><ChatLineRound /></el-icon>
                <el-icon v-else-if="notification.type === 'stock_alert'" :size="14" class="notification-icon warning-icon"><Warning /></el-icon>
                {{ notification.title }}
              </h4>
              <small>{{ formatDate(notification.createdAt) }}</small>
            </div>
            <p>{{ notification.content }}</p>
          </div>
          
          <div class="notification-actions">
            <el-button
              type="text"
              @click.stop="handleDelete(notification.id)"
              title="删除"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useNotificationStore } from '../../stores/notifications';
import { useAuthStore } from '../../stores/auth';
import { Bell, Delete, Document, ChatLineRound, Warning } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';

const router = useRouter();
const notificationStore = useNotificationStore();
const authStore = useAuthStore();
const drawerVisible = ref(false);
const pollTimer = ref(null); // 添加轮询计时器引用

// 获取通知数据
const notifications = computed(() => notificationStore.notifications);
const unreadCount = computed(() => notificationStore.unreadCount);
const hasUnread = computed(() => notificationStore.hasUnread);

// 加载通知数据
const loadNotifications = async () => {
  await notificationStore.fetchNotifications();
  await notificationStore.fetchUnreadCount();
};

// 定期轮询通知数量
const startPolling = () => {
  // 清除已有的计时器
  if (pollTimer.value) {
    clearInterval(pollTimer.value);
  }
  
  // 设置新的轮询计时器，每10秒刷新一次
  pollTimer.value = setInterval(async () => {
    await notificationStore.fetchUnreadCount(true);
  }, 10000);
};

// 停止轮询
const stopPolling = () => {
  if (pollTimer.value) {
    clearInterval(pollTimer.value);
    pollTimer.value = null;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 显示通知抽屉
const showNotifications = async () => {
  drawerVisible.value = true;
  await loadNotifications();
};

// 处理点击通知
const handleNotificationClick = async (notification) => {
  // 标记为已读
  if (!notification.isRead) {
    await notificationStore.readNotification(notification.id);
  }
  
  // 根据通知类型跳转
  if (notification.type === 'exchange') {
    router.push(`/admin/exchanges?id=${notification.sourceId}`);
  } else if (notification.type === 'feedback') {
    router.push(`/admin/feedbacks?id=${notification.sourceId}`);
  } else if (notification.type === 'stock_alert') {
    // 库存告警通知跳转到商品管理页面，带上商品ID
    router.push(`/admin/products?lowStock=true&id=${notification.sourceId}`);
  }
  
  drawerVisible.value = false;
};

// 处理删除通知
const handleDelete = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除此通知吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    await notificationStore.removeNotification(id);
  } catch (error) {
    // 用户取消删除，不做处理
  }
};

// 标记所有为已读
const handleReadAll = async () => {
  await notificationStore.readAllNotifications();
};

// 组件挂载时获取未读数量并开始轮询
onMounted(async () => {
  // 只有在用户已登录时才开始轮询
  if (authStore.isAuthenticated) {
    // 强制更新通知计数
    await notificationStore.fetchUnreadCount(true);
    
    // 如果有未读通知，预加载通知内容
    if (notificationStore.unreadCount > 0) {
      await notificationStore.fetchNotifications();
    }
    
    // 开始定时轮询
    startPolling();
  }
});

// 监听用户登录状态变化
watch(() => authStore.isAuthenticated, (newVal) => {
  if (newVal) {
    // 用户登录后开始轮询
    notificationStore.fetchUnreadCount(true).then(() => {
      if (notificationStore.unreadCount > 0) {
        notificationStore.fetchNotifications();
      }
    });
    startPolling();
  } else {
    // 用户登出后停止轮询
    stopPolling();
  }
});

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling();
});

// 当抽屉关闭时刷新未读数量
watch(drawerVisible, (newVal) => {
  if (!newVal) {
    notificationStore.fetchUnreadCount();
  }
});
</script>

<style scoped>
.notification-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.notification-drawer-header h3 {
  margin: 0;
}

.notification-list {
  padding: 10px;
}

.notification-item {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: background-color 0.3s;
  border: 1px solid #ebeef5;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.notification-item.alert-notification {
  background-color: #fef4e8;
  border-left: 3px solid #e6a23c;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.notification-header h4 {
  margin: 0;
  font-size: 15px;
}

.notification-header small {
  color: #909399;
  font-size: 12px;
}

.notification-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  word-break: break-word;
}

.notification-actions {
  display: flex;
  opacity: 0.6;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.notification-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.warning-icon {
  color: #e6a23c;
}
</style> 
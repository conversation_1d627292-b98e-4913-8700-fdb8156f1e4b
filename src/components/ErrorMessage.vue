<template>
  <div v-if="visible" class="error-container" :class="typeClass">
    <div class="error-icon">
      <i class="el-icon-warning" v-if="type === 'warning'"></i>
      <i class="el-icon-error" v-else-if="type === 'error'"></i>
      <i class="el-icon-info" v-else></i>
    </div>
    <div class="error-content">
      <h3 class="error-title">{{ title }}</h3>
      <p class="error-message">{{ message }}</p>
      <p v-if="details" class="error-details">{{ details }}</p>
      <div class="error-actions" v-if="showActions">
        <slot name="actions">
          <el-button type="primary" size="small" @click="$emit('retry')" v-if="showRetry">
            重试
          </el-button>
          <el-button size="small" @click="$emit('close')" v-if="showClose">
            关闭
          </el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 错误类型: 'error', 'warning', 'info'
  type: {
    type: String,
    default: 'error'
  },
  // 标题
  title: {
    type: String,
    default: '出错了'
  },
  // 主要错误信息
  message: {
    type: String,
    required: true
  },
  // 详细错误信息
  details: {
    type: String,
    default: ''
  },
  // 是否显示
  visible: {
    type: Boolean,
    default: true
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 是否显示重试按钮
  showRetry: {
    type: Boolean,
    default: true
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true
  }
});

// 计算不同类型的样式类
const typeClass = computed(() => {
  return `error-${props.type}`;
});

// 定义事件
defineEmits(['retry', 'close']);
</script>

<style scoped>
.error-container {
  display: flex;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.error-error {
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
}

.error-warning {
  background-color: #fdf6ec;
  border: 1px solid #faecd8;
}

.error-info {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

.error-icon {
  font-size: 24px;
  margin-right: 16px;
}

.error-error .error-icon {
  color: #f56c6c;
}

.error-warning .error-icon {
  color: #e6a23c;
}

.error-info .error-icon {
  color: #67c23a;
}

.error-content {
  flex: 1;
}

.error-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.error-message {
  margin: 0 0 8px 0;
  color: #606266;
}

.error-details {
  margin: 0 0 16px 0;
  color: #909399;
  font-size: 14px;
}

.error-actions {
  margin-top: 16px;
}
</style> 
<template>
  <el-dialog
    :model-value="dialogVisible"
    @update:model-value="(val) => $emit('update:dialogVisible', val)"
    :title="newProducts.length === 1 ? '新品上新-' + newProducts[0].name : '新品上新'"
    :width="window.innerWidth <= 768 ? '90%' : (newProducts.length === 1 ? '60%' : '80%')"
    center
    :show-close="true"
    @closed="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    transition="dialog-fade"
    :duration="300"
    class="dialog-common"
  >
    <div v-if="newProducts.length > 0" class="new-products-container">
      <div v-for="product in newProducts" :key="product.originalIndex" class="content-wrapper">
        <div class="left-section">
          <div class="product-image">
            <el-carousel :height="window.innerWidth <= 768 ? '300px' : '400px'" :interval="3000" arrow="hover" indicator-position="outside">
              <el-carousel-item v-for="(image, imageIndex) in product.images" :key="imageIndex">
                <img :src="image" :alt="product.name" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.3s ease;" @click="$emit('preview-images', product.images, imageIndex)">
              </el-carousel-item>
            </el-carousel>
            <div class="new-tag">新品</div>
          </div>
        </div>
        <div class="right-section">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="product-info">
            <div class="ly-price">光年币：{{ product.lyPrice }}个</div>
            <div class="rmb-price">人民币：{{ product.rmbPrice }}元</div>
            <div class="stock" :class="{ 'stock-out': product.stock === 0, 'stock-in': product.stock > 0 }">
              库存状态：{{ product.stock > 0 ? '有货' : '缺货' }}
            </div>
            <div class="category">类别：{{ product.category }}</div>
            <div class="description">商品介绍：{{ product.description || '暂无介绍' }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <el-button type="primary" @click="handleClose">确定</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, onMounted, onUnmounted, ref } from 'vue'

const window = ref(globalThis.window)

onMounted(() => {
  window.value = globalThis.window
  window.value.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.value.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  window.value = globalThis.window
}

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    required: true
  },
  newProducts: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update:dialogVisible', 'closed'])

const handleClose = () => {
  emit('update:dialogVisible', false)
  // 等待对话框关闭动画完成后再触发closed事件
  setTimeout(() => {
    emit('closed')
  }, 300)
}
</script>

<style scoped>
.new-products-container {
  padding: 20px;
}

.content-wrapper {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 16px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  align-items: stretch;
}

@media screen and (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
    padding: 10px;
    align-items: center;
  }

  .left-section,
  .right-section {
    flex: 1;
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .right-section {
    padding: 8px;
    text-align: center;
  }

  .product-image {
    width: 100%;
    margin: 0 auto;
    border-radius: 12px;
    overflow: hidden;
  }
  
  :deep(.el-carousel__container) {
    height: 300px !important;
  }

  .product-info {
    font-size: 14px;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10px;
  }
  
  .product-info > div {
    width: 100%;
    box-sizing: border-box;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-all;
    text-align: left;
    justify-content: flex-start;
  }
  
  .product-name {
    width: 100%;
    text-align: center;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }
}

.content-wrapper:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.left-section {
  flex: 0 0 45%;
  display: flex;
  flex-direction: column;
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 12px;
  gap: 16px;
}

.product-image {
  position: relative;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  width: 100%;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-image img:hover {
  transform: scale(1.05);
}

.product-info {
  display: grid;
  grid-template-areas:
    "ly-price ly-price"
    "rmb-price stock"
    "category category"
    "description description";
  gap: 10px;
  margin-top: 12px;
}

@media screen and (max-width: 768px) {
  .product-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-top: 12px;
    width: 100%;
  }
  
  .product-info > div {
    width: 90%;
    margin: 0 auto;
  }
  
  .product-info .description {
    width: 90%;
    margin: 8px auto 0;
    min-height: 100px;
    text-align: left;
    justify-content: flex-start;
  }
}

.product-info > div {
  margin: 0;
  padding: 10px 12px;
  border-radius: 10px;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
  box-sizing: border-box;
  width: 100%;
}

.product-info .description {
  grid-area: description;
  color: #333;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border: 2px solid #e9ecef;
  padding: 16px;
  font-size: 15px;
  line-height: 1.6;
  border-radius: 12px;
  white-space: pre-wrap;
  margin-top: 8px;
  min-height: 120px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
  text-align: left;
  justify-content: flex-start;
}

.product-info .description:hover {
  border-color: #1a73e8;
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.1);
}
.product-info .ly-price {
  grid-area: ly-price;
  color: #ff4d4f;
  background: linear-gradient(135deg, #fff5f5, #fff1f0);
  border: 2px solid #ffccc7;
  font-size: 20px;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.product-info .rmb-price {
  grid-area: rmb-price;
  color: #52c41a;
  background: linear-gradient(135deg, #f9ffe6, #f6ffed);
  border: 2px solid #b7eb8f;
}

.product-info .stock {
  grid-area: stock;
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
}

.stock-out {
  background-color: #fef0f0;
  color: #f56c6c;
}

.stock-in {
  background-color: #f0f9eb;
  color: #67c23a;
}

.product-info .category {
  grid-area: category;
  color: #1a73e8;
  background: linear-gradient(135deg, #f0f7ff, #e8f0fe);
  border: 2px solid #4285f4;
}

.new-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(45deg, #40c057, #69db7c);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1;
}

.dialog-footer {
  text-align: center;
  margin-top: 20px;
}

:deep(.el-carousel__container) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.el-carousel__item) {
  border-radius: 16px;
}

:deep(.el-dialog) {
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #1a73e8, #34a853);
  margin: 0;
  padding: 32px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

:deep(.el-dialog__header)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

:deep(.el-dialog__title) {
  font-size: 32px;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  padding: 0 24px;
}

:deep(.el-dialog__title)::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  border-radius: 2px;
}

:deep(.el-dialog__body) {
  padding: 32px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  .product-info .description {
    grid-area: description;
    color: #333;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 2px solid #e9ecef;
    padding: 16px;
    font-size: 15px;
    line-height: 1.6;
    border-radius: 12px;
    white-space: pre-wrap;
    margin-top: 8px;
  }
}

.dialog-footer {
  text-align: center;
  margin-top: 20px;
}

.dialog-footer .el-button {
  width: 240px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #1a73e8, #4285f4);
  border: none;
  color: white;
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.2);
}

.dialog-footer .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(26, 115, 232, 0.3);
}
.product-name {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin: 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #ebeef5;
  position: relative;
}

.product-name::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #1a73e8, #4285f4);
}
</style>
<template>
  <div>
    <div v-if="error" class="error-container">
      <h2>抱歉，出现了一些问题</h2>
      <p>{{ error.message }}</p>
      <el-button type="primary" @click="handleReset">刷新页面</el-button>
    </div>
    <slot v-else></slot>
  </div>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'
import { ElMessage } from 'element-plus'
const error = ref(null)

onErrorCaptured((err, instance, info) => {
  error.value = err
  ElMessage.error('组件渲染出错，请刷新页面重试')
  console.error('错误详情:', { err, instance, info })
  return false
})

const handleReset = () => {
  error.value = null
  window.location.reload()
}
</script>

<style scoped>
.error-container {
  text-align: center;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  max-width: 600px;
}

.error-container h2 {
  color: #f56c6c;
  margin-bottom: 20px;
}

.error-container p {
  color: #666;
  margin-bottom: 20px;
}
</style>
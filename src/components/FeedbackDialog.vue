<template>
  <div class="feedback-dialog">
    <el-dialog
      v-model="dialogVisible"
      title="意见反馈"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="resetForm"
    >
      <el-form
        ref="feedbackFormRef"
        :model="feedbackForm"
        :rules="rules"
        label-position="top"
        class="feedback-form"
      >
        <el-form-item label="反馈类型" prop="type">
          <el-select v-model="feedbackForm.type" placeholder="请选择反馈类型" class="w-full">
            <el-option label="商品相关" value="product" />
            <el-option label="功能建议" value="feature" />
            <el-option label="问题反馈" value="bug" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="标题" prop="title">
          <el-input v-model="feedbackForm.title" placeholder="请输入反馈标题" />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="feedbackForm.content"
            type="textarea"
            placeholder="请详细描述您的反馈内容..."
            :rows="5"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitFeedback" :loading="submitting">
            提交反馈
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { submitFeedback as apiSubmitFeedback } from '../api/feedback';
import { useAuthStore } from '../stores/auth';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'submitted']);

const authStore = useAuthStore();
const isAuthenticated = computed(() => authStore.isAuthenticated);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const feedbackFormRef = ref(null);
const submitting = ref(false);

const feedbackForm = ref({
  type: '',
  title: '',
  content: ''
});

const rules = {
  type: [
    { required: true, message: '请选择反馈类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入反馈标题', trigger: 'blur' },
    { min: 3, max: 50, message: '标题长度应在3-50个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入反馈内容', trigger: 'blur' },
    { min: 10, max: 500, message: '内容长度应在10-500个字符之间', trigger: 'blur' }
  ]
};

const handleSubmitFeedback = async () => {
  if (!isAuthenticated.value) {
    ElMessage.warning('请先登录后再提交反馈');
    dialogVisible.value = false;
    return;
  }
  
  await feedbackFormRef.value.validate(async (valid) => {
    if (!valid) {
      return false;
    }
    
    try {
      submitting.value = true;
      await apiSubmitFeedback(feedbackForm.value);
      
      ElMessage.success('反馈提交成功，感谢您的宝贵意见！');
      dialogVisible.value = false;
      emit('submitted');
      
    } catch (error) {
      console.error('提交反馈失败:', error);
      ElMessage.error('提交反馈失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

const resetForm = () => {
  if (feedbackFormRef.value) {
    feedbackFormRef.value.resetFields();
  }
  
  feedbackForm.value = {
    type: '',
    title: '',
    content: ''
  };
};
</script>

<style scoped>
.feedback-form {
  margin-top: 10px;
}

.w-full {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
</style> 
<template>
  <div class="product-filter">
    <!-- 基础筛选 -->
    <div class="filter-basic">
      <!-- 搜索框 -->
      <el-input
        v-model="searchQuery"
        placeholder="搜索商品名称"
        clearable
        @input="debounceEmitChange"
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <!-- 分类选择 -->
      <el-select
        v-model="selectedCategory"
        placeholder="选择分类"
        clearable
        @change="handleCategoryChange"
        class="category-select"
      >
        <el-option
          v-for="category in categories"
          :key="category.id"
          :label="category.name"
          :value="category.id"
        />
      </el-select>

      <!-- 排序选择 -->
      <el-select
        v-model="sortOrder"
        placeholder="排序方式"
        clearable
        @change="handleSortChange"
        class="sort-select"
      >
        <el-option label="默认排序" value="default" />
        <el-option label="最新添加" value="newest" />
        <el-option label="光年币价格: 低到高" value="ly-price-asc" />
        <el-option label="光年币价格: 高到低" value="ly-price-desc" />
        <el-option label="人民币价格: 低到高" value="rmb-price-asc" />
        <el-option label="人民币价格: 高到低" value="rmb-price-desc" />
        <el-option label="名称: A-Z" value="name-asc" />
        <el-option label="名称: Z-A" value="name-desc" />
      </el-select>

      <!-- 按钮组 -->
      <div class="filter-button-group">
        <!-- 高级筛选按钮 -->
        <el-button 
          type="info" 
          plain 
          @click="showAdvanced = !showAdvanced"
          class="advanced-button"
        >
          {{ showAdvanced ? '收起高级筛选' : '高级筛选' }}
          <el-icon class="el-icon--right">
            <component :is="showAdvanced ? 'ArrowUp' : 'ArrowDown'" />
          </el-icon>
        </el-button>
        
        <div class="feedback-announcement-group">
          <!-- 意见反馈按钮 -->
          <el-tooltip content="意见反馈" placement="top">
            <el-button 
              type="primary" 
              @click="handleFeedback"
              class="feedback-button"
              :icon="ChatDotRound"
              circle
            />
          </el-tooltip>

          <!-- 公告按钮 -->
          <el-tooltip content="公告" placement="top">
            <el-button 
              type="warning" 
              @click="handleAnnouncement"
              class="announcement-button"
              :icon="Bell"
              circle
            />
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 高级筛选选项 -->
    <el-collapse-transition>
      <div v-show="showAdvanced" class="filter-advanced">
        <el-divider content-position="left">价格范围</el-divider>
        
        <div class="price-range">
          <div class="price-group">
            <span class="price-label">光年币价格</span>
            <div class="price-inputs">
              <el-slider
                v-model="lyPriceRange"
                range
                :min="priceRanges.minLyPrice"
                :max="priceRanges.maxLyPrice"
                :show-input="true"
                :show-stops="false"
                :show-tooltip="true"
                @change="handleLySliderChange"
              />
            </div>
          </div>
          
          <div class="price-group">
            <span class="price-label">人民币价格</span>
            <div class="price-inputs">
              <el-slider
                v-model="rmbPriceRange"
                range
                :min="priceRanges.minRmbPrice"
                :max="priceRanges.maxRmbPrice"
                :show-input="true"
                :show-stops="false"
                :show-tooltip="true"
                :format-tooltip="formatRmbTooltip"
                @change="handleRmbSliderChange"
              />
            </div>
          </div>
        </div>
        
        <el-divider content-position="left">商品属性</el-divider>
        
        <div class="filter-tags">
          <el-checkbox v-model="inStock" @change="emitChange">有库存</el-checkbox>
          <el-checkbox v-model="isNew" @change="emitChange">新品</el-checkbox>
          <el-checkbox v-model="isHot" @change="emitChange">热门</el-checkbox>
        </div>
        
        <el-divider content-position="left">上架日期</el-divider>
        
        <div class="date-range">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </div>
        
        <div class="filter-actions">
          <el-button type="primary" @click="applyFilters">应用筛选</el-button>
          <el-button type="info" plain @click="resetFilters">重置筛选</el-button>
        </div>
        
        <!-- 已激活筛选条件 -->
        <div v-if="Object.keys(activeFilters).length > 0" class="active-filters">
          <div class="active-filters-title">已选条件:</div>
          
          <el-tag 
            v-if="activeFilters.search" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('search')"
          >
            关键词: {{ activeFilters.search }}
          </el-tag>
          
          <el-tag 
            v-if="activeFilters.category" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('category')"
          >
            分类: {{ getCategoryName(activeFilters.category) }}
          </el-tag>
          
          <el-tag 
            v-if="activeFilters.minLyPrice !== null || activeFilters.maxLyPrice !== null" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('minLyPrice')"
          >
            光年币: {{ activeFilters.minLyPrice || 0 }} - {{ activeFilters.maxLyPrice || '∞' }}
          </el-tag>
          
          <el-tag 
            v-if="activeFilters.minRmbPrice !== null || activeFilters.maxRmbPrice !== null" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('minRmbPrice')"
          >
            人民币: ¥{{ activeFilters.minRmbPrice || 0 }} - ¥{{ activeFilters.maxRmbPrice || '∞' }}
          </el-tag>
          
          <el-tag 
            v-if="activeFilters.inStock" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('inStock')"
          >
            有库存
          </el-tag>
          
          <el-tag 
            v-if="activeFilters.isNew" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('isNew')"
          >
            新品
          </el-tag>
          
          <el-tag 
            v-if="activeFilters.isHot" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('isHot')"
          >
            热门
          </el-tag>
          
          <el-tag 
            v-if="activeFilters.dateRange" 
            class="active-filter-tag" 
            closable 
            @close="removeFilter('dateRange')"
          >
            上架日期: {{ formatDate(activeFilters.dateRange[0]) }} 至 {{ formatDate(activeFilters.dateRange[1]) }}
          </el-tag>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, ArrowDown, ArrowUp, Filter, ChatDotRound, Bell } from '@element-plus/icons-vue';
import { getCategories } from '../api/categories';
import { getProductPriceRanges } from '../api/products';
import { debounce } from 'lodash-es';

// 定义组件props
const props = defineProps({
  initialFilters: Object,
  categoriesList: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits([
  'change', 
  'reset', 
  'feedback', 
  'announcement'
]);

// 筛选状态
const searchQuery = ref('');
const selectedCategory = ref('');
const sortOrder = ref('');
const minLyPrice = ref(null);
const maxLyPrice = ref(null);
const minRmbPrice = ref(null);
const maxRmbPrice = ref(null);
const inStock = ref(false);
const isNew = ref(false);
const isHot = ref(false);
const dateRange = ref(null);
const showAdvanced = ref(false);

// 价格范围滑块的数据模型
const lyPriceRange = ref([0, 100]);
const rmbPriceRange = ref([0, 100]);

// 价格范围数据
const priceRanges = ref({
  minLyPrice: 0,
  maxLyPrice: 100,
  minRmbPrice: 0,
  maxRmbPrice: 100
});

// 分类数据
const categories = ref(props.categoriesList);

// 已激活的筛选条件
const activeFilters = ref({});

// 格式化人民币价格提示
const formatRmbTooltip = (val) => {
  return `¥${val.toFixed(2)}`;
};

// 处理光年币滑块变化
const handleLySliderChange = (values) => {
  minLyPrice.value = values[0];
  maxLyPrice.value = values[1];
  emitChange();
};

// 处理人民币滑块变化
const handleRmbSliderChange = (values) => {
  minRmbPrice.value = values[0];
  maxRmbPrice.value = values[1];
  emitChange();
};

// 初始化筛选器
const initializeFilters = () => {
  if (props.initialFilters) {
    searchQuery.value = props.initialFilters.search || '';
    selectedCategory.value = props.initialFilters.category || '';
    sortOrder.value = props.initialFilters.sort || '';
    minLyPrice.value = props.initialFilters.minLyPrice || null;
    maxLyPrice.value = props.initialFilters.maxLyPrice || null;
    minRmbPrice.value = props.initialFilters.minRmbPrice || null;
    maxRmbPrice.value = props.initialFilters.maxRmbPrice || null;
    inStock.value = props.initialFilters.inStock || false;
    isNew.value = props.initialFilters.isNew || false;
    isHot.value = props.initialFilters.isHot || false;
    
    if (props.initialFilters.startDate && props.initialFilters.endDate) {
      dateRange.value = [
        new Date(props.initialFilters.startDate),
        new Date(props.initialFilters.endDate)
      ];
    }

    // 更新滑块值
    updateSliderValues();
  }
};

// 获取价格范围
const fetchPriceRanges = async () => {
  try {
    const params = {};
    // 如果已选择了分类，传递分类参数以获取更精确的价格范围
    if (selectedCategory.value) {
      params.category = selectedCategory.value;
    }
    // 如果有搜索关键词，传递搜索参数
    if (searchQuery.value) {
      params.search = searchQuery.value;
    }
    
    const response = await getProductPriceRanges(params);
    priceRanges.value = response;
    
    // 更新滑块值
    updateSliderValues();
  } catch (error) {
    console.error('获取价格范围失败:', error);
    ElMessage.error('获取价格范围数据失败');
  }
};

// 更新滑块值
const updateSliderValues = () => {
  // 光年币价格滑块初始化
  if (minLyPrice.value !== null && maxLyPrice.value !== null) {
    lyPriceRange.value = [minLyPrice.value, maxLyPrice.value];
  } else {
    lyPriceRange.value = [priceRanges.value.minLyPrice, priceRanges.value.maxLyPrice];
  }
  
  // 人民币价格滑块初始化
  if (minRmbPrice.value !== null && maxRmbPrice.value !== null) {
    rmbPriceRange.value = [minRmbPrice.value, maxRmbPrice.value];
  } else {
    rmbPriceRange.value = [priceRanges.value.minRmbPrice, priceRanges.value.maxRmbPrice];
  }
};

// 更新活跃的筛选条件
const updateActiveFilters = () => {
  const filters = {};
  
  if (searchQuery.value) filters.search = searchQuery.value;
  if (selectedCategory.value) filters.category = selectedCategory.value;
  if (sortOrder.value) filters.sort = sortOrder.value;
  if (minLyPrice.value !== null) filters.minLyPrice = minLyPrice.value;
  if (maxLyPrice.value !== null) filters.maxLyPrice = maxLyPrice.value;
  if (minRmbPrice.value !== null) filters.minRmbPrice = minRmbPrice.value;
  if (maxRmbPrice.value !== null) filters.maxRmbPrice = maxRmbPrice.value;
  if (inStock.value) filters.inStock = inStock.value;
  if (isNew.value) filters.isNew = isNew.value;
  if (isHot.value) filters.isHot = isHot.value;
  if (dateRange.value && dateRange.value.length === 2) {
    filters.dateRange = dateRange.value;
  }
  
  activeFilters.value = filters;
};

// 删除某个筛选条件
const removeFilter = (key) => {
  switch (key) {
    case 'search':
      searchQuery.value = '';
      break;
    case 'category':
      selectedCategory.value = '';
      break;
    case 'sort':
      sortOrder.value = '';
      break;
    case 'minLyPrice':
    case 'maxLyPrice':
      minLyPrice.value = null;
      maxLyPrice.value = null;
      updateSliderValues();
      break;
    case 'minRmbPrice':
    case 'maxRmbPrice':
      minRmbPrice.value = null;
      maxRmbPrice.value = null;
      updateSliderValues();
      break;
    case 'inStock':
      inStock.value = false;
      break;
    case 'isNew':
      isNew.value = false;
      break;
    case 'isHot':
      isHot.value = false;
      break;
    case 'dateRange':
      dateRange.value = null;
      break;
  }
  
  emitChange();
};

// 初始化
initializeFilters();

// 如果没有传入categories，则从API获取
const fetchCategories = async () => {
  if (categories.value.length === 0) {
    try {
      const response = await getCategories();
      categories.value = response || [];
    } catch (error) {
      console.error('获取分类失败:', error);
      ElMessage.error('获取分类数据失败');
    }
  }
};

// 节流后的筛选变更通知
const debounceEmitChange = debounce(() => {
  emitChange();
}, 300);

// 发射筛选变更事件
const emitChange = () => {
  updateActiveFilters();
  
  const filters = {
    search: searchQuery.value,
    category: selectedCategory.value,
    sort: sortOrder.value,
    minLyPrice: minLyPrice.value,
    maxLyPrice: maxLyPrice.value,
    minRmbPrice: minRmbPrice.value,
    maxRmbPrice: maxRmbPrice.value,
    inStock: inStock.value,
    isNew: isNew.value,
    isHot: isHot.value
  };
  
  // 添加日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    filters.startDate = dateRange.value[0].toISOString().split('T')[0];
    filters.endDate = dateRange.value[1].toISOString().split('T')[0];
  }
  
  console.log('ProductFilter发射筛选变更:', {
    sortOrder: sortOrder.value,
    filters
  });
  
  emit('change', filters);
};

// 应用筛选
const applyFilters = () => {
  emitChange();
};

// 处理日期变化
const handleDateChange = () => {
  emitChange();
};

// 重置筛选
const resetFilters = () => {
  searchQuery.value = '';
  selectedCategory.value = '';
  sortOrder.value = '';
  minLyPrice.value = null;
  maxLyPrice.value = null;
  minRmbPrice.value = null;
  maxRmbPrice.value = null;
  inStock.value = false;
  isNew.value = false;
  isHot.value = false;
  dateRange.value = null;
  
  // 重置滑块值
  updateSliderValues();
  
  emit('reset');
};

// 处理意见反馈
const handleFeedback = () => {
  emit('feedback');
};

// 处理公告
const handleAnnouncement = () => {
  emit('announcement');
};

// 处理排序变化 - 特殊处理以确保排序状态正确
const handleSortChange = (value) => {
  console.log('排序选择器变化:', { 
    oldValue: sortOrder.value, 
    newValue: value 
  });
  
  // 确保值已经更新
  sortOrder.value = value;
  
  // 立即触发变更事件
  emitChange();
};

// 处理分类变化
const handleCategoryChange = (value) => {
  console.log('分类选择器变化:', { 
    oldValue: selectedCategory.value, 
    newValue: value 
  });
  
  // 确保值已经更新
  selectedCategory.value = value;
  
  // 立即触发变更事件
  emitChange();
};

// 监听props变化
watch(() => props.initialFilters, initializeFilters);

// 监听分类变化，重新获取价格范围
watch(() => selectedCategory.value, () => {
  fetchPriceRanges();
});

// 监听搜索词变化，重新获取价格范围
watch(() => searchQuery.value, debounce(() => {
  fetchPriceRanges();
}, 500));

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories();
  fetchPriceRanges();
});

// 获取分类名称
const getCategoryName = (categoryId) => {
  const category = categories.value.find(c => c.id === categoryId);
  return category ? category.name : categoryId;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};
</script>

<style scoped>
.product-filter {
  margin-bottom: 24px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.filter-basic {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-input {
  width: 240px;
}

.category-select,
.sort-select {
  width: 180px;
}

.filter-button-group {
  display: flex;
  margin-left: auto;
  gap: 12px;
  align-items: center;
}

.feedback-announcement-group {
  display: flex;
  gap: 8px;
}

.filter-advanced {
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.price-range {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin: 16px 0;
}

.price-group {
  flex: 1;
  min-width: 280px;
}

.price-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.separator {
  color: #909399;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 16px 0;
}

.date-range {
  margin: 16px 0;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  justify-content: flex-end;
}

/* 活跃筛选条件样式 */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #f0f0f0;
}

.active-filter-tag {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  white-space: nowrap;
}

.advanced-button {
  transition: all 0.3s;
}

@media (max-width: 768px) {
  .filter-basic {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input,
  .category-select,
  .sort-select {
    width: 100%;
  }
  
  .filter-button-group {
    margin-left: 0;
    width: 100%;
    justify-content: space-between;
  }
  
  .price-group {
    width: 100%;
  }
  
  .filter-actions {
    flex-direction: column;
  }
  
  .filter-actions .el-button {
    width: 100%;
  }
}
</style> 
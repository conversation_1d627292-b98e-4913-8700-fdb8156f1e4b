<template>
  <div class="exchange-records-container">
    <div class="records-header">
      <h3 class="title">我的兑换记录</h3>
      <el-button type="primary" @click="refreshRecords" :loading="loading" size="small">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>
    
    <div v-if="loading" class="loading-wrapper">
      <el-skeleton :rows="3" animated />
    </div>
    
    <div v-else-if="records.length === 0" class="empty-state">
      <el-empty description="暂无兑换记录" />
    </div>
    
    <div v-else class="records-list">
      <el-table
        :data="records"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa' }"
      >
        <el-table-column prop="id" label="编号" width="70" />
        
        <el-table-column label="商品" min-width="220">
          <template #default="{ row }">
            <div class="product-info">
              <el-image
                v-if="row.Product?.imageUrl"
                :src="row.Product.imageUrl"
                fit="cover"
                class="product-image"
                :preview-src-list="[row.Product.imageUrl]"
              />
              <span>{{ row.Product?.name || '未知商品' }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="quantity" label="数量" width="70" align="center" />
        
        <el-table-column label="支付方式" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.paymentMethod === 'ly'" type="primary" effect="light" size="small">
              光年币
            </el-tag>
            <el-tag v-else type="success" effect="light" size="small">
              人民币
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="金额" width="120" align="center">
          <template #default="{ row }">
            <span v-if="row.paymentMethod === 'ly'">
              {{ row.quantity * (row.Product?.lyPrice || 0) }} 光年币
            </span>
            <span v-else>
              ¥{{ (row.quantity * (row.Product?.rmbPrice || 0)).toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              effect="light"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="申请时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending'"
              type="danger"
              size="small"
              plain
              @click="handleCancel(row)"
              :loading="cancelLoading === row.id"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              size="small"
              plain
              @click="handleDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="兑换详情"
      width="90%"
      max-width="600px"
      destroy-on-close
    >
      <div v-if="currentRecord" class="record-detail">
        <div class="detail-header">
          <h3>{{ currentRecord.Product?.name }}</h3>
          <el-tag :type="getStatusType(currentRecord.status)" effect="light">
            {{ getStatusText(currentRecord.status) }}
          </el-tag>
        </div>
        
        <div class="detail-item">
          <div class="item-label">兑换编号</div>
          <div class="item-value">{{ currentRecord.id }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">支付方式</div>
          <div class="item-value">
            {{ currentRecord.paymentMethod === 'ly' ? '光年币' : '人民币' }}
          </div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">数量</div>
          <div class="item-value">{{ currentRecord.quantity }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">金额</div>
          <div class="item-value">
            <span v-if="currentRecord.paymentMethod === 'ly'">
              {{ currentRecord.quantity * (currentRecord.Product?.lyPrice || 0) }} 光年币
            </span>
            <span v-else>
              ¥{{ (currentRecord.quantity * (currentRecord.Product?.rmbPrice || 0)).toFixed(2) }}
            </span>
          </div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">联系方式</div>
          <div class="item-value">{{ currentRecord.contactInfo }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">位置</div>
          <div class="item-value">{{ currentRecord.location }}</div>
        </div>
        
        <div class="detail-item">
          <div class="item-label">申请时间</div>
          <div class="item-value">{{ formatDate(currentRecord.createdAt) }}</div>
        </div>
        
        <div class="detail-item" v-if="currentRecord.remarks">
          <div class="item-label">备注</div>
          <div class="item-value">{{ currentRecord.remarks }}</div>
        </div>
        
        <div class="detail-item" v-if="currentRecord.adminRemarks">
          <div class="item-label">管理员备注</div>
          <div class="item-value">{{ currentRecord.adminRemarks }}</div>
        </div>
        
        <div class="detail-item" v-if="currentRecord.trackingNumber">
          <div class="item-label">物流信息</div>
          <div class="item-value">
            {{ currentRecord.trackingCompany || '未知物流公司' }}：{{ currentRecord.trackingNumber }}
          </div>
        </div>
        
        <div class="detail-actions">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentRecord.status === 'pending'"
            type="danger"
            plain
            @click="handleCancelInDetail"
            :loading="cancelLoading === currentRecord.id"
          >
            取消申请
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { getUserExchanges, cancelExchange, getExchangeDetail } from '../api/exchanges';

// 页面状态
const loading = ref(false);
const cancelLoading = ref(null);
const records = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 详情对话框
const detailDialogVisible = ref(false);
const currentRecord = ref(null);

// 初始化加载数据
onMounted(() => {
  fetchRecords();
});

// 获取兑换记录列表
const fetchRecords = async () => {
  loading.value = true;
  
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    };
    
    const response = await getUserExchanges(params);
    records.value = response.data || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取兑换记录失败:', error);
    ElMessage.error('获取兑换记录失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshRecords = () => {
  fetchRecords();
};

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchRecords();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchRecords();
};

// 处理取消兑换申请
const handleCancel = async (record) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消此兑换申请吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    cancelLoading.value = record.id;
    
    await cancelExchange(record.id);
    
    ElMessage.success('兑换申请已取消');
    fetchRecords();
    
    // 如果详情对话框是打开的且显示的是当前记录，则更新状态
    if (detailDialogVisible.value && currentRecord.value?.id === record.id) {
      currentRecord.value.status = 'cancelled';
    }
  } catch (error) {
    if (error !== 'cancel') { // 用户取消操作不提示错误
      console.error('取消兑换申请失败:', error);
      ElMessage.error('取消兑换申请失败，请稍后重试');
    }
  } finally {
    cancelLoading.value = null;
  }
};

// 在详情对话框中取消申请
const handleCancelInDetail = () => {
  handleCancel(currentRecord.value);
};

// 查看详情
const handleDetail = async (record) => {
  try {
    // 获取最新的详细信息
    const response = await getExchangeDetail(record.id);
    currentRecord.value = response;
    detailDialogVisible.value = true;
  } catch (error) {
    console.error('获取兑换详情失败:', error);
    ElMessage.error('获取兑换详情失败，请稍后重试');
  }
};

// 状态映射
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'approved': '已批准',
    'shipped': '已发货',
    'completed': '已完成',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  };
  
  return statusMap[status] || status;
};

// 状态对应的标签类型
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'shipped': 'primary',
    'completed': 'success',
    'rejected': 'danger',
    'cancelled': 'info'
  };
  
  return typeMap[status] || 'info';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style scoped>
.exchange-records-container {
  padding: 20px;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.loading-wrapper {
  padding: 30px;
}

.empty-state {
  padding: 40px 0;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.record-detail {
  padding: 10px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.detail-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
}

.item-label {
  width: 100px;
  color: #606266;
  flex-shrink: 0;
}

.item-value {
  color: #303133;
  flex-grow: 1;
}

.detail-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 15px;
}
</style> 
<template>
  <div class="lazy-image-container" ref="container">
    <img
      :src="loadedSrc"
      :alt="alt"
      :class="['lazy-image', { 'lazy-image-loaded': isLoaded }]"
      @load="handleImageLoaded"
      @error="handleImageError"
    />
    <div v-if="!isLoaded && !error" class="lazy-image-placeholder">
      <el-skeleton :loading="true" animated>
        <template #template>
          <el-skeleton-item variant="image" style="width: 100%; height: 100%" />
        </template>
      </el-skeleton>
    </div>
    <div v-if="error" class="lazy-image-error">
      <el-icon><warning /></el-icon>
      <span>图片加载失败</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Warning } from '@element-plus/icons-vue';
import { observeImage, cleanupImageObserver } from '../utils/imageLoader';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    default: ''
  }
});

const container = ref(null);
const loadedSrc = ref('');
const isLoaded = ref(false);
const error = ref(false);

const handleImageLoaded = () => {
  isLoaded.value = true;
};

const handleImageError = () => {
  error.value = true;
};

const preloadImage = (src) => {
  const img = new Image();
  img.src = src;
  img.onload = () => {
    loadedSrc.value = src;
  };
  img.onerror = handleImageError;
};

onMounted(() => {
  if (container.value) {
    observeImage(container.value, () => {
      preloadImage(props.src);
    });
  }
});

onUnmounted(() => {
  cleanupImageObserver();
});
</script>

<style lang="scss" scoped>
.lazy-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f5f7fa;
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-image-loaded {
  opacity: 1;
}

.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.lazy-image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
  
  .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
}
</style>
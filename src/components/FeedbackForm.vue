<template>
  <div class="feedback-form">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择反馈类型">
          <el-option label="产品问题" value="product" />
          <el-option label="功能建议" value="feature" />
          <el-option label="系统错误" value="bug" />
          <el-option label="其他问题" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入反馈标题" />
      </el-form-item>
      
      <el-form-item label="内容" prop="content">
        <el-input 
          v-model="form.content" 
          type="textarea" 
          :rows="4" 
          placeholder="请详细描述您的问题或建议..."
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm" :loading="loading">提交反馈</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { submitFeedback } from '../api/feedback';

const emit = defineEmits(['submit-success']);

const formRef = ref(null);
const loading = ref(false);

const form = reactive({
  type: '',
  title: '',
  content: ''
});

const rules = {
  type: [{ required: true, message: '请选择反馈类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入反馈标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入反馈内容', trigger: 'blur' }]
};

const submitForm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    await submitFeedback(form);
    
    ElMessage.success('反馈提交成功，感谢您的反馈！');
    resetForm();
    emit('submit-success');
  } catch (error) {
    console.error('提交反馈失败:', error);
    ElMessage.error(error.response?.data?.message || '提交失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
};
</script>

<style scoped>
.feedback-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 
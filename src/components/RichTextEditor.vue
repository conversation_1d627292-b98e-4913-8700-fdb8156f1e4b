<template>
  <div class="rich-text-editor-container" :style="{ height: height }">
    <div class="editor-toolbar-container" v-if="!readOnly">
      <Toolbar class="editor-toolbar" :editor="editorRef" :mode="mode" :default-config="toolbarConfig" />
    </div>
    <div class="editor-content-container" :style="editorStyles">
      <Editor
        v-if="editorRef"
        :model-value="html"
        :default-config="editorConfig"
        :mode="mode"
        class="editor-content"
        @update:model-value="handleChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, shallowRef, onBeforeUnmount, onMounted, watch } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import axios from 'axios';
import { fixImageUrl } from '../utils/imageUtils';

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  mode: {
    type: String,
    default: 'default' // 'default' or 'simple'
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  apiBaseUrl: {
    type: String,
    default: '/api'
  },
  initialContent: {
    type: String,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error']);

// Editor reference
const editorRef = shallowRef();

// HTML content (derived from modelValue)
const html = computed(() => props.modelValue);

// Editor styles
const editorStyles = computed(() => ({
  height: props.readOnly ? '100%' : 'calc(100% - 40px)',
  overflowY: 'hidden',
}));

// 工具栏配置
const toolbarConfig = {};

// 编辑器配置
const editorConfig = {
  placeholder: props.placeholder,
  readOnly: props.readOnly,
  autoFocus: !props.readOnly,
  scroll: true,
  MENU_CONF: {}
};

// 处理内容变化
const handleChange = (editor) => {
  emit('update:modelValue', editor.getHtml());
};

// 处理图片URL格式 - 使用我们的通用工具函数
const processImageUrl = (url) => {
  return fixImageUrl(url);
};

// 图片上传配置 - 重要修复
editorConfig.MENU_CONF['uploadImage'] = {
  server: `${props.apiBaseUrl}/upload/image`,
  fieldName: 'file',
  maxFileSize: 10 * 1024 * 1024,
  allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  headers: {
    Authorization: `Bearer ${localStorage.getItem('token') || ''}`
  },
  
  // 自定义上传函数
  customUpload: async (file, insertFn) => {
    // 文件类型检查
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      console.error('不支持的文件类型');
      emit('upload-error', '不支持的文件类型，只允许 JPG, PNG, GIF, WebP 格式的图片');
      return;
    }
    
    // 文件大小检查 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      console.error('文件过大');
      emit('upload-error', '图片大小不能超过 10MB');
      return;
    }
    
    // 准备表单数据
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      console.log('准备上传图片到:', `${props.apiBaseUrl}/upload/image`);
      const response = await axios.post(`${props.apiBaseUrl}/upload/image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        }
      });
      
      console.log('上传响应:', response.data);
      
      // 处理不同的响应格式
      let imageUrl;
      
      if (response.data.errno === 0 && response.data.data) {
        // 标准格式响应
        imageUrl = processImageUrl(response.data.data.url);
      } else if (response.data.url) {
        // 直接返回URL的格式
        imageUrl = processImageUrl(response.data.url);
      } else {
        throw new Error('未能从响应中获取图片URL');
      }
      
      console.log('处理后的图片URL:', imageUrl);
      emit('upload-success', imageUrl);
      
      // 插入图片到编辑器
      insertFn(imageUrl, '', '');
    } catch (error) {
      console.error('图片上传失败:', error);
      emit('upload-error', error.message || '图片上传失败');
    }
  }
};

// 粘贴图片上传配置 - 重要修复
editorConfig.MENU_CONF['insertImage'] = {
  parseImageSrc: (src) => processImageUrl(src)
};

// 配置粘贴上传
if (editorConfig.MENU_CONF['uploadImage'].customUpload) {
  editorConfig.MENU_CONF['pasteImage'] = {
    server: `${props.apiBaseUrl}/upload/paste-image`,
    fieldName: 'file',
    maxFileSize: 10 * 1024 * 1024,
    allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token') || ''}`
    },
    // 共用相同的上传处理函数
    customUpload: editorConfig.MENU_CONF['uploadImage'].customUpload
  };
}

// 监听父组件传入的modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (editorRef.value && editorRef.value.getHtml() !== newVal) {
    editorRef.value.setHtml(newVal);
  }
});

// 监听readOnly变化
watch(() => props.readOnly, (newVal) => {
  if (editorRef.value) {
    editorRef.value.enable(!newVal);
  }
});

// 生命周期钩子
onMounted(() => {
  if (props.initialContent && !props.modelValue) {
    setTimeout(() => {
      if (editorRef.value) {
        editorRef.value.setHtml(props.initialContent);
        emit('update:modelValue', props.initialContent);
      }
    }, 100);
  }
});

onBeforeUnmount(() => {
  if (editorRef.value) {
    editorRef.value.destroy();
  }
});
</script>

<style>
.rich-text-editor-container {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
}

.editor-toolbar-container {
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.editor-toolbar {
  background-color: #f5f7fa;
}

.editor-content-container {
  flex: 1;
  overflow: auto;
}

.editor-content {
  height: 100%;
  overflow-y: auto;
}

/* 调整编辑器内部样式 */
.w-e-text-container {
  min-height: 100px !important;
}

.w-e-toolbar {
  border-bottom: 1px solid #dcdfe6 !important;
}
</style> 
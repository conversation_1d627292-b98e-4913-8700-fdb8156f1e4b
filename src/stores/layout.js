import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useLayoutStore = defineStore('layout', () => {
  // 常量定义
  const MIN_SIDEBAR_WIDTH = 64;  // 最小宽度 - 收起状态
  const DEFAULT_EXPANDED_WIDTH = 240; // 默认展开宽度
  const COLLAPSE_THRESHOLD = 150; // 自动收起阈值
  const MAX_SIDEBAR_WIDTH = 400; // 最大宽度限制
  
  // 本地存储键名
  const SIDEBAR_WIDTH_KEY = 'admin_sidebar_width';
  
  // 读取用户保存的宽度，如果没有则使用默认值
  const savedWidth = localStorage.getItem(SIDEBAR_WIDTH_KEY);
  
  // 侧边栏宽度状态 
  const sidebarWidth = ref(savedWidth ? parseInt(savedWidth) : DEFAULT_EXPANDED_WIDTH);
  
  // 计算侧边栏是否处于收起状态
  const isSidebarCollapsed = computed(() => {
    return sidebarWidth.value <= MIN_SIDEBAR_WIDTH;
  });

  // 切换侧边栏折叠状态（保留此方法以兼容其他组件）
  const toggleSidebar = () => {
    if (isSidebarCollapsed.value) {
      // 展开
      sidebarWidth.value = localStorage.getItem(SIDEBAR_WIDTH_KEY) 
        ? parseInt(localStorage.getItem(SIDEBAR_WIDTH_KEY)) 
        : DEFAULT_EXPANDED_WIDTH;
    } else {
      // 收起前保存当前宽度
      localStorage.setItem(SIDEBAR_WIDTH_KEY, sidebarWidth.value.toString());
      sidebarWidth.value = MIN_SIDEBAR_WIDTH;
    }
  };

  // 设置侧边栏宽度
  const setSidebarWidth = (width) => {
    // 确保宽度在有效范围内
    let newWidth = Math.max(MIN_SIDEBAR_WIDTH, Math.min(width, MAX_SIDEBAR_WIDTH));
    
    // 更新宽度状态
    sidebarWidth.value = newWidth;
    
    // 如果宽度大于收起阈值，保存当前宽度以便下次使用
    if (newWidth > COLLAPSE_THRESHOLD) {
      localStorage.setItem(SIDEBAR_WIDTH_KEY, newWidth.toString());
    }
  };

  // 响应屏幕尺寸变化
  const handleScreenResize = () => {
    if (window.innerWidth < 768 && sidebarWidth.value > MIN_SIDEBAR_WIDTH) {
      // 在小屏幕上自动收起侧边栏
      sidebarWidth.value = MIN_SIDEBAR_WIDTH;
    }
  };

  return {
    sidebarWidth,
    isSidebarCollapsed,
    toggleSidebar,
    setSidebarWidth,
    handleScreenResize,
    MIN_SIDEBAR_WIDTH,
    COLLAPSE_THRESHOLD
  };
}); 
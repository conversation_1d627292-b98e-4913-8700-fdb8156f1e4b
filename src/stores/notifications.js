import { defineStore } from 'pinia';
import { getNotifications, getUnreadCount, mark<PERSON>Read, markAllAsRead, deleteNotification } from '../api/notifications';

export const useNotificationStore = defineStore('notifications', {
  state: () => ({
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
    lastFetchTime: 0
  }),

  getters: {
    hasUnread: (state) => state.unreadCount > 0
  },

  actions: {
    async fetchNotifications() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await getNotifications();
        this.notifications = response.data || [];
        
        // 更新未读计数，避免额外的API调用
        this.updateUnreadCountFromNotifications();
        
        // 更新最后获取时间
        this.lastFetchTime = Date.now();
        
        return this.notifications;
      } catch (error) {
        this.error = error.message || '获取通知失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    // 根据本地通知数据更新未读计数
    updateUnreadCountFromNotifications() {
      const unreadNotifications = this.notifications.filter(n => !n.isRead);
      this.unreadCount = unreadNotifications.length;
    },
    
    async fetchUnreadCount(forceUpdate = false) {
      try {
        // 如果上次获取时间在30秒内且没有强制更新，则跳过请求
        const now = Date.now();
        const timeSinceLastFetch = now - this.lastFetchTime;
        
        if (!forceUpdate && timeSinceLastFetch < 10000 && this.notifications.length > 0) {
          this.updateUnreadCountFromNotifications();
          return this.unreadCount;
        }
        
        const response = await getUnreadCount();
        this.unreadCount = response.data?.count || 0;
        this.lastFetchTime = now;
        return this.unreadCount;
      } catch (error) {
        console.error('获取未读通知数量失败:', error);
      }
    },
    
    async readNotification(id) {
      try {
        await markAsRead(id);
        
        // 更新本地状态
        const notification = this.notifications.find(n => n.id === id);
        if (notification && !notification.isRead) {
          notification.isRead = true;
          this.unreadCount = Math.max(0, this.unreadCount - 1);
        }
      } catch (error) {
        console.error('标记通知已读失败:', error);
        throw error;
      }
    },
    
    async readAllNotifications() {
      try {
        await markAllAsRead();
        
        // 更新本地状态
        this.notifications.forEach(notification => {
          notification.isRead = true;
        });
        this.unreadCount = 0;
      } catch (error) {
        console.error('标记所有通知已读失败:', error);
        throw error;
      }
    },
    
    async removeNotification(id) {
      try {
        await deleteNotification(id);
        
        // 更新本地状态
        const notification = this.notifications.find(n => n.id === id);
        if (notification && !notification.isRead) {
          this.unreadCount = Math.max(0, this.unreadCount - 1);
        }
        
        this.notifications = this.notifications.filter(n => n.id !== id);
      } catch (error) {
        console.error('删除通知失败:', error);
        throw error;
      }
    }
  }
}); 
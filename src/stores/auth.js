import { defineStore } from 'pinia';
import { login as login<PERSON>pi, getProfile as getProfileApi, feishuLogin as feishuLogin<PERSON><PERSON>, refreshToken as refreshTokenApi } from '../api/auth';
import api from '../api';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    // 只从sessionStorage获取token，确保关闭浏览器后重新打开网页时为未登录状态
    token: sessionStorage.getItem('token') || null,
    loading: false,
    error: null
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    isAdmin: (state) => state.user && state.user.role === 'admin'
  },

  actions: {
    async login(credentials) {
      this.loading = true;
      this.error = null;
      
      try {
        console.log('正在调用登录API:', credentials.username);
        const response = await loginApi(credentials);
        console.log('登录API返回成功:', response);
        
        this.user = response.user;
        this.token = response.token;
        
        // 无论是否记住我，都只将token存储在sessionStorage中
        // 这样确保关闭浏览器后重新打开网页时为未登录状态
        sessionStorage.setItem('token', response.token);
        // 确保清除localStorage中可能存在的token
        localStorage.removeItem('token');
        
        return response;
      } catch (error) {
        console.error('登录API错误:', error);
        
        // 更详细的错误处理
        if (error.message) {
          this.error = error.message;
        } else if (error.response && error.response.data && error.response.data.message) {
          this.error = error.response.data.message;
        } else {
          this.error = '登录失败，服务器未返回错误信息';
        }
        
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async getProfile() {
      if (!this.token) return null;
      
      this.loading = true;
      this.error = null;
      
      try {
        const response = await getProfileApi();
        this.user = response.user;
        return response.user;
      } catch (error) {
        this.error = error.message || '获取用户资料失败';
        // 如果是401错误，清除用户信息和token
        if (error.status === 401) {
          this.logout();
        }
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async feishuLogin(code) {
      this.loading = true;
      this.error = null;
      
      try {
        console.log('正在调用飞书登录API，授权码长度:', code.length);
        const response = await feishuLoginApi({ code });
        console.log('飞书登录API返回成功:', response);
        
        this.user = response.user;
        this.token = response.token;
        
        // 存储令牌（飞书登录默认使用长期令牌）
        sessionStorage.setItem('token', response.token);
        
        return response;
      } catch (error) {
        console.error('飞书登录API错误:', error);
        
        // 更详细的错误处理
        if (error.message) {
          this.error = error.message;
        } else if (error.response && error.response.data && error.response.data.message) {
          this.error = error.response.data.message;
        } else {
          this.error = '飞书登录失败，服务器未返回错误信息';
        }
        
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async logout() {
      // Call the backend logout endpoint first
      try {
        // Check if the token exists before calling the API
        if (this.token) {
          console.log('Calling backend logout API...');
          await api.post('/auth/logout'); 
          console.log('Backend logout successful.');
        }
      } catch (error) {
        // Log the error but proceed with local cleanup
        console.error('Backend logout failed:', error);
        // Optionally notify the user, but don't block logout
        // ElMessage.error('退出请求失败，但本地已登出');
      }
      
      // Always perform local cleanup regardless of API call success
      console.log('Performing local logout cleanup...');
      this.user = null;
      this.token = null;
      
      // 清除所有相关的存储
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      
      // 清除记住我相关的信息
      localStorage.removeItem('remember_login');
      localStorage.removeItem('remember_username');
      localStorage.removeItem('remember_email');
      localStorage.removeItem('remember_password');
      
      // 清除飞书相关信息
      sessionStorage.removeItem('feishu_state');
      
      console.log('Local logout cleanup complete.');
    },
    
    // 刷新用户令牌
    async refreshToken() {
      if (!this.token) return null;
      
      this.loading = true;
      this.error = null;
      
      try {
        console.log('正在刷新令牌...');
        const response = await refreshTokenApi();
        
        // 更新用户信息和令牌
        this.user = response.user;
        this.token = response.token;
        
        // 保存到会话存储
        sessionStorage.setItem('token', response.token);
        
        console.log('令牌刷新成功，用户角色:', this.user.role);
        return response;
      } catch (error) {
        console.error('刷新令牌错误:', error);
        this.error = error.message || '刷新令牌失败';
        
        // 如果是401错误，清除用户信息和token
        if (error.response && error.response.status === 401) {
          this.logout();
        }
        
        throw error;
      } finally {
        this.loading = false;
      }
    }
  }
}); 
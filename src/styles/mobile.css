/**
 * 移动端优化样式
 * 包含移动端组件、交互和布局的优化样式
 */

/* 移动端消息提示优化 */
.mobile-message {
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  max-width: 90vw !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.mobile-message .el-message__content {
  line-height: 1.4 !important;
}

/* 移动端按钮通用样式 */
.mobile-button {
  min-height: 44px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* 触摸反馈效果 */
.touch-feedback {
  transition: transform 0.1s ease !important;
}

.touch-feedback:active {
  transform: scale(0.98) !important;
}

/* 移动端表单元素优化 */
@media (max-width: 768px) {
  /* 输入框优化 */
  .el-input__inner {
    font-size: 16px !important; /* 防止iOS缩放 */
    padding: 12px 16px !important;
    border-radius: 8px !important;
  }
  
  /* 按钮优化 */
  .el-button {
    min-height: 44px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
  }
  
  /* 卡片优化 */
  .el-card {
    border-radius: 12px !important;
    margin: 12px !important;
  }
  
  /* 对话框优化 */
  .el-dialog {
    margin: 20px !important;
    border-radius: 12px !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
  }
  
  .el-dialog__header {
    padding: 20px 20px 10px !important;
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px !important;
  }
  
  /* 表格优化 */
  .el-table {
    font-size: 14px !important;
  }
  
  .el-table th,
  .el-table td {
    padding: 12px 8px !important;
  }
  
  /* 分页优化 */
  .el-pagination {
    text-align: center !important;
  }
  
  .el-pagination .el-pager li {
    min-width: 36px !important;
    height: 36px !important;
    line-height: 36px !important;
  }
}

/* 移动端键盘弹起时的处理 */
.keyboard-open {
  padding-bottom: 0 !important;
}

.keyboard-open .fixed-bottom {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 2000 !important;
}

/* 移动端导航优化 */
@media (max-width: 768px) {
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: #fff;
    border-top: 1px solid #e4e7ed;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    color: #909399;
    font-size: 12px;
    text-decoration: none;
    min-width: 60px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }
  
  .mobile-nav-item.active {
    color: #409eff;
    background-color: #ecf5ff;
  }
  
  .mobile-nav-item:active {
    transform: scale(0.95);
    background-color: #f5f7fa;
  }
  
  .mobile-nav-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }
}

/* 移动端卡片列表优化 */
@media (max-width: 768px) {
  .mobile-card-list {
    padding: 12px;
  }
  
  .mobile-card-item {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;
  }
  
  .mobile-card-item:active {
    transform: scale(0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .mobile-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
  
  .mobile-card-content {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
  }
  
  .mobile-card-footer {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

/* 移动端加载状态优化 */
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.mobile-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: mobile-spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes mobile-spin {
  to {
    transform: rotate(360deg);
  }
}

.mobile-loading-text {
  font-size: 14px;
  text-align: center;
}

/* 移动端空状态优化 */
.mobile-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #909399;
  text-align: center;
}

.mobile-empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.mobile-empty-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.mobile-empty-description {
  font-size: 14px;
  color: #909399;
  line-height: 1.4;
}

/* 移动端安全区域适配 */
@supports (padding: max(0px)) {
  .mobile-safe-area-top {
    padding-top: max(20px, env(safe-area-inset-top));
  }
  
  .mobile-safe-area-bottom {
    padding-bottom: max(20px, env(safe-area-inset-bottom));
  }
  
  .mobile-safe-area-left {
    padding-left: max(20px, env(safe-area-inset-left));
  }
  
  .mobile-safe-area-right {
    padding-right: max(20px, env(safe-area-inset-right));
  }
}

/* 移动端滚动优化 */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* 禁用iOS点击高亮 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* 移动端选择防止缩放 */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="number"],
textarea,
select {
  font-size: 16px !important;
}

/* 移动端横屏适配 */
@media (max-height: 500px) and (orientation: landscape) {
  .mobile-landscape-compact .el-dialog {
    margin: 10px !important;
    max-height: 95vh !important;
  }
  
  .mobile-landscape-compact .el-form-item {
    margin-bottom: 12px !important;
  }
  
  .mobile-landscape-compact .mobile-nav {
    height: 50px !important;
  }
} 
// 全局样式变量 - 现代化配色方案
$primary-color: #2563eb; // 更鲜明的蓝色
$success-color: #10b981; // 更清新的绿色
$warning-color: #f59e0b; // 更温暖的黄色
$danger-color: #ef4444;  // 更鲜艳的红色
$info-color: #6366f1;    // 添加信息色
$text-primary: #111827;  // 更深的主要文本色
$text-regular: #374151;  // 更有层次的常规文本色
$text-secondary: #6b7280;// 更柔和的次要文本色
$border-color: #e5e7eb;  // 更柔和的边框色
$background-color: #f9fafb; // 更明亮的背景色

// 扩展颜色变量
$primary-light: #dbeafe;
$primary-dark: #1d4ed8;
$success-light: #d1fae5;
$success-dark: #059669;
$warning-light: #fef3c7;
$warning-dark: #d97706;
$danger-light: #fee2e2;
$danger-dark: #dc2626;
$info-light: #e0e7ff;
$info-dark: #4f46e5;

// 尺寸变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$border-radius-sm: 6px;   // 增加圆角
$border-radius-md: 12px;  // 增加圆角
$border-radius-lg: 16px;
$border-radius-xl: 24px;
$border-radius-pill: 9999px; // 添加药丸形圆角

// 阴影变量 - 添加更多层次感
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Z-index变量
$z-index-dropdown: 100;
$z-index-sticky: 200;
$z-index-fixed: 300;
$z-index-modal: 400;
$z-index-popover: 500;
$z-index-tooltip: 600;

// 全局过渡动画
:root {
  --el-transition-duration: 0.25s;
  --el-transition-duration-fast: 0.15s;
}

// 通用卡片样式 - 更现代的设计
.card-common {
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  transition: all 0.3s;
  background: white;
  box-shadow: $shadow-sm;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: $shadow-lg;
  }
}

// 现代化标签样式
.tag-common {
  position: absolute;
  top: 10px;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-pill; // 药丸形更现代
  color: white;
  font-size: 12px;
  font-weight: 500;
  z-index: 1;
  box-shadow: $shadow-xs;
}

// 实用工具混合器
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin truncate($lines: 1) {
  @if $lines == 1 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.4, 0, 0.2, 1)) {
  transition: $property $duration $timing;
}

// 响应式布局
@mixin mobile {
  @media screen and (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media screen and (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media screen and (min-width: 1025px) and (max-width: 1440px) {
    @content;
  }
}

@mixin large-desktop {
  @media screen and (min-width: 1441px) {
    @content;
  }
}

// 响应式容器
.responsive-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-md;
  
  @include mobile {
    max-width: 100%;
    padding: 0 $spacing-sm;
  }
  
  @include tablet {
    max-width: 768px;
  }
  
  @include desktop {
    max-width: 1024px;
  }
  
  @include large-desktop {
    max-width: 1200px;
  }
}

// 响应式网格 - 现代化布局
.responsive-grid {
  display: grid;
  gap: $spacing-lg; // 增加间距提高可读性
  
  @include mobile {
    grid-template-columns: repeat(1, 1fr);
    gap: $spacing-md;
  }
  
  @include tablet {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include desktop {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @include large-desktop {
    grid-template-columns: repeat(4, 1fr); // 大屏幕显示4列
  }
}

// 图片容器 - 更现代的效果
.image-container {
  position: relative;
  overflow: hidden;
  border-radius: $border-radius-md;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    @include transition(transform);
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(180deg, rgba(0,0,0,0) 60%, rgba(0,0,0,0.6) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::after {
    opacity: 1;
  }
}

// 加载状态 - 现代化设计
.loading-container {
  @include flex-center;
  min-height: 200px;
  
  .loading-spinner {
    border: 3px solid $primary-light;
    border-top: 3px solid $primary-color;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

// 错误提示 - 更友好的设计
.error-message {
  color: $danger-dark;
  text-align: center;
  padding: $spacing-md;
  margin: $spacing-md 0;
  background-color: $danger-light;
  border-radius: $border-radius-md;
  border-left: 4px solid $danger-color;
  box-shadow: $shadow-sm;
  font-weight: 500;
}

// 按钮样式 - 更现代优雅
.btn-primary {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: $border-radius-md;
  padding: $spacing-sm $spacing-lg;
  font-weight: 500;
  @include transition;
  
  &:hover {
    background-color: $primary-dark;
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }
  
  &:active {
    transform: translateY(0);
  }
}

.btn-secondary {
  background-color: white;
  color: $text-primary;
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  padding: $spacing-sm $spacing-lg;
  font-weight: 500;
  @include transition;
  
  &:hover {
    background-color: $background-color;
    transform: translateY(-2px);
    box-shadow: $shadow-sm;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 文本样式工具类
.text-truncate {
  @include truncate(1);
}

.text-truncate-2 {
  @include truncate(2);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

// 动画效果 - 更平滑
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
  100% {
    transform: scale(1);
  }
}

.fade-enter-active {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-leave-active {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) reverse;
}

.slide-enter-active {
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

// 间距工具类
.mt-1 { margin-top: $spacing-xs; }
.mt-2 { margin-top: $spacing-sm; }
.mt-3 { margin-top: $spacing-md; }
.mt-4 { margin-top: $spacing-lg; }
.mt-5 { margin-top: $spacing-xl; }

.mb-1 { margin-bottom: $spacing-xs; }
.mb-2 { margin-bottom: $spacing-sm; }
.mb-3 { margin-bottom: $spacing-md; }
.mb-4 { margin-bottom: $spacing-lg; }
.mb-5 { margin-bottom: $spacing-xl; }

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.p-1 { padding: $spacing-xs; }
.p-2 { padding: $spacing-sm; }
.p-3 { padding: $spacing-md; }
.p-4 { padding: $spacing-lg; }
.p-5 { padding: $spacing-xl; }

// 表格样式增强
.el-table {
  border-radius: $border-radius-md;
  overflow: hidden;
  box-shadow: $shadow-sm;
  
  .el-table__header-wrapper {
    th {
      background-color: #f8fafc !important;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #f1f5f9 !important;
    }
  }
}

// 表单样式增强
.el-form-item {
  margin-bottom: $spacing-lg;
  
  .el-form-item__label {
    font-weight: 500;
    color: $text-primary;
  }
  
  .el-input__wrapper,
  .el-textarea__wrapper {
    box-shadow: none !important;
    border: 1px solid $border-color;
    border-radius: $border-radius-sm;
    transition: all 0.3s;
    
    &:hover, &:focus, &.is-focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.1) !important;
    }
  }
}

// 卡片样式增强
.el-card {
  border-radius: $border-radius-md;
  border: none;
  box-shadow: $shadow-sm !important;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: $shadow-md !important;
  }
}

// 按钮样式增强
.el-button {
  border-radius: $border-radius-sm;
  font-weight: 500;
}

// 弹窗样式增强
.el-dialog {
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-xl;
  
  .el-dialog__header {
    padding: $spacing-md $spacing-lg;
    margin-right: 0;
    border-bottom: 1px solid $border-color;
  }
  
  .el-dialog__body {
    padding: $spacing-lg;
  }
  
  .el-dialog__footer {
    padding: $spacing-md $spacing-lg;
    border-top: 1px solid $border-color;
  }
}
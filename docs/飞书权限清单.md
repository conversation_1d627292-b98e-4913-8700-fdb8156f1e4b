# 飞书应用权限使用清单

## 概述
本文档详细列出了项目中实际使用的飞书API接口和所需权限，用于权限申请和管理。

## 使用的API接口

### 1. 认证相关接口
| 接口 | 用途 | 权限要求 |
|------|------|----------|
| `authen/v1/index` | 生成授权URL | 无需特殊权限 |
| `authen/v1/access_token` | 获取用户访问令牌 | 无需特殊权限 |
| `authen/v1/user_info` | 获取基础用户信息 | 需要用户授权 |
| `auth/v3/tenant_access_token/internal` | 获取企业访问令牌 | 应用凭证 |

### 2. 通讯录相关接口
| 接口 | 用途 | 权限要求 |
|------|------|----------|
| `contact/v3/users/{user_id}` | 获取用户详细信息 | contact:user.* 权限 |
| `contact/v3/departments/{department_id}` | 获取部门信息 | contact:department.* 权限 |
| `contact/v3/departments/search` | 搜索部门（备用方案） | contact:department.* 权限 |

## 权限分类

### 🔴 核心权限（必需）

#### 1. `contact:user.base:readonly`
- **用途**: 获取用户基本信息（姓名、头像等）
- **使用位置**: `getUserInfo()`, `getUserDetailInfo()`
- **获取字段**: `name`, `avatar_url`, `open_id`, `union_id`

#### 2. `contact:user.email:readonly`
- **用途**: 获取用户邮箱信息
- **使用位置**: `getUserDetailInfo()`
- **获取字段**: `email`, `enterprise_email`

#### 3. `contact:user.department:readonly`
- **用途**: 获取用户组织架构信息
- **使用位置**: `getUserDetailInfo()`, `getDepartmentInfo()`
- **获取字段**: `department_ids`, `leader_user_id`

#### 4. `contact:user.employee:readonly`
- **用途**: 获取用户受雇信息
- **使用位置**: `getUserDetailInfo()`
- **获取字段**: `employee_no`, `job_title`, `city`, `work_station`

#### 5. `contact:department.base:readonly`
- **用途**: 获取部门基础信息
- **使用位置**: `getDepartmentName()`, `getDepartmentInfo()`
- **获取字段**: `name`, `department_id`

### 🟡 推荐权限（建议申请）

#### 6. `contact:user.phone:readonly`
- **用途**: 获取用户手机号
- **状态**: ⚠️ **当前缺失**，导致无法获取手机号
- **影响**: 用户信息中手机号字段为空
- **获取字段**: `mobile`

#### 7. `contact:department.organize:readonly`
- **用途**: 获取部门组织架构信息
- **使用位置**: `getDepartmentPath()`（构建完整部门路径时使用）
- **获取字段**: `parent_department_id`, `member_count`

### ⚪ 不需要的权限

#### `contact:user.employee_id:readonly`
- **说明**: 获取用户employee_id
- **状态**: 项目中未使用此字段，可以移除

## 权限申请建议

### 立即申请
```
contact:user.phone:readonly
```
**原因**: 当前无法获取用户手机号，影响用户信息完整性

### 可选申请
```
contact:department.organize:readonly
```
**原因**: 可以构建更完整的部门路径，提升用户体验

### 移除权限
```
contact:user.employee_id:readonly
```
**原因**: 代码中未使用，减少权限申请复杂度

## 代码中的权限检查

### 当前实现
代码中已经实现了权限缺失的检测和友好提示：

```javascript
// 手机号权限检查
if (!user.mobile && user.mobile_visible) {
  console.log('飞书服务: 警告 - mobile_visible为true但没有mobile字段，可能权限不足');
}

// 手机号详细检查
if (!user.mobile) {
  console.log('飞书服务: 警告 - 用户详细信息中没有手机号，这可能是因为:');
  console.log('1. 该用户在飞书中未设置手机号');
  console.log('2. 应用权限不足，需要申请contact:user.phone:readonly权限');
  console.log('3. 用户隐私设置不允许显示手机号');
}
```

## 权限申请流程

1. **登录飞书开放平台**: https://open.feishu.cn/
2. **找到应用**: App ID `cli_a66b3b2dcab8d013`
3. **权限管理**: 进入应用的权限管理页面
4. **申请权限**: 按照上述建议申请相应权限
5. **提交审核**: 提交版本发布申请
6. **等待审核**: 通常需要几个工作日

## 测试验证

权限申请成功后，可以通过以下方式验证：

1. **查看日志**: 观察飞书登录时的详细日志
2. **检查字段**: 确认API返回的用户信息包含所需字段
3. **功能测试**: 测试用户注册和信息更新功能

## 注意事项

1. **权限最小化原则**: 只申请实际需要的权限
2. **权限审核**: 飞书权限申请需要审核，请提前规划
3. **向下兼容**: 代码已处理权限缺失情况，不会影响基本功能
4. **监控告警**: 建议设置权限相关的监控告警 
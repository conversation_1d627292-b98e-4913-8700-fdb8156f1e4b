#!/bin/bash

echo "=== 快速修复nginx语法错误 ==="

# 1. 上传修复后的配置文件
echo "1. 上传修复后的nginx配置..."
scp complete-nginx-fix.conf root@**************:/tmp/nginx-fixed.conf

# 2. 在服务器上应用修复
echo "2. 应用修复后的配置..."
ssh root@************** '
echo "📋 备份当前配置..."
cp /www/server/panel/vhost/nginx/**************.conf /www/server/panel/vhost/nginx/**************.conf.backup.$(date +%Y%m%d_%H%M%S)

echo "🔄 应用修复后的配置..."
cp /tmp/nginx-fixed.conf /www/server/panel/vhost/nginx/**************.conf

echo "🔧 测试nginx配置语法..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx配置语法正确"
    
    echo "🔄 重新加载nginx..."
    nginx -s reload
    echo "✅ Nginx已重新加载"
    
    echo "🧪 快速测试..."
    curl -I http://127.0.0.1/ 2>/dev/null | head -1
    
else
    echo "❌ Nginx配置仍有问题！"
    echo "显示错误详情："
    nginx -t
    exit 1
fi
'

echo ""
echo "=== 语法修复完成 ==="
echo "✅ 主要修复内容："
echo "  • 修复了server块的语法错误"
echo "  • 确保nginx可以正常启动"
echo ""
echo "📝 请重新测试网站访问" 
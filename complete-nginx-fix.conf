server {
    listen 80;
    server_name **************;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/workyy/dist;
    
    #CERT-APPLY-CHECK--START
    # 用于SSL证书申请时的文件验证相关配置 -- 请勿删除
    include /www/server/panel/vhost/nginx/well-known/**************.conf;
    #CERT-APPLY-CHECK--END

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #SSL-END

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-81.conf;
    #PHP-INFO-END

    #REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
    include /www/server/panel/vhost/rewrite/**************.conf;
    #REWRITE-END

    # ===== 关键配置开始 =====
    
    # API代理配置 - 将/api请求代理到后端服务
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # 文件上传相关配置
        client_max_body_size 100M;
        proxy_request_buffering off;
    }

    # 静态文件服务 - 处理上传的图片和文件
    location /uploads/ {
        alias /www/wwwroot/workyy/server/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        
        # 允许所有图片和文档文件
        location ~* \.(jpg|jpeg|png|gif|bmp|webp|svg|ico|pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar)$ {
            expires 30d;
            add_header Cache-Control "public, immutable";
        }
        
        # 禁止执行脚本文件
        location ~* \.(php|php5|php7|phtml|php3|asp|aspx|jsp|pl|py|cgi|sh)$ {
            deny all;
        }
    }

    # SPA前端路由处理 - 将所有不存在的文件请求重定向到index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # ===== 关键配置结束 =====

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    # 静态资源缓存配置
    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    
    access_log  /www/wwwlogs/**************.log;
    error_log  /www/wwwlogs/**************.error.log;
} 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知开关功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 通知开关功能测试</h1>
        
        <div class="status info">
            <strong>测试说明：</strong>
            <p>此页面用于测试通知开关功能的前端逻辑，模拟真实的开关切换操作。</p>
        </div>

        <div>
            <button onclick="loadConfigs()">加载配置</button>
            <button onclick="testAllSwitches()">测试所有开关</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="configList"></div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let configs = [];
        let testing = false;

        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // 模拟通知配置数据
        function loadConfigs() {
            log('📋 加载通知配置...');
            
            configs = [
                { notificationType: 'exchange_notification', typeName: '兑换申请', enabled: true },
                { notificationType: 'stock_alert', typeName: '库存告警', enabled: true },
                { notificationType: 'new_user_welcome', typeName: '新用户欢迎', enabled: false },
                { notificationType: 'new_product_notification', typeName: '新品上架', enabled: true },
                { notificationType: 'order_alert', typeName: '订单预警', enabled: false },
                { notificationType: 'maintenance_notification', typeName: '维护通知', enabled: false },
                { notificationType: 'error_alert', typeName: '错误告警', enabled: false }
            ];

            renderConfigs();
            log('✅ 配置加载完成，共 ' + configs.length + ' 个配置项');
            showStatus('配置加载成功', 'success');
        }

        function renderConfigs() {
            const listDiv = document.getElementById('configList');
            listDiv.innerHTML = '<h3>通知配置列表</h3>';
            
            configs.forEach(config => {
                const item = document.createElement('div');
                item.className = 'test-item';
                item.innerHTML = `
                    <div>
                        <strong>${config.typeName}</strong>
                        <br><small>${config.notificationType}</small>
                    </div>
                    <label class="switch">
                        <input type="checkbox" ${config.enabled ? 'checked' : ''} 
                               onchange="toggleConfig('${config.notificationType}', this.checked)">
                        <span class="slider"></span>
                    </label>
                `;
                listDiv.appendChild(item);
            });
        }

        // 模拟开关切换
        async function toggleConfig(notificationType, enabled) {
            if (testing) {
                log('⚠️ 测试进行中，跳过手动操作');
                return;
            }

            log(`🔧 切换配置: ${notificationType} -> ${enabled ? '启用' : '禁用'}`);
            
            try {
                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 模拟成功/失败（90%成功率）
                const success = Math.random() > 0.1;
                
                if (success) {
                    // 更新本地状态
                    const config = configs.find(c => c.notificationType === notificationType);
                    if (config) {
                        config.enabled = enabled;
                    }
                    
                    log(`✅ 配置更新成功: ${notificationType}`);
                    showStatus(`${notificationType} ${enabled ? '已启用' : '已禁用'}`, 'success');
                } else {
                    // 模拟失败，回滚开关状态
                    const checkbox = document.querySelector(`input[onchange*="${notificationType}"]`);
                    if (checkbox) {
                        checkbox.checked = !enabled;
                    }
                    
                    log(`❌ 配置更新失败: ${notificationType} - 模拟网络错误`);
                    showStatus('更新配置失败: 网络错误', 'error');
                }
            } catch (error) {
                log(`💥 配置更新异常: ${notificationType} - ${error.message}`);
                showStatus('更新配置失败: ' + error.message, 'error');
            }
        }

        // 测试所有开关
        async function testAllSwitches() {
            if (testing) {
                log('⚠️ 测试已在进行中');
                return;
            }

            testing = true;
            log('🚀 开始自动测试所有开关...');
            showStatus('开始自动测试...', 'info');

            let successCount = 0;
            let totalTests = 0;

            for (const config of configs) {
                // 测试禁用
                totalTests++;
                log(`🔧 测试禁用: ${config.typeName}`);
                const disableSuccess = await simulateToggle(config.notificationType, false);
                if (disableSuccess) successCount++;

                await new Promise(resolve => setTimeout(resolve, 1000));

                // 测试启用
                totalTests++;
                log(`🔧 测试启用: ${config.typeName}`);
                const enableSuccess = await simulateToggle(config.notificationType, true);
                if (enableSuccess) successCount++;

                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            testing = false;
            
            log(`📊 测试完成 - 成功: ${successCount}/${totalTests} (${((successCount/totalTests)*100).toFixed(1)}%)`);
            
            if (successCount === totalTests) {
                showStatus('🎉 所有测试通过！', 'success');
            } else {
                showStatus(`⚠️ 部分测试失败 (${totalTests - successCount}/${totalTests})`, 'error');
            }
        }

        async function simulateToggle(notificationType, enabled) {
            try {
                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 300));
                
                // 90%成功率
                const success = Math.random() > 0.1;
                
                if (success) {
                    // 更新UI
                    const checkbox = document.querySelector(`input[onchange*="${notificationType}"]`);
                    if (checkbox) {
                        checkbox.checked = enabled;
                    }
                    
                    // 更新本地状态
                    const config = configs.find(c => c.notificationType === notificationType);
                    if (config) {
                        config.enabled = enabled;
                    }
                    
                    log(`  ✅ ${notificationType} ${enabled ? '启用' : '禁用'}成功`);
                    return true;
                } else {
                    log(`  ❌ ${notificationType} ${enabled ? '启用' : '禁用'}失败`);
                    return false;
                }
            } catch (error) {
                log(`  💥 ${notificationType} 测试异常: ${error.message}`);
                return false;
            }
        }

        // 页面加载时自动加载配置
        window.onload = function() {
            log('🌟 通知开关功能测试页面已加载');
            loadConfigs();
        };
    </script>
</body>
</html>

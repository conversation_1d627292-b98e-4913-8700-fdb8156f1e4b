#!/bin/bash

echo "=== 生产环境上传文件诊断 ==="

ssh root@************** '
echo "🔍 === 诊断开始 ==="

echo ""
echo "1. 检查Nginx配置文件..."
echo "📄 当前nginx配置文件："
if grep -A 20 "location /uploads/" /www/server/panel/vhost/nginx/**************.conf; then
    echo "✅ 找到uploads配置"
else
    echo "❌ 未找到uploads配置！"
fi

echo ""
echo "2. 检查uploads目录结构..."
echo "📁 /www/wwwroot/server/uploads/ 目录："
if [ -d "/www/wwwroot/server/uploads" ]; then
    ls -la /www/wwwroot/server/uploads/
    echo ""
    echo "📁 images子目录："
    if [ -d "/www/wwwroot/server/uploads/images" ]; then
        ls -la /www/wwwroot/server/uploads/images/
        echo "图片文件数量: $(find /www/wwwroot/server/uploads/images/ -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | wc -l)"
    else
        echo "❌ images目录不存在"
    fi
else
    echo "❌ uploads目录不存在"
fi

echo ""
echo "3. 检查目录权限..."
stat /www/wwwroot/server/uploads/ 2>/dev/null || echo "❌ uploads目录不存在"
if [ -d "/www/wwwroot/server/uploads/images" ]; then
    stat /www/wwwroot/server/uploads/images/
fi

echo ""
echo "4. 测试Nginx配置语法..."
nginx -t

echo ""
echo "5. 检查Nginx进程状态..."
ps aux | grep nginx | grep -v grep

echo ""
echo "6. 创建测试文件并测试访问..."
cd /www/wwwroot/server
mkdir -p uploads/images
echo "test image content" > uploads/test.png
echo "test content" > uploads/images/test.png

echo "📋 测试文件创建完成"
ls -la uploads/test.png uploads/images/test.png

echo ""
echo "7. 测试HTTP访问..."
echo "🌐 测试 /uploads/test.png："
curl -I http://127.0.0.1/uploads/test.png 2>/dev/null | head -3

echo "🌐 测试 /uploads/images/test.png："
curl -I http://127.0.0.1/uploads/images/test.png 2>/dev/null | head -3

echo ""
echo "8. 检查nginx错误日志..."
echo "📋 最近的nginx错误日志："
tail -10 /www/wwwlogs/**************.error.log 2>/dev/null || echo "无法读取错误日志"

echo ""
echo "9. 检查特定图片文件..."
echo "🔍 查找9253359开头的文件："
find /www/wwwroot/server/uploads/ -name "*9253359*" -type f 2>/dev/null || echo "未找到此文件"

echo "🔍 查找58e2202开头的文件："
find /www/wwwroot/server/uploads/ -name "*58e2202*" -type f 2>/dev/null || echo "未找到此文件"

echo ""
echo "10. 检查nginx配置重载状态..."
nginx -s reload && echo "✅ nginx重载成功" || echo "❌ nginx重载失败"

echo ""
echo "🧹 清理测试文件..."
rm -f uploads/test.png uploads/images/test.png

echo ""
echo "=== 诊断完成 ==="
' 
// 导入模型
const db = require('./models');
const { Announcement } = db;

async function checkDatabase() {
  try {
    console.log('正在连接数据库...');
    // 测试数据库连接
    await db.sequelize.authenticate();
    console.log('数据库连接成功');

    // 获取所有公告
    const announcements = await Announcement.findAll({ raw: true });
    console.log(`\n找到 ${announcements.length} 条公告：`);
    
    // 显示每条公告的关键信息
    announcements.forEach(a => {
      console.log(`\nID: ${a.id}`);
      console.log(`标题: ${a.title}`);
      console.log(`类型: ${a.type}`);
      console.log(`状态: ${a.status}`);
      console.log(`图片URL: ${a.imageUrl || '无'}`);
      console.log(`内容长度: ${a.content ? a.content.length : 0}字符`);
      console.log(`创建时间: ${a.createdAt}`);
    });

    // 检查数据库结构
    console.log('\n检查公告表结构:');
    const tableInfo = await db.sequelize.getQueryInterface().describeTable('announcements');
    Object.keys(tableInfo).forEach(field => {
      console.log(`${field}: ${tableInfo[field].type} (${tableInfo[field].allowNull ? '可为空' : '不可为空'})`);
    });
    
  } catch (error) {
    console.error('数据库检查失败:', error);
  } finally {
    // 关闭数据库连接
    await db.sequelize.close();
  }
}

// 执行检查
checkDatabase(); 
#!/bin/bash

echo "=== 修复uploads路径配置问题 ==="

# 1. 上传修正后的配置文件
echo "1. 上传修正路径后的nginx配置..."
scp complete-nginx-fix.conf root@**************:/tmp/nginx-path-fixed.conf

# 2. 在服务器上应用修复
echo "2. 应用修正后的配置..."
ssh root@************** '
echo "📋 备份当前配置..."
cp /www/server/panel/vhost/nginx/**************.conf /www/server/panel/vhost/nginx/**************.conf.backup.$(date +%Y%m%d_%H%M%S)

echo "🔄 应用修正后的配置..."
cp /tmp/nginx-path-fixed.conf /www/server/panel/vhost/nginx/**************.conf

echo "📁 确认uploads目录存在..."
ls -la /www/wwwroot/workyy/server/uploads/
echo "📁 确认images目录存在..."
ls -la /www/wwwroot/workyy/server/uploads/images/ | head -5

echo "🔧 测试nginx配置..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx配置语法正确"
    
    echo "🔄 重新加载nginx..."
    nginx -s reload
    echo "✅ Nginx已重新加载"
    
    echo "🧪 测试uploads路径访问..."
    # 创建测试文件
    echo "test image content" > /www/wwwroot/workyy/server/uploads/test.txt
    chmod 644 /www/wwwroot/workyy/server/uploads/test.txt
    
    # 测试访问
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1/uploads/test.txt)
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ uploads路径访问正常 (HTTP $HTTP_STATUS)"
    else
        echo "❌ uploads路径访问异常 (HTTP $HTTP_STATUS)"
    fi
    
    # 测试实际图片文件
    FIRST_IMAGE=$(find /www/wwwroot/workyy/server/uploads/images/ -name "*.png" | head -1)
    if [ -n "$FIRST_IMAGE" ]; then
        IMAGE_NAME=$(basename "$FIRST_IMAGE")
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://127.0.0.1/uploads/images/$IMAGE_NAME")
        echo "🖼️ 测试实际图片访问: $IMAGE_NAME (HTTP $HTTP_STATUS)"
    fi
    
    # 清理测试文件
    rm -f /www/wwwroot/workyy/server/uploads/test.txt
    
else
    echo "❌ Nginx配置测试失败！"
    nginx -t
    exit 1
fi
'

echo ""
echo "=== 路径修复完成 ==="
echo "✅ 主要修复内容："
echo "  📁 修正uploads路径：/www/wwwroot/workyy/server/uploads/"
echo "  🔧 确保nginx指向正确的目录"
echo ""
echo "🔗 现在图片应该可以正常显示："
echo "  http://**************/uploads/images/xxx.png"
echo ""
echo "�� 请重新测试商品管理中的图片显示" 
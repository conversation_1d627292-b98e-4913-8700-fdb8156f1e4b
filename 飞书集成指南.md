# 飞书登录集成指南

本文档详细说明如何配置飞书OAuth登录功能并集成到系统中，包括飞书应用创建、权限申请、回调设置和环境变量配置等内容。

## 一、创建飞书应用

### 1.1 登录飞书开放平台

访问 [飞书开放平台](https://open.feishu.cn/) 并使用管理员账号登录。

### 1.2 创建企业自建应用

1. 点击右上角"开发者后台"
2. 选择"企业自建应用" → "创建企业自建应用"
3. 填写应用名称、描述、图标等基本信息
4. 点击"确定创建"完成应用创建

### 1.3 获取应用凭证

1. 在应用详情页面，点击左侧菜单的"凭证与基础信息" 
2. 记录"App ID"和"App Secret"，这些将用于系统集成配置

## 二、配置应用权限

### 2.1 申请通讯录权限

在左侧菜单选择"权限管理"，搜索并申请以下权限：

#### 必须权限：
- **获取部门基础信息**: `contact:department.base:readonly`
- **获取部门组织架构信息**: `contact:department.organize:readonly`
- **获取用户基本信息**: `contact:user.base:readonly`
- **获取用户组织架构信息**: `contact:user.department:readonly`
- **获取用户 user ID**: `contact:user.employee_id:readonly`
- **获取用户位置信息**: `contact:user.location:readonly`

#### 推荐权限：
- **获取用户邮箱信息**: `contact:user.email:readonly`
- **获取用户手机号**: `contact:user.phone:readonly`（需审核）
- **读取通讯录**: `contact:contact:readonly`
- **读取部门基本信息**: `contact:contact.department.base:readonly`
- **读取部门组织架构信息**: `contact:contact.department.info:readonly`

> **注意**：如果部门名称无法显示，请确保已申请并获得 `contact:contact.department.base:readonly` 权限。此权限对于获取完整的部门名称是必要的。

### 2.2 配置访问范围
在"权限管理"→"可访问的数据范围"中，配置应用可访问的部门/成员范围，建议设置为"全部部门和成员"。

#### 重要提示：解决部门获取问题
如果用户登录后无法正确显示部门信息，通常是因为应用没有访问该部门的权限。请按照以下步骤设置：

1. 登录 [飞书开放平台](https://open.feishu.cn/)
2. 进入应用详情页面
3. 点击左侧菜单的"权限管理"
4. 选择"可访问的数据范围"选项卡
5. 将访问范围设置为"全部部门和成员"
   - 或者，如果出于安全考虑需要限制范围，确保至少添加了用户所在的部门及其上级部门
6. 保存设置后，点击右上角"发布版本"，等待管理员审核通过

注意：即使已经获得了部门相关API权限，如果没有正确设置"可访问的数据范围"，仍然会出现"no dept authority error"错误。

### 2.3 提交版本发布申请

重要！申请权限后，须提交版本发布申请并由管理员审核通过：

1. 点击右上角"版本管理与发布"
2. 点击"创建版本"，填写版本信息
3. 点击"申请线上发布"
4. 等待管理员在飞书管理后台审批通过

## 三、配置重定向URL及安全设置

### 3.1 设置重定向URL

1. 在左侧菜单点击"应用功能" → "网页"
2. 填写"重定向URL"，格式为：`https://您的域名/api/feishu/callback`
   - 开发环境可使用: `http://localhost:3000/api/feishu/callback`
   - 生产环境务必使用HTTPS协议

### 3.2 配置应用主页

1. 在"应用功能" → "网页"中设置"网页应用主页"URL
2. 填写系统的登录页面地址，如：`https://您的域名/login`

### 3.3 配置IP白名单（可选）

如需限制API请求来源，可在"安全设置" → "IP白名单"中添加服务器IP地址。

## 四、系统集成配置

### 4.1 配置环境变量

在系统的`.env`文件中添加以下配置：

```
# 飞书应用配置
FEISHU_APP_ID=cli_xxxxxxxxxxxx        # 替换为您的App ID
FEISHU_APP_SECRET=xxxxxxxxxxxx        # 替换为您的App Secret
FEISHU_REDIRECT_URI=https://您的域名/api/feishu/callback
```

### 4.2 验证配置

1. 启动系统后，尝试使用飞书登录
2. 查看后台日志，确认是否正常获取用户信息和部门数据
3. 如有报错，检查权限是否已审核通过，以及系统日志中的详细错误信息

## 五、常见问题排查

### 5.1 无法获取用户部门信息

可能原因：
- 部门权限未申请或未审核通过
- API调用参数错误

解决方案：
- 确认`contact:department.base:readonly`和`contact:department.organize:readonly`权限已开通
- 确认应用版本已发布并审核通过
- 检查日志中的详细错误信息

### 5.2 授权失败

可能原因：
- 重定向URL配置错误
- 应用ID或Secret配置错误

解决方案：
- 检查重定向URL是否与飞书平台配置一致
- 验证环境变量中的应用ID和Secret是否正确

### 5.3 权限申请被拒绝

可能原因：
- 权限申请理由不充分
- 应用信息不完整

解决方案：
- 重新申请权限，详细说明应用用途和权限需求
- 完善应用的基本信息、隐私协议等

## 六、测试与验证

### 6.1 测试登录流程

1. 访问系统登录页面
2. 点击"飞书登录"按钮
3. 跳转到飞书授权页面，确认授权
4. 验证是否成功返回系统并登录
5. 检查用户信息是否包含正确的部门数据

### 6.2 部门信息验证

登录成功后，检查用户资料中的部门信息：
1. 部门名称是否正确
2. 部门层级路径是否完整（如"公司/技术部/后端组"）

## 七、更新与维护

### 7.1 权限变更

如需添加新权限，需重新提交版本发布申请并等待审核通过。

### 7.2 应用凭证轮换

出于安全考虑，可定期更新应用Secret：
1. 在飞书开放平台重置Secret
2. 更新系统环境变量中的Secret
3. 重启应用使新配置生效

## 八、特别说明：关于城市/工作地点获取

系统新增了直接从飞书获取用户城市/工作地点的功能。这个功能依赖于飞书API中的城市(city)字段，有以下几点需要注意：

1. 必须申请`contact:user.location:readonly`权限
2. 用户在飞书中必须设置了城市信息才能获取
3. 系统会优先使用飞书提供的城市信息作为用户的工作地点
4. 如果飞书中未设置城市信息，系统将回退到根据部门名称推断工作地点的方式

如果用户工作地点显示不正确，请检查：
- 飞书应用是否已获得`contact:user.location:readonly`权限
- 用户在飞书通讯录中是否设置了正确的城市信息
- 应用的"可访问的数据范围"是否包含该用户

如遇到其他问题，请参考[飞书开放平台文档](https://open.feishu.cn/document/home/<USER>
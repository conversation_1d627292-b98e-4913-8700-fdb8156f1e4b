{"name": "exchange-mall", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@tiptap/core": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.8.4", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "echarts": "^5.6.0", "element-plus": "^2.3.8", "exceljs": "^4.4.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.0", "node-cron": "^4.1.0", "node-schedule": "^2.1.1", "nodejieba": "^2.5.2", "pinia": "^3.0.2", "pinyin": "^4.0.0-alpha.2", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-echarts": "6.6.8", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "sass": "^1.63.6", "sequelize-cli": "^6.6.2", "vite": "^4.4.0"}}
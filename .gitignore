# 依赖目录
node_modules
server/node_modules
/dist

# 本地环境文件
.env.local
.env.*.local

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统生成的文件
.DS_Store
Thumbs.db

# 构建临时文件
.temp
.cache

# 测试覆盖率报告
/coverage

# 本地证书文件
*.pem
*.key
*.crt

# 数据库文件
*.sqlite
*.db

# 备份文件
*.bak
*.backup
*~

# Vue-cli 生成的文件
.browserslistrc
.postcssrc

# 私有配置
config.private.js 
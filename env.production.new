# 光年小卖部 - 后端生产环境配置

# 服务器配置
PORT=3000
NODE_ENV=production
SERVER_URL=http://**************:3000

# 数据库配置
DB_NAME=feishu_mall
DB_USER=root
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=3306

# JWT配置
JWT_SECRET=JO/Ssvef59AR5zFMx5m/MGMin34aMPT0KY6sIcqwowA=
JWT_EXPIRES_IN=1d
JWT_LONG_EXPIRES_IN=30d

# 上传目录配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# CORS配置
CORS_ORIGIN=http://**************,http://**************:3000,http://**************:80,http://**************:5173

# 飞书应用配置
FEISHU_APP_ID=cli_a7f521a6fe32501c
FEISHU_APP_SECRET=s8DAHMcDcCSd9329wAQ0LgsXUmUCZjVZ
FEISHU_REDIRECT_URI=http://**************:3000/api/feishu/callback 
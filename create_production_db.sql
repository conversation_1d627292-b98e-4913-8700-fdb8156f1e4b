-- 生产环境数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS feishu_mall 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE feishu_mall;

-- 创建用户表（如果不存在）
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    mobile VARCHAR(20),
    authType VARCHAR(50) DEFAULT 'local',
    authId VARCHAR(255),
    avatarUrl TEXT,
    isActive BOOLEAN DEFAULT TRUE,
    lastLoginAt DATETIME,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_auth (authType, authId),
    INDEX idx_mobile (mobile),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建商品表（如果不存在）
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    originalPrice DECIMAL(10,2),
    stock INT DEFAULT 0,
    imageUrl VARCHAR(500),
    category VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    sellerId INT,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_seller (sellerId),
    FOREIGN KEY (sellerId) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建订单表（如果不存在）
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    orderNumber VARCHAR(50) UNIQUE NOT NULL,
    buyerId INT NOT NULL,
    sellerId INT NOT NULL,
    productId INT NOT NULL,
    quantity INT NOT NULL,
    totalPrice DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_buyer (buyerId),
    INDEX idx_seller (sellerId),
    INDEX idx_product (productId),
    INDEX idx_status (status),
    INDEX idx_order_number (orderNumber),
    FOREIGN KEY (buyerId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sellerId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些示例数据（仅在表为空时）
INSERT IGNORE INTO users (username, email, authType, authId, isActive) VALUES 
('admin', '<EMAIL>', 'local', 'admin', TRUE);

-- 显示创建结果
SHOW TABLES; 